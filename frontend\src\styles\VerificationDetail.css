.verification-detail {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.detail-header h2 {
  margin: 0;
  color: #333;
  font-size: 24px;
}

.btn-back {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  color: #333;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.3s;
}

.btn-back i {
  margin-right: 8px;
}

.btn-back:hover {
  background-color: #e0e0e0;
}

.detail-section {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 20px;
}

.section-title {
  font-size: 18px;
  color: #333;
  margin-top: 0;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  display: inline-block;
  width: 4px;
  height: 18px;
  background-color: #4caf50;
  margin-right: 10px;
  border-radius: 2px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.detail-item {
  display: flex;
  flex-direction: column;
}

.detail-item-full {
  grid-column: 1 / -1;
}

.detail-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.detail-value {
  font-size: 16px;
  color: #333;
}

.detail-comments {
  background-color: #f9f9f9;
  padding: 10px;
  border-radius: 4px;
  border-left: 3px solid #4caf50;
  white-space: pre-line;
}

.conclusion-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  color: white;
  font-size: 14px;
}

.emission-actions {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

.btn-view-emission {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  color: #333;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-view-emission:hover {
  background-color: #e0e0e0;
}

.detail-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.btn-secondary {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  color: #333;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-secondary:hover {
  background-color: #e0e0e0;
}

.btn-primary {
  background-color: #4caf50;
  border: none;
  color: white;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-primary:hover {
  background-color: #45a049;
}

/* 加载和错误状态 */
.verification-detail-loading,
.verification-detail-error,
.verification-detail-not-found {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 20px;
  text-align: center;
}

.verification-detail-loading i,
.verification-detail-error i,
.verification-detail-not-found i {
  font-size: 48px;
  margin-bottom: 20px;
  color: #757575;
}

.verification-detail-error i {
  color: #f44336;
}

.verification-detail-not-found i {
  color: #9e9e9e;
}

.verification-detail-loading span,
.verification-detail-error span,
.verification-detail-not-found span {
  font-size: 18px;
  margin-bottom: 20px;
  color: #333;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-grid {
    grid-template-columns: 1fr;
  }
  
  .detail-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .detail-header h2 {
    margin-bottom: 10px;
  }
  
  .detail-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .detail-actions button {
    width: 100%;
    margin-bottom: 10px;
  }
}
