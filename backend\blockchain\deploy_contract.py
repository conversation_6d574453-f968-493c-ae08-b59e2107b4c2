"""
智能合约部署脚本
用于将CarbonEmission.sol合约部署到Ganache本地区块链
"""

import os
import json
from web3 import Web3
from web3.middleware import geth_poa_middleware
from eth_account import Account
from dotenv import load_dotenv, set_key
from flask import current_app

# 合约ABI和字节码
CONTRACT_ABI_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'blockchain/contracts/artifacts/CarbonEmission_metadata.json')
CONTRACT_BIN_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'blockchain/contracts/artifacts/CarbonEmission.bin')

def deploy_contract(ethereum_node_url=None, admin_private_key=None):
    """部署智能合约"""
    # 加载环境变量
    load_dotenv()
    
    # 获取环境变量
    if not ethereum_node_url:
        ethereum_node_url = os.getenv('ETHEREUM_NODE_URL', 'http://127.0.0.1:8545')
    
    if not admin_private_key:
        admin_private_key = os.getenv('ADMIN_PRIVATE_KEY')
    
    print(f"连接到以太坊节点: {ethereum_node_url}")
    
    # 连接到以太坊节点
    w3 = Web3(Web3.HTTPProvider(ethereum_node_url))
    
    # 如果使用的是PoA共识的网络，需要添加这个中间件
    w3.middleware_onion.inject(geth_poa_middleware, layer=0)
    
    # 检查连接
    if not w3.is_connected():
        print("无法连接到以太坊节点")
        return {
            'success': False,
            'message': '无法连接到以太坊节点'
        }
    
    print(f"成功连接到以太坊节点，当前区块号: {w3.eth.block_number}")
    
    # 创建账户
    if not admin_private_key:
        print("错误: 未提供部署账户私钥，请在.env文件中设置ADMIN_PRIVATE_KEY")
        return {
            'success': False,
            'message': '未提供部署账户私钥，请在.env文件中设置ADMIN_PRIVATE_KEY'
        }
    
    account = Account.from_key(admin_private_key)
    address = account.address
    
    print(f"使用账户地址: {address}")
    
    # 加载合约ABI
    try:
        with open(CONTRACT_ABI_PATH, 'r') as f:
            contract_metadata = json.load(f)
            abi = contract_metadata['output']['abi']
    except Exception as e:
        print(f"加载合约ABI失败: {str(e)}")
        return {
            'success': False,
            'message': f'加载合约ABI失败: {str(e)}'
        }
    
    # 加载合约字节码
    try:
        with open(CONTRACT_BIN_PATH, 'r') as f:
            bytecode = f.read().strip()
    except Exception as e:
        print(f"加载合约字节码失败: {str(e)}")
        return {
            'success': False,
            'message': f'加载合约字节码失败: {str(e)}'
        }
    
    # 获取nonce
    nonce = w3.eth.get_transaction_count(address)
    
    # 创建合约
    contract = w3.eth.contract(
        abi=abi,
        bytecode=bytecode
    )
    
    # 构建部署交易
    transaction = contract.constructor().build_transaction({
        'from': address,
        'nonce': nonce,
        'gas': 5000000,
        'gasPrice': w3.eth.gas_price
    })
    
    # 签名交易
    signed_txn = w3.eth.account.sign_transaction(transaction, private_key=admin_private_key)
    
    # 发送交易
    print("发送部署交易...")
    try:
        tx_hash = w3.eth.send_raw_transaction(signed_txn.rawTransaction)
        print(f"交易已发送，交易哈希: {tx_hash.hex()}")
        
        # 等待交易被确认
        print("等待交易确认...")
        tx_receipt = w3.eth.wait_for_transaction_receipt(tx_hash)
        
        # 获取合约地址
        contract_address = tx_receipt.contractAddress
        
        print(f"合约已部署，地址: {contract_address}")
        print(f"交易哈希: {tx_hash.hex()}")
        print(f"区块号: {tx_receipt.blockNumber}")
        print(f"Gas使用量: {tx_receipt.gasUsed}")
        
        # 更新.env文件
        env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), '.env')
        if os.path.exists(env_path):
            # 读取现有.env文件内容
            env_content = ""
            with open(env_path, 'r') as f:
                env_content = f.read()
            
            # 更新CONTRACT_ADDRESS
            if 'CONTRACT_ADDRESS=' in env_content:
                env_content = '\n'.join([line if not line.startswith('CONTRACT_ADDRESS=') else f'CONTRACT_ADDRESS={contract_address}' for line in env_content.split('\n')])
            else:
                env_content += f'\nCONTRACT_ADDRESS={contract_address}'
            
            # 写入.env文件
            with open(env_path, 'w') as f:
                f.write(env_content)
            
            print(f".env文件已更新，CONTRACT_ADDRESS={contract_address}")
        else:
            print("警告: .env文件不存在，请手动创建并设置CONTRACT_ADDRESS")
        
        # 更新环境变量
        os.environ['CONTRACT_ADDRESS'] = contract_address
        
        return {
            'success': True,
            'contract_address': contract_address,
            'tx_hash': tx_hash.hex(),
            'block_number': tx_receipt.blockNumber
        }
    except Exception as e:
        print(f"部署失败: {str(e)}")
        return {
            'success': False,
            'message': f'部署失败: {str(e)}'
        }

if __name__ == '__main__':
    deploy_contract()
