<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放管理系统 - 待核查任务</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/unified-nav.css') }}">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background: linear-gradient(to right, #1B5E20, #388E3C);
            color: white;
            padding: 20px 0;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-name {
            margin-right: 15px;
        }
        .logout-btn {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 30px;
            cursor: pointer;
            text-decoration: none;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .logout-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        nav {
            background: linear-gradient(to right, #2E7D32, #43A047);
            padding: 10px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
        }
        .nav-item {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            margin-right: 5px;
            border-radius: 30px;
            transition: all 0.3s ease;
        }
        .nav-item:hover, .nav-item.active {
            background: linear-gradient(to right, #1B5E20, #2E7D32);
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        .card {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            transform: translateY(-5px);
        }
        .card-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        .btn-primary {
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            color: white;
        }
        .btn-success {
            background: linear-gradient(to right, #2ecc71, #27ae60);
            color: white;
        }
        .btn-danger {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
        }
        .filters {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }
        .filter-group {
            display: flex;
            align-items: center;
        }
        .filter-group label {
            margin-right: 5px;
            font-weight: bold;
        }
        .filter-group select {
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 30px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .filter-group select:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 2px 10px rgba(76, 175, 80, 0.2);
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        .modal-content {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 20px;
            width: 500px;
            max-width: 90%;
            padding: 30px;
            box-shadow: 0 15px 50px rgba(0,0,0,0.2);
            border: 1px solid rgba(0,0,0,0.05);
        }
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .modal-title {
            font-size: 18px;
            font-weight: bold;
        }
        .close-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
        }
        .modal-footer {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
        }
        .modal-footer button {
            margin-left: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 30px;
            font-size: 14px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 2px 10px rgba(76, 175, 80, 0.2);
        }
        textarea {
            height: 100px;
            resize: vertical;
            border-radius: 15px;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo">碳排放管理系统</div>
            <div class="user-info">
                <span class="user-name">国家碳排放核查中心</span>
                <a href="/login" class="logout-btn">退出登录</a>
            </div>
        </div>
    </header>

    <div class="demo-nav">
        <div class="nav-content">
            <a href="/verifier" class="nav-item"><i class="fas fa-tachometer-alt mr-2"></i>仪表板</a>
            <a href="/verifier_tasks" class="nav-item active"><i class="fas fa-tasks mr-2"></i>待核查任务</a>
            <a href="/verifier_records" class="nav-item"><i class="fas fa-clipboard-check mr-2"></i>核查记录</a>
            <a href="/verifier_enterprises" class="nav-item"><i class="fas fa-building mr-2"></i>企业管理</a>
            <a href="/verifier_reports" class="nav-item"><i class="fas fa-file-alt mr-2"></i>报告生成</a>
            <a href="/verifier_blockchain_config" class="nav-item"><i class="fas fa-link mr-2"></i>区块链配置</a>
        </div>
    </div>

    <div class="container">
        <h1>待核查任务</h1>

        <div class="filters">
            <div class="filter-group">
                <label for="enterprise-filter">企业:</label>
                <select id="enterprise-filter">
                    <option value="all">全部</option>
                    <option value="1">北京碳排放科技有限公司</option>
                    <option value="2">上海绿色能源有限公司</option>
                    <option value="3">广州环保科技有限公司</option>
                </select>
            </div>
            <div class="filter-group">
                <label for="source-filter">排放源:</label>
                <select id="source-filter">
                    <option value="all">全部</option>
                    <option value="coal">燃煤锅炉</option>
                    <option value="gas">天然气锅炉</option>
                    <option value="process">工业生产过程</option>
                    <option value="transport">交通运输</option>
                    <option value="electricity">电力消耗</option>
                </select>
            </div>
            <div class="filter-group">
                <label for="date-filter">提交日期:</label>
                <select id="date-filter">
                    <option value="all">全部</option>
                    <option value="today">今天</option>
                    <option value="week">本周</option>
                    <option value="month" selected>本月</option>
                </select>
            </div>
        </div>

        <div class="card">
            <div class="card-title">待核查任务列表</div>
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>企业</th>
                        <th>排放源</th>
                        <th>排放量</th>
                        <th>提交时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1002</td>
                        <td>北京碳排放科技有限公司</td>
                        <td>天然气锅炉</td>
                        <td>320 吨CO2e</td>
                        <td>2023-05-10</td>
                        <td><button class="btn btn-primary verify-btn" data-id="1002">核查</button></td>
                    </tr>
                    <tr>
                        <td>1005</td>
                        <td>上海绿色能源有限公司</td>
                        <td>电力消耗</td>
                        <td>450 吨CO2e</td>
                        <td>2023-05-11</td>
                        <td><button class="btn btn-primary verify-btn" data-id="1005">核查</button></td>
                    </tr>
                    <tr>
                        <td>1008</td>
                        <td>广州环保科技有限公司</td>
                        <td>工业生产过程</td>
                        <td>680 吨CO2e</td>
                        <td>2023-05-12</td>
                        <td><button class="btn btn-primary verify-btn" data-id="1008">核查</button></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- 核查模态框 -->
    <div class="modal" id="verifyModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">核查排放数据</div>
                <button class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>企业</label>
                    <div id="verify-company">北京碳排放科技有限公司</div>
                </div>
                <div class="form-group">
                    <label>排放源</label>
                    <div id="verify-source">天然气锅炉</div>
                </div>
                <div class="form-group">
                    <label>排放量</label>
                    <div id="verify-amount">320 吨CO2e</div>
                </div>
                <div class="form-group">
                    <label>计算方法</label>
                    <div id="verify-method">排放因子法</div>
                </div>
                <div class="form-group">
                    <label for="verify-conclusion">核查结论</label>
                    <select id="verify-conclusion">
                        <option value="">请选择核查结论</option>
                        <option value="approved">通过</option>
                        <option value="rejected">拒绝</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="verify-comments">核查意见</label>
                    <textarea id="verify-comments" rows="5" placeholder="请输入核查意见"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-danger cancel-btn">取消</button>
                <button class="btn btn-primary submit-btn">提交</button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 导航切换 - 不需要额外的事件监听器，因为我们使用的是链接
            // 根据当前URL设置活动导航项
            const currentPath = window.location.pathname;
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                if (item.getAttribute('href') === currentPath) {
                    navItems.forEach(i => i.classList.remove('active'));
                    item.classList.add('active');
                }
            });

            // 核查按钮点击事件
            const verifyBtns = document.querySelectorAll('.verify-btn');
            const verifyModal = document.getElementById('verifyModal');
            const closeBtn = verifyModal.querySelector('.close-btn');
            const cancelBtn = verifyModal.querySelector('.cancel-btn');
            const submitBtn = verifyModal.querySelector('.submit-btn');

            verifyBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const id = this.getAttribute('data-id');
                    // 在实际应用中，这里应该根据ID获取数据
                    // 这里只是演示，使用固定数据
                    verifyModal.style.display = 'flex';
                });
            });

            // 关闭模态框
            function closeModal() {
                verifyModal.style.display = 'none';
            }

            closeBtn.addEventListener('click', closeModal);
            cancelBtn.addEventListener('click', closeModal);

            // 提交核查结果
            submitBtn.addEventListener('click', function() {
                const conclusion = document.getElementById('verify-conclusion').value;
                const comments = document.getElementById('verify-comments').value;

                if (!conclusion) {
                    alert('请选择核查结论');
                    return;
                }

                if (!comments) {
                    alert('请输入核查意见');
                    return;
                }

                // 在实际应用中，这里应该发送AJAX请求提交核查结果
                // 这里只是演示，显示一个提示
                alert('核查结果提交成功！');
                closeModal();

                // 跳转到核查记录页面
                window.location.href = '/verifier_records';
            });

            // 添加任务分布图表
            const chartContainer = document.createElement('div');
            chartContainer.style.height = '300px';
            chartContainer.style.marginBottom = '30px';
            chartContainer.innerHTML = '<canvas id="taskDistributionChart"></canvas>';

            // 将图表插入到筛选器下方
            const filters = document.querySelector('.filters');
            filters.parentNode.insertBefore(chartContainer, filters.nextSibling);

            // 创建图表
            const ctx = document.getElementById('taskDistributionChart').getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['天然气锅炉', '燃煤锅炉', '工业生产过程', '交通运输', '电力消耗'],
                    datasets: [{
                        data: [5, 3, 8, 2, 4],
                        backgroundColor: [
                            'rgba(46, 204, 113, 0.8)',
                            'rgba(52, 152, 219, 0.8)',
                            'rgba(155, 89, 182, 0.8)',
                            'rgba(241, 196, 15, 0.8)',
                            'rgba(230, 126, 34, 0.8)'
                        ],
                        borderColor: [
                            'rgba(46, 204, 113, 1)',
                            'rgba(52, 152, 219, 1)',
                            'rgba(155, 89, 182, 1)',
                            'rgba(241, 196, 15, 1)',
                            'rgba(230, 126, 34, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                        },
                        title: {
                            display: true,
                            text: '待核查任务排放源分布',
                            font: {
                                size: 16
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.chart.data.datasets[0].data.reduce(
                                        (sum, value) => sum + value, 0
                                    );
                                    const percentage = Math.round((value * 100) / total);
                                    return `${label}: ${value} 个 (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>
