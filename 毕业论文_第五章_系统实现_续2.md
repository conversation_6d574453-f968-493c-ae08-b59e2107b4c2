### 5.4.2 组件实现

前端组件是构建用户界面的基本单元，包括导航栏、表单、表格、图表等。以下是主要组件的实现：

#### 5.4.2.1 导航栏组件

导航栏组件是系统的主要导航工具，提供页面间的跳转功能。导航栏组件的CSS样式实现如下：

```css
/* static/css/components/navbar.css */
#sidebar {
    min-width: 250px;
    max-width: 250px;
    background: #343a40;
    color: #fff;
    transition: all 0.3s;
    height: 100vh;
    position: fixed;
    z-index: 999;
}

#sidebar.active {
    margin-left: -250px;
}

#sidebar .sidebar-header {
    padding: 20px;
    background: #2E7D32;
}

#sidebar ul.components {
    padding: 20px 0;
    border-bottom: 1px solid #47748b;
}

#sidebar ul p {
    color: #fff;
    padding: 10px;
}

#sidebar ul li a {
    padding: 10px 20px;
    font-size: 1.1em;
    display: block;
    color: #fff;
    text-decoration: none;
    transition: all 0.3s;
}

#sidebar ul li a:hover {
    color: #fff;
    background: #4CAF50;
}

#sidebar ul li.active > a {
    color: #fff;
    background: #4CAF50;
}

#sidebar ul li a i {
    margin-right: 10px;
}

#content {
    width: calc(100% - 250px);
    min-height: 100vh;
    transition: all 0.3s;
    position: absolute;
    top: 0;
    right: 0;
}

#content.active {
    width: 100%;
}

@media (max-width: 768px) {
    #sidebar {
        margin-left: -250px;
    }
    #sidebar.active {
        margin-left: 0;
    }
    #content {
        width: 100%;
    }
    #content.active {
        width: calc(100% - 250px);
    }
}
```

导航栏组件的JavaScript实现如下：

```javascript
// static/js/components/navbar.js
document.addEventListener('DOMContentLoaded', function() {
    // 侧边栏切换
    document.getElementById('sidebarCollapse').addEventListener('click', function() {
        document.getElementById('sidebar').classList.toggle('active');
        document.getElementById('content').classList.toggle('active');
    });
    
    // 设置当前活动导航项
    const currentPath = window.location.pathname;
    const navItems = document.querySelectorAll('#sidebar ul li a');
    
    navItems.forEach(item => {
        const href = item.getAttribute('href');
        if (currentPath === href) {
            item.parentElement.classList.add('active');
        } else {
            item.parentElement.classList.remove('active');
        }
    });
    
    // 退出登录
    document.getElementById('logout-btn').addEventListener('click', function(e) {
        e.preventDefault();
        
        // 清除本地存储的token和用户信息
        localStorage.removeItem('access_token');
        localStorage.removeItem('user');
        
        // 跳转到登录页面
        window.location.href = '/login';
    });
    
    // 显示用户名
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    if (user.username) {
        document.getElementById('username').textContent = user.username;
    }
});
```

#### 5.4.2.2 表单组件

表单组件用于数据输入和提交，如排放数据提交表单、核查结果提交表单等。以排放数据提交表单为例，其HTML实现如下：

```html
<!-- templates/components/emission_form.html -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">排放数据提交</h6>
    </div>
    <div class="card-body">
        <form id="emission-form">
            <div class="row mb-3">
                <div class="col-md-4">
                    <label for="year" class="form-label">排放年份</label>
                    <select class="form-select" id="year" name="year" required>
                        <option value="">请选择年份</option>
                        <option value="2025">2025</option>
                        <option value="2024">2024</option>
                        <option value="2023">2023</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="period" class="form-label">排放周期</label>
                    <select class="form-select" id="period" name="period" required>
                        <option value="">请选择周期</option>
                        <option value="year">年度</option>
                        <option value="quarter">季度</option>
                        <option value="month">月度</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="period-value" class="form-label">周期值</label>
                    <select class="form-select" id="period-value" name="period_value" required disabled>
                        <option value="">请先选择周期</option>
                    </select>
                </div>
            </div>
            
            <div class="mb-3">
                <h6 class="font-weight-bold">排放源</h6>
                <div id="emission-sources-container">
                    <!-- 排放源表单将通过JavaScript动态添加 -->
                </div>
                <button type="button" class="btn btn-outline-primary mt-2" id="add-source-btn">
                    <i class="fas fa-plus"></i> 添加排放源
                </button>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-4">
                    <label for="total-emission" class="form-label">总排放量 (吨CO2e)</label>
                    <input type="number" class="form-control" id="total-emission" name="total_emission" readonly>
                </div>
                <div class="col-md-4">
                    <label for="scope1-emission" class="form-label">范围一排放量 (吨CO2e)</label>
                    <input type="number" class="form-control" id="scope1-emission" name="scope1_emission">
                </div>
                <div class="col-md-4">
                    <label for="scope2-emission" class="form-label">范围二排放量 (吨CO2e)</label>
                    <input type="number" class="form-control" id="scope2-emission" name="scope2_emission">
                </div>
            </div>
            
            <div class="d-flex justify-content-between">
                <button type="button" class="btn btn-secondary" id="save-draft-btn">保存草稿</button>
                <button type="submit" class="btn btn-primary">提交数据</button>
            </div>
        </form>
    </div>
</div>
```

排放数据提交表单的JavaScript实现如下：

```javascript
// static/js/components/emission_form.js
document.addEventListener('DOMContentLoaded', function() {
    // 周期选择联动
    const periodSelect = document.getElementById('period');
    const periodValueSelect = document.getElementById('period-value');
    
    periodSelect.addEventListener('change', function() {
        const period = this.value;
        periodValueSelect.innerHTML = '<option value="">请选择</option>';
        periodValueSelect.disabled = true;
        
        if (period === 'quarter') {
            for (let i = 1; i <= 4; i++) {
                const option = document.createElement('option');
                option.value = i;
                option.textContent = `第${i}季度`;
                periodValueSelect.appendChild(option);
            }
            periodValueSelect.disabled = false;
        } else if (period === 'month') {
            for (let i = 1; i <= 12; i++) {
                const option = document.createElement('option');
                option.value = i;
                option.textContent = `${i}月`;
                periodValueSelect.appendChild(option);
            }
            periodValueSelect.disabled = false;
        } else if (period === 'year') {
            const option = document.createElement('option');
            option.value = 1;
            option.textContent = '全年';
            periodValueSelect.appendChild(option);
            periodValueSelect.disabled = false;
        }
    });
    
    // 添加排放源
    const addSourceBtn = document.getElementById('add-source-btn');
    const sourcesContainer = document.getElementById('emission-sources-container');
    let sourceCount = 0;
    
    addSourceBtn.addEventListener('click', function() {
        sourceCount++;
        const sourceId = `source-${sourceCount}`;
        
        const sourceDiv = document.createElement('div');
        sourceDiv.className = 'card mb-3';
        sourceDiv.id = sourceId;
        
        sourceDiv.innerHTML = `
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="card-title mb-0">排放源 #${sourceCount}</h6>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-source-btn" data-source-id="${sourceId}">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">排放源类型</label>
                        <select class="form-select source-type" name="emission_sources[${sourceCount-1}][source_type]" required>
                            <option value="">请选择排放源类型</option>
                            <option value="fuel_combustion">燃料燃烧</option>
                            <option value="industrial_process">工业生产过程</option>
                            <option value="electricity">电力消耗</option>
                            <option value="transportation">交通运输</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">排放源名称</label>
                        <input type="text" class="form-control source-name" name="emission_sources[${sourceCount-1}][source_name]" required>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-3">
                        <label class="form-label">活动数据</label>
                        <input type="number" step="0.01" class="form-control activity-data" name="emission_sources[${sourceCount-1}][activity_data]" required>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">活动数据单位</label>
                        <select class="form-select activity-unit" name="emission_sources[${sourceCount-1}][activity_unit]" required>
                            <option value="">请选择单位</option>
                            <option value="t">吨</option>
                            <option value="kWh">千瓦时</option>
                            <option value="L">升</option>
                            <option value="m3">立方米</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">排放因子</label>
                        <input type="number" step="0.000001" class="form-control emission-factor" name="emission_sources[${sourceCount-1}][emission_factor]" required>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">排放因子单位</label>
                        <select class="form-select emission-factor-unit" name="emission_sources[${sourceCount-1}][emission_factor_unit]" required>
                            <option value="">请选择单位</option>
                            <option value="tCO2e/t">吨CO2e/吨</option>
                            <option value="tCO2e/kWh">吨CO2e/千瓦时</option>
                            <option value="tCO2e/L">吨CO2e/升</option>
                            <option value="tCO2e/m3">吨CO2e/立方米</option>
                        </select>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">计算方法</label>
                        <select class="form-select calculation-method" name="emission_sources[${sourceCount-1}][calculation_method]" required>
                            <option value="">请选择计算方法</option>
                            <option value="emission_factor">排放因子法</option>
                            <option value="mass_balance">物料平衡法</option>
                            <option value="direct_measurement">直接测量法</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">排放量 (吨CO2e)</label>
                        <input type="number" step="0.01" class="form-control emission-amount" name="emission_sources[${sourceCount-1}][emission_amount]" readonly>
                    </div>
                </div>
            </div>
        `;
        
        sourcesContainer.appendChild(sourceDiv);
        
        // 绑定删除排放源按钮事件
        sourceDiv.querySelector('.remove-source-btn').addEventListener('click', function() {
            const sourceId = this.getAttribute('data-source-id');
            document.getElementById(sourceId).remove();
            calculateTotalEmission();
        });
        
        // 绑定排放量计算事件
        const activityDataInput = sourceDiv.querySelector('.activity-data');
        const emissionFactorInput = sourceDiv.querySelector('.emission-factor');
        
        function calculateEmissionAmount() {
            const activityData = parseFloat(activityDataInput.value) || 0;
            const emissionFactor = parseFloat(emissionFactorInput.value) || 0;
            const emissionAmount = activityData * emissionFactor;
            sourceDiv.querySelector('.emission-amount').value = emissionAmount.toFixed(2);
            calculateTotalEmission();
        }
        
        activityDataInput.addEventListener('input', calculateEmissionAmount);
        emissionFactorInput.addEventListener('input', calculateEmissionAmount);
    });
    
    // 计算总排放量
    function calculateTotalEmission() {
        const emissionAmountInputs = document.querySelectorAll('.emission-amount');
        let total = 0;
        
        emissionAmountInputs.forEach(input => {
            total += parseFloat(input.value) || 0;
        });
        
        document.getElementById('total-emission').value = total.toFixed(2);
    }
    
    // 表单提交
    const emissionForm = document.getElementById('emission-form');
    
    emissionForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // 收集表单数据
        const formData = new FormData(emissionForm);
        const data = {};
        
        for (const [key, value] of formData.entries()) {
            if (key.includes('[') && key.includes(']')) {
                // 处理排放源数组
                const matches = key.match(/emission_sources\[(\d+)\]\[([^\]]+)\]/);
                if (matches) {
                    const index = matches[1];
                    const field = matches[2];
                    
                    if (!data.emission_sources) {
                        data.emission_sources = [];
                    }
                    
                    if (!data.emission_sources[index]) {
                        data.emission_sources[index] = {};
                    }
                    
                    data.emission_sources[index][field] = value;
                }
            } else {
                data[key] = value;
            }
        }
        
        // 发送请求
        fetch('/api/emissions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.message) {
                alert(result.message);
                if (result.emission_data) {
                    // 提交成功，跳转到排放数据列表页面
                    window.location.href = '/emissions';
                }
            }
        })
        .catch(error => {
            console.error('提交排放数据出错:', error);
            alert('提交排放数据出错，请稍后再试');
        });
    });
    
    // 保存草稿
    document.getElementById('save-draft-btn').addEventListener('click', function() {
        // 类似表单提交，但状态为草稿
        // 实现略
    });
    
    // 初始添加一个排放源
    addSourceBtn.click();
});
```
