<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放核查系统 - 惩罚管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container">
            <a class="navbar-brand" href="/"><i class="fas fa-leaf me-2"></i>碳排放核查系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin"><i class="fas fa-tachometer-alt me-1"></i>仪表板</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/users"><i class="fas fa-users me-1"></i>用户管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/quotas"><i class="fas fa-chart-pie me-1"></i>配额管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/admin/penalties"><i class="fas fa-exclamation-triangle me-1"></i>惩罚管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/reports"><i class="fas fa-file-alt me-1"></i>报告管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/blockchain/config"><i class="fas fa-link me-1"></i>区块链配置</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>{{ current_user.get('username', '管理员') }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="/admin/profile"><i class="fas fa-id-card me-1"></i>个人资料</a></li>
                            <li><a class="dropdown-item" href="/admin/settings"><i class="fas fa-cog me-1"></i>系统设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-1"></i>退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mt-4">
        <!-- 消息提示 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-exclamation-triangle me-2"></i>惩罚管理</h1>
            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addPenaltyModal">
                <i class="fas fa-plus me-1"></i>添加惩罚
            </button>
        </div>

        <!-- 惩罚统计 -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">总惩罚数</h6>
                                <h2 class="card-text">{{ penalties|length if penalties else 0 }}</h2>
                            </div>
                            <i class="fas fa-exclamation-triangle fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">待处理</h6>
                                <h2 class="card-text">{{ penalties|selectattr('status', 'equalto', '待处理')|list|length if penalties else 0 }}</h2>
                            </div>
                            <i class="fas fa-clock fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">已处理</h6>
                                <h2 class="card-text">{{ penalties|selectattr('status', 'equalto', '已处理')|list|length if penalties else 0 }}</h2>
                            </div>
                            <i class="fas fa-check-circle fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 惩罚筛选 -->
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0"><i class="fas fa-filter me-2"></i>筛选惩罚</h5>
            </div>
            <div class="card-body">
                <form method="get" action="/admin/penalties" class="row g-3">
                    <div class="col-md-3">
                        <label for="enterprise" class="form-label">企业名称</label>
                        <input type="text" class="form-control" id="enterprise" name="enterprise" placeholder="输入企业名称">
                    </div>
                    <div class="col-md-3">
                        <label for="penalty_type" class="form-label">惩罚类型</label>
                        <select class="form-select" id="penalty_type" name="penalty_type">
                            <option value="">全部类型</option>
                            <option value="超额排放">超额排放</option>
                            <option value="数据造假">数据造假</option>
                            <option value="未按时提交">未按时提交</option>
                            <option value="拒绝核查">拒绝核查</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">状态</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">全部状态</option>
                            <option value="待处理">待处理</option>
                            <option value="已处理">已处理</option>
                            <option value="已撤销">已撤销</option>
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-1"></i>搜索
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 惩罚列表 -->
        <div class="card">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0"><i class="fas fa-list me-2"></i>惩罚列表</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>企业名称</th>
                                <th>惩罚类型</th>
                                <th>金额 (元)</th>
                                <th>发布日期</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if penalties %}
                                {% for penalty in penalties %}
                                    <tr>
                                        <td>{{ penalty.id }}</td>
                                        <td>{{ penalty.enterprise_name }}</td>
                                        <td>{{ penalty.penalty_type }}</td>
                                        <td>{{ penalty.amount }}</td>
                                        <td>{{ penalty.issue_date }}</td>
                                        <td>
                                            {% if penalty.status == '待处理' %}
                                                <span class="badge bg-warning">待处理</span>
                                            {% elif penalty.status == '已处理' %}
                                                <span class="badge bg-success">已处理</span>
                                            {% elif penalty.status == '已撤销' %}
                                                <span class="badge bg-secondary">已撤销</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="/admin/penalties/{{ penalty.id }}" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="/admin/penalties/{{ penalty.id }}/edit" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ penalty.id }}">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                            
                                            <!-- 删除确认模态框 -->
                                            <div class="modal fade" id="deleteModal{{ penalty.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ penalty.id }}" aria-hidden="true">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title" id="deleteModalLabel{{ penalty.id }}">确认删除</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            确定要删除对 <strong>{{ penalty.enterprise_name }}</strong> 的惩罚记录吗？此操作不可逆。
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                                            <form action="/admin/penalties/{{ penalty.id }}/delete" method="post" style="display: inline;">
                                                                <button type="submit" class="btn btn-danger">确认删除</button>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="7" class="text-center">暂无惩罚数据</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1" aria-disabled="true">上一页</a>
                        </li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                        <li class="page-item">
                            <a class="page-link" href="#">下一页</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- 添加惩罚模态框 -->
    <div class="modal fade" id="addPenaltyModal" tabindex="-1" aria-labelledby="addPenaltyModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addPenaltyModalLabel">添加惩罚</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addPenaltyForm" action="/admin/penalties/add" method="post">
                        <div class="mb-3">
                            <label for="enterprise_id" class="form-label">企业</label>
                            <select class="form-select" id="enterprise_id" name="enterprise_id" required>
                                <option value="">选择企业</option>
                                <option value="1">北京碳排放科技有限公司</option>
                                <option value="2">上海绿色能源有限公司</option>
                                <option value="3">广州环保科技有限公司</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="penalty_type" class="form-label">惩罚类型</label>
                            <select class="form-select" id="penalty_type" name="penalty_type" required>
                                <option value="">选择类型</option>
                                <option value="超额排放">超额排放</option>
                                <option value="数据造假">数据造假</option>
                                <option value="未按时提交">未按时提交</option>
                                <option value="拒绝核查">拒绝核查</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="amount" class="form-label">金额 (元)</label>
                            <input type="number" class="form-control" id="amount" name="amount" min="0" required>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">描述</label>
                            <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" form="addPenaltyForm" class="btn btn-primary">添加</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">© 2025 碳排放核查系统 | 基于区块链技术</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
