"""
数据库初始化脚本
提供两种初始化方式：
1. 使用SQL脚本初始化
2. 使用SQLAlchemy初始化
"""

import sys
from db_utils import test_connection, execute_sql_script, init_db_with_sqlalchemy, verify_database

def init_db():
    """初始化数据库"""
    print('开始初始化数据库...')
    
    # 测试数据库连接
    if not test_connection():
        print('数据库连接失败，无法初始化数据库')
        return False
    
    # 询问初始化方式
    if len(sys.argv) > 1 and sys.argv[1] == '--sql':
        # 使用SQL脚本初始化
        print('使用SQL脚本初始化数据库...')
        if execute_sql_script('create_tables.sql'):
            print('SQL脚本执行成功')
        else:
            print('SQL脚本执行失败')
            return False
    else:
        # 使用SQLAlchemy初始化
        print('使用SQLAlchemy初始化数据库...')
        if not init_db_with_sqlalchemy():
            print('SQLAlchemy初始化失败')
            return False
    
    # 验证数据库
    if verify_database():
        print('数据库初始化成功')
        return True
    else:
        print('数据库初始化失败')
        return False

if __name__ == '__main__':
    init_db()
