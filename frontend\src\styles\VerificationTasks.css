.verification-tasks {
  padding: 20px;
}

.verification-tasks h2 {
  margin-bottom: 30px;
  color: #333;
}

.task-form {
  background: white;
  padding: 25px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.task-form h3 {
  margin-bottom: 20px;
  color: #444;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #555;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.form-control:focus {
  border-color: #667eea;
  outline: none;
}

textarea.form-control {
  resize: vertical;
  min-height: 100px;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn-primary {
  background-color: #667eea;
  color: white;
}

.btn-primary:hover {
  background-color: #5a6fd6;
}

.task-list {
  background: white;
  padding: 25px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.task-list h3 {
  margin-bottom: 20px;
  color: #444;
}

.table-responsive {
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #444;
}

.table tr:hover {
  background-color: #f8f9fa;
}

.priority {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.priority.low {
  background-color: #e0f2fe;
  color: #0369a1;
}

.priority.medium {
  background-color: #fef3c7;
  color: #92400e;
}

.priority.high {
  background-color: #fee2e2;
  color: #991b1b;
}

.status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status.pending {
  background-color: #f3f4f6;
  color: #4b5563;
}

.status.in_progress {
  background-color: #dbeafe;
  color: #1e40af;
}

.status.completed {
  background-color: #dcfce7;
  color: #166534;
}

.status.cancelled {
  background-color: #fee2e2;
  color: #991b1b;
}

.status-select {
  padding: 6px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background-color: white;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: 18px;
  color: #666;
}

.alert {
  padding: 12px;
  border-radius: 5px;
  margin-bottom: 20px;
}

.alert-danger {
  background-color: #fee2e2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .verification-tasks {
    padding: 15px;
  }

  .task-form,
  .task-list {
    padding: 15px;
  }

  .table th,
  .table td {
    padding: 8px;
  }
} 