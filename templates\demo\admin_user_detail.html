<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放管理系统 - 用户详情</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background: linear-gradient(to right, #1B5E20, #388E3C);
            color: white;
            padding: 20px 0;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-name {
            margin-right: 15px;
        }
        .logout-btn {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 30px;
            cursor: pointer;
            text-decoration: none;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .logout-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        nav {
            background: linear-gradient(to right, #2E7D32, #43A047);
            padding: 10px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
        }
        .nav-item {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            margin-right: 5px;
            border-radius: 30px;
            transition: all 0.3s ease;
        }
        .nav-item:hover, .nav-item.active {
            background: linear-gradient(to right, #1B5E20, #2E7D32);
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        .card {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            transform: translateY(-5px);
        }
        .card-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        .btn-primary {
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            color: white;
        }
        .btn-success {
            background: linear-gradient(to right, #2ecc71, #27ae60);
            color: white;
        }
        .btn-danger {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
        }
        .detail-section {
            margin-bottom: 30px;
            background-color: #f9f9f9;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.05);
            border-left: 5px solid #4CAF50;
        }
        .detail-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2E7D32;
            border-bottom: 1px solid #A5D6A7;
            padding-bottom: 10px;
        }
        .detail-row {
            display: flex;
            margin-bottom: 15px;
            border-bottom: 1px dashed rgba(0,0,0,0.05);
            padding-bottom: 10px;
        }
        .detail-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        .detail-label {
            width: 180px;
            font-weight: bold;
            color: #388E3C;
        }
        .detail-value {
            flex: 1;
        }
        .status {
            display: inline-block;
            padding: 8px 15px;
            border-radius: 30px;
            font-size: 12px;
            font-weight: bold;
            color: white;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        .status-active {
            background: linear-gradient(to right, #2ecc71, #27ae60);
        }
        .status-inactive {
            background: linear-gradient(to right, #e74c3c, #c0392b);
        }
        .chart-container {
            height: 300px;
            margin-top: 20px;
            background-color: #f1f8e9;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        .chart-placeholder {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 10px;
        }
        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        th, td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        th {
            background: linear-gradient(to right, #43A047, #66BB6A);
            color: white;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 14px;
            letter-spacing: 0.5px;
        }
        tr:last-child td {
            border-bottom: none;
        }
        tr:nth-child(even) {
            background-color: rgba(0,0,0,0.02);
        }
        tr:hover {
            background-color: rgba(76, 175, 80, 0.05);
        }
        .actions {
            display: flex;
            justify-content: flex-end;
            gap: 15px;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px dashed rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo">碳排放管理系统</div>
            <div class="user-info">
                <span class="user-name">管理员</span>
                <a href="/login" class="logout-btn">退出登录</a>
            </div>
        </div>
    </header>

    <nav>
        <div class="nav-content">
            <a href="/admin" class="nav-item">系统概览</a>
            <a href="/admin_users" class="nav-item active">用户管理</a>
            <a href="/admin_quotas" class="nav-item">配额管理</a>
            <a href="/admin_verifications" class="nav-item">核查管理</a>
            <a href="/admin_transactions" class="nav-item">交易管理</a>
            <a href="/admin_reports" class="nav-item">系统报告</a>
            <a href="/admin_settings" class="nav-item">系统配置</a>
            <a href="/admin_logs" class="nav-item">日志查看</a>
        </div>
    </nav>

    <div class="container">
        <div class="card-title">
            <h1>用户详情</h1>
            <div>
                <a href="/admin_users" class="btn btn-primary">返回列表</a>
            </div>
        </div>

        <div class="card">
            <div class="detail-section">
                <div class="detail-title">基本信息</div>

                <div class="detail-row">
                    <div class="detail-label">用户ID</div>
                    <div class="detail-value">2</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">用户名</div>
                    <div class="detail-value">enterprise1</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">电子邮箱</div>
                    <div class="detail-value"><EMAIL></div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">角色</div>
                    <div class="detail-value">企业</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">公司名称</div>
                    <div class="detail-value">北京碳排放科技有限公司</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">统一社会信用代码</div>
                    <div class="detail-value">91110000123456789A</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">注册时间</div>
                    <div class="detail-value">2023-01-15</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">状态</div>
                    <div class="detail-value">
                        <span class="status status-active">活跃</span>
                    </div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">最后登录时间</div>
                    <div class="detail-value">2023-05-12 15:30:45</div>
                </div>
            </div>

            <div class="detail-section">
                <div class="detail-title">排放概况</div>

                <div class="detail-row">
                    <div class="detail-label">年度配额</div>
                    <div class="detail-value">5,000 吨CO2e</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">当前排放量</div>
                    <div class="detail-value">2,350 吨CO2e</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">剩余配额</div>
                    <div class="detail-value">2,650 吨CO2e</div>
                </div>

                <div class="chart-container">
                    <img src="https://via.placeholder.com/1160x300?text=排放趋势图表" alt="排放趋势图表" class="chart-placeholder">
                </div>
            </div>

            <div class="detail-section">
                <div class="detail-title">最近活动</div>

                <table>
                    <thead>
                        <tr>
                            <th>时间</th>
                            <th>活动类型</th>
                            <th>详情</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>2023-05-12 09:45:23</td>
                            <td>提交排放数据</td>
                            <td>提交了2023年4月的排放数据</td>
                        </tr>
                        <tr>
                            <td>2023-05-10 14:30:15</td>
                            <td>交易</td>
                            <td>向enterprise3购买了200吨配额</td>
                        </tr>
                        <tr>
                            <td>2023-05-05 11:20:45</td>
                            <td>生成报告</td>
                            <td>生成了2023年第一季度排放报告</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="actions">
                <a href="/admin_users" class="btn btn-primary">返回列表</a>
                <a href="#" class="btn btn-success">编辑用户</a>
                <a href="#" class="btn btn-danger">禁用账号</a>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 导航切换 - 不需要额外的事件监听器，因为我们使用的是链接
            // 根据当前URL设置活动导航项
            const currentPath = window.location.pathname;
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                if (item.getAttribute('href') === currentPath) {
                    navItems.forEach(i => i.classList.remove('active'));
                    item.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>
