.blockchain-info {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 15px;
  background-color: #f9f9f9;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s ease;
}

.blockchain-info:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.blockchain-info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  cursor: pointer;
  background-color: #ffffff;
  border-bottom: 1px solid #e0e0e0;
}

.blockchain-info-type {
  display: flex;
  align-items: center;
  width: 120px;
}

.blockchain-info-type i {
  margin-right: 8px;
  font-size: 16px;
}

.blockchain-info-hash {
  flex-grow: 1;
  font-family: monospace;
  color: #555;
  padding: 0 15px;
}

.blockchain-info-status {
  width: 80px;
  text-align: center;
  font-weight: 500;
}

.blockchain-info-toggle {
  width: 30px;
  text-align: center;
  color: #888;
}

.blockchain-info-details {
  padding: 15px;
  background-color: #f5f9f5;
  border-top: 1px solid #e0e0e0;
}

.blockchain-info-detail-row {
  display: flex;
  margin-bottom: 10px;
  font-size: 14px;
}

.detail-label {
  width: 100px;
  color: #666;
  font-weight: 500;
}

.detail-value {
  flex-grow: 1;
  font-family: monospace;
  word-break: break-all;
}

.blockchain-info-actions {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

.btn-explorer {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  transition: background-color 0.3s;
}

.btn-explorer:hover {
  background-color: #45a049;
}

.btn-explorer i {
  margin-right: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .blockchain-info-header {
    flex-wrap: wrap;
  }
  
  .blockchain-info-type {
    width: 100px;
  }
  
  .blockchain-info-hash {
    width: calc(100% - 210px);
  }
  
  .detail-label {
    width: 80px;
  }
}
