"""
惩罚记录模型
"""

from datetime import datetime
from backend import db
from backend.models.user import User

class Penalty(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    enterprise_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>('user.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    reason = db.Column(db.Text, nullable=False)
    penalty_time = db.Column(db.DateTime, default=datetime.utcnow)
    status = db.Column(db.String(20), default='pending')  # pending, paid, disputed
    blockchain_hash = db.Column(db.String(66))
    blockchain_block = db.Column(db.Integer)

    enterprise = db.relationship('User', backref=db.backref('penalties', lazy=True))

    def to_dict(self):
        return {
            'id': self.id,
            'enterprise_id': self.enterprise_id,
            'enterprise_name': self.enterprise.company_name if self.enterprise else None,
            'amount': self.amount,
            'reason': self.reason,
            'penalty_time': self.penalty_time.isoformat() if self.penalty_time else None,
            'status': self.status,
            'blockchain_hash': self.blockchain_hash,
            'blockchain_block': self.blockchain_block
        }
