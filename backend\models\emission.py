"""
排放数据模型
"""

from datetime import datetime
from backend import db
from backend.models.user import User

class Emission(db.Model):
    """排放数据模型"""
    __tablename__ = 'emission'

    id = db.Column(db.Integer, primary_key=True)
    enterprise_id = db.Column(db.Integer, db.<PERSON>ey('user.id'), nullable=False)
    emission_source = db.Column(db.String(100), nullable=False)
    emission_amount = db.Column(db.Float, nullable=False)
    emission_unit = db.Column(db.String(20), nullable=False)
    calculation_method = db.Column(db.String(100), nullable=False)
    emission_period_start = db.Column(db.Date, nullable=False)
    emission_period_end = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), default='draft')  # draft, submitted, pending, verified, rejected
    description = db.Column(db.Text)
    proof_file_path = db.Column(db.String(255))
    blockchain_hash = db.Column(db.String(66))
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # 关联关系
    enterprise = db.relationship('User', backref=db.backref('emissions', lazy=True))

    def __init__(self, enterprise_id, emission_source, calculation_method, emission_amount, emission_unit,
                 emission_period_start, emission_period_end, status='draft', description=None, proof_file_path=None,
                 blockchain_hash=None):
        self.enterprise_id = enterprise_id
        self.emission_source = emission_source
        self.calculation_method = calculation_method
        self.emission_amount = emission_amount
        self.emission_unit = emission_unit
        self.emission_period_start = emission_period_start
        self.emission_period_end = emission_period_end
        self.status = status
        self.description = description
        self.proof_file_path = proof_file_path
        self.blockchain_hash = blockchain_hash

    def __repr__(self):
        return f'<Emission {self.id}: {self.emission_source} - {self.emission_amount} {self.emission_unit}>'

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'enterprise_id': self.enterprise_id,
            'enterprise_name': self.enterprise.company_name if self.enterprise else None,
            'emission_source': self.emission_source,
            'calculation_method': self.calculation_method,
            'emission_amount': self.emission_amount,
            'emission_unit': self.emission_unit,
            'emission_period_start': self.emission_period_start.strftime('%Y-%m-%d') if self.emission_period_start else None,
            'emission_period_end': self.emission_period_end.strftime('%Y-%m-%d') if self.emission_period_end else None,
            'status': self.status,
            'description': self.description,
            'proof_file_path': self.proof_file_path,
            'blockchain_hash': self.blockchain_hash,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S')
        }

# 保留旧模型以兼容现有代码
class EmissionData(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    enterprise_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    emission_source = db.Column(db.String(100), nullable=False)
    emission_amount = db.Column(db.Float, nullable=False)
    emission_unit = db.Column(db.String(20), nullable=False)
    calculation_method = db.Column(db.String(100), nullable=False)
    emission_period_start = db.Column(db.Date, nullable=False)
    emission_period_end = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), default='pending')  # pending, verified, rejected
    submission_time = db.Column(db.DateTime, default=datetime.utcnow)
    proof_file_path = db.Column(db.String(255))
    blockchain_hash = db.Column(db.String(66))
    blockchain_block = db.Column(db.Integer)

    enterprise = db.relationship('User', backref=db.backref('emission_data', lazy=True))

    def to_dict(self):
        return {
            'id': self.id,
            'enterprise_id': self.enterprise_id,
            'enterprise_name': self.enterprise.company_name if self.enterprise else None,
            'emission_source': self.emission_source,
            'emission_amount': self.emission_amount,
            'emission_unit': self.emission_unit,
            'calculation_method': self.calculation_method,
            'emission_period_start': self.emission_period_start.isoformat() if self.emission_period_start else None,
            'emission_period_end': self.emission_period_end.isoformat() if self.emission_period_end else None,
            'status': self.status,
            'submission_time': self.submission_time.isoformat() if self.submission_time else None,
            'proof_file_path': self.proof_file_path,
            'blockchain_hash': self.blockchain_hash,
            'blockchain_block': self.blockchain_block
        }
