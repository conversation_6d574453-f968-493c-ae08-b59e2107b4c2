"""
修改数据库表结构
用于修改用户表中的password_hash字段长度
"""

import os
from dotenv import load_dotenv
from db_utils import get_db_connection

# 加载环境变量
load_dotenv()

def alter_user_table():
    """修改用户表中的password_hash字段长度"""
    print("修改用户表中的password_hash字段长度...")

    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        # 读取SQL脚本，指定UTF-8编码
        with open('alter_user_table.sql', 'r', encoding='utf-8') as f:
            sql = f.read()

        # 执行SQL脚本
        cursor.execute(sql)
        conn.commit()

        print("用户表修改成功")
    except Exception as e:
        print(f"修改用户表失败: {str(e)}")
    finally:
        conn.close()

if __name__ == "__main__":
    alter_user_table()
