.report-generator {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.report-generator h2 {
  text-align: center;
  margin-bottom: 30px;
  color: #2c3e50;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.success-message {
  background-color: #d4edda;
  color: #155724;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.report-container {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  margin-bottom: 30px;
}

.report-form {
  flex: 1;
  min-width: 300px;
}

.report-form h3,
.reports-list h3,
.report-features h3 {
  margin-bottom: 20px;
  color: #2c3e50;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 10px;
}

.report-form form {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #495057;
}

.form-group input[type="text"],
.form-group input[type="date"],
.form-group select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 16px;
}

.form-row {
  display: flex;
  gap: 15px;
}

.form-row .form-group {
  flex: 1;
}

.form-group.checkbox {
  display: flex;
  align-items: center;
  gap: 10px;
}

.form-group.checkbox input {
  width: auto;
}

.form-group.checkbox label {
  margin-bottom: 0;
}

.generate-btn {
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 12px 20px;
  font-size: 16px;
  cursor: pointer;
  width: 100%;
  margin-top: 10px;
}

.generate-btn:hover {
  background-color: #0069d9;
}

.generate-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.report-preview {
  flex: 1;
  min-width: 300px;
}

.preview-placeholder {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.preview-placeholder img {
  max-width: 100%;
  height: auto;
  margin-bottom: 20px;
}

.preview-placeholder p {
  color: #6c757d;
  font-style: italic;
}

.reports-list {
  margin-bottom: 30px;
}

.reports-list table {
  width: 100%;
  border-collapse: collapse;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.reports-list th,
.reports-list td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.reports-list th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #495057;
}

.reports-list tr:hover {
  background-color: #f8f9fa;
}

.download-btn {
  background-color: #17a2b8;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  cursor: pointer;
}

.download-btn:hover {
  background-color: #138496;
}

.loading,
.no-data {
  text-align: center;
  padding: 30px;
  color: #6c757d;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.report-features {
  margin-bottom: 30px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

.feature-card {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-icon {
  font-size: 36px;
  margin-bottom: 15px;
}

.feature-card h4 {
  margin-bottom: 10px;
  color: #2c3e50;
}

.feature-card p {
  color: #6c757d;
  font-size: 14px;
}

@media (max-width: 768px) {
  .report-container {
    flex-direction: column;
  }
  
  .form-row {
    flex-direction: column;
    gap: 0;
  }
  
  .reports-list {
    overflow-x: auto;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
}
