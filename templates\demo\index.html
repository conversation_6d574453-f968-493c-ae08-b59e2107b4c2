{% extends 'base.html' %}

{% block title %}碳排放核查系统 - 演示{% endblock %}

{% block head %}
<style>
    body {
        font-family: 'Arial', sans-serif;
        margin: 0;
        padding: 0;
        background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
        color: #333;
        min-height: 100vh;
    }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background: linear-gradient(to right, #1B5E20, #388E3C);
            color: white;
            padding: 20px 0;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-name {
            margin-right: 15px;
        }
        .logout-btn {
            background-color: #e74c3c;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        nav {
            background: linear-gradient(to right, #2E7D32, #43A047);
            padding: 10px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
        }
        .nav-item {
            color: white;
            text-decoration: none;
            padding: 15px 20px;
            margin-right: 5px;
            border-radius: 0;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 16px;
            letter-spacing: 0.5px;
            position: relative;
            display: flex;
            align-items: center;
        }
        .nav-item:hover, .nav-item.active {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }
        .nav-item.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 20%;
            right: 20%;
            height: 3px;
            background-color: white;
            border-radius: 3px 3px 0 0;
        }
        .card {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            transform: translateY(-5px);
        }
        .card-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .dashboard {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
        }
        .stat-card {
            text-align: center;
            padding: 30px 20px;
        }
        .stat-value {
            font-size: 36px;
            font-weight: bold;
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin: 15px 0;
            text-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .stat-label {
            color: #7f8c8d;
        }
        .chart-container {
            height: 300px;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        th, td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        th {
            background: linear-gradient(to right, #43A047, #66BB6A);
            color: white;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 14px;
            letter-spacing: 0.5px;
        }
        tr:last-child td {
            border-bottom: none;
        }
        tr:nth-child(even) {
            background-color: rgba(0,0,0,0.02);
        }
        tr:hover {
            background-color: rgba(76, 175, 80, 0.05);
        }
        .status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            display: inline-block;
        }
        .status-pending {
            background: linear-gradient(to right, #f39c12, #e67e22);
            color: white;
        }
        .status-verified {
            background: linear-gradient(to right, #2ecc71, #27ae60);
            color: white;
        }
        .status-rejected {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        .btn-primary {
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            color: white;
        }
        .btn-success {
            background: linear-gradient(to right, #2ecc71, #27ae60);
            color: white;
        }
        .btn-danger {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 30px;
            font-size: 14px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 2px 10px rgba(76, 175, 80, 0.2);
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        .modal-content {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 20px;
            width: 500px;
            max-width: 90%;
            padding: 30px;
            box-shadow: 0 15px 50px rgba(0,0,0,0.2);
            border: 1px solid rgba(0,0,0,0.05);
        }
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .modal-title {
            font-size: 18px;
            font-weight: bold;
        }
        .close-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
        }
        .modal-footer {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
        }
        .modal-footer button {
            margin-left: 10px;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="demo-header">
        <div class="header-content">
            <div class="logo">碳排放管理系统</div>
            <div class="user-info">
                <span class="user-name">北京碳排放科技有限公司</span>
                <a href="/login" class="logout-btn">退出登录</a>
            </div>
        </div>
    </div>

    <div class="demo-nav">
        <div class="nav-content">
            <a href="/demo" class="nav-item active"><i class="fas fa-tachometer-alt mr-2"></i>仪表板</a>
            <a href="/emissions" class="nav-item"><i class="fas fa-cloud mr-2"></i>排放数据</a>
            <a href="/verifications" class="nav-item"><i class="fas fa-clipboard-check mr-2"></i>核查记录</a>
            <a href="/transactions" class="nav-item"><i class="fas fa-exchange-alt mr-2"></i>碳交易</a>
            <a href="/calculator" class="nav-item"><i class="fas fa-calculator mr-2"></i>碳计算器</a>
            <a href="/predictions" class="nav-item"><i class="fas fa-chart-line mr-2"></i>预测分析</a>
            <a href="/reports" class="nav-item"><i class="fas fa-file-alt mr-2"></i>报告生成</a>
            <a href="/enterprise_blockchain_config" class="nav-item"><i class="fas fa-link mr-2"></i>区块链配置</a>
        </div>
    </div>

    <div class="container">
        <h1>企业仪表板</h1>

        <div class="dashboard">
            <div class="card stat-card">
                <div class="stat-label">总排放量</div>
                <div class="stat-value">2,450</div>
                <div class="stat-label">吨CO2e</div>
            </div>
            <div class="card stat-card">
                <div class="stat-label">当前配额</div>
                <div class="stat-value">3,200</div>
                <div class="stat-label">吨CO2e</div>
            </div>
            <div class="card stat-card">
                <div class="stat-label">核查通过率</div>
                <div class="stat-value">85%</div>
                <div class="stat-label">本年度</div>
            </div>
        </div>

        <div class="card">
            <div class="card-title">排放数据趋势</div>
            <div class="chart-container">
                <!-- 使用内联SVG代替外部图片，确保图表始终能够显示 -->
                <svg width="100%" height="100%" viewBox="0 0 1160 300" xmlns="http://www.w3.org/2000/svg">
                    <!-- 背景网格 -->
                    <g stroke="#eee" stroke-width="1">
                        <!-- 水平线 -->
                        <line x1="50" y1="50" x2="1100" y2="50" />
                        <line x1="50" y1="100" x2="1100" y2="100" />
                        <line x1="50" y1="150" x2="1100" y2="150" />
                        <line x1="50" y1="200" x2="1100" y2="200" />
                        <line x1="50" y1="250" x2="1100" y2="250" />
                        <!-- 垂直线 -->
                        <line x1="50" y1="50" x2="50" y2="250" />
                        <line x1="150" y1="50" x2="150" y2="250" />
                        <line x1="250" y1="50" x2="250" y2="250" />
                        <line x1="350" y1="50" x2="350" y2="250" />
                        <line x1="450" y1="50" x2="450" y2="250" />
                        <line x1="550" y1="50" x2="550" y2="250" />
                        <line x1="650" y1="50" x2="650" y2="250" />
                        <line x1="750" y1="50" x2="750" y2="250" />
                        <line x1="850" y1="50" x2="850" y2="250" />
                        <line x1="950" y1="50" x2="950" y2="250" />
                        <line x1="1050" y1="50" x2="1050" y2="250" />
                    </g>

                    <!-- 坐标轴 -->
                    <g stroke="#333" stroke-width="2">
                        <line x1="50" y1="250" x2="1100" y2="250" />
                        <line x1="50" y1="50" x2="50" y2="250" />
                    </g>

                    <!-- 坐标轴标签 -->
                    <g fill="#333" font-size="12" text-anchor="middle">
                        <!-- X轴标签 -->
                        <text x="100" y="270">1月</text>
                        <text x="200" y="270">2月</text>
                        <text x="300" y="270">3月</text>
                        <text x="400" y="270">4月</text>
                        <text x="500" y="270">5月</text>
                        <text x="600" y="270">6月</text>
                        <text x="700" y="270">7月</text>
                        <text x="800" y="270">8月</text>
                        <text x="900" y="270">9月</text>
                        <text x="1000" y="270">10月</text>
                        <text x="1100" y="270">11月</text>

                        <!-- Y轴标签 -->
                        <text x="35" y="250" text-anchor="end">0</text>
                        <text x="35" y="200" text-anchor="end">200</text>
                        <text x="35" y="150" text-anchor="end">400</text>
                        <text x="35" y="100" text-anchor="end">600</text>
                        <text x="35" y="50" text-anchor="end">800</text>
                    </g>

                    <!-- 图表标题 -->
                    <text x="580" y="30" fill="#2E7D32" font-size="16" font-weight="bold" text-anchor="middle">月度碳排放趋势 (吨CO2e)</text>

                    <!-- 数据点 -->
                    <g fill="#4CAF50">
                        <circle cx="100" cy="150" r="5" />
                        <circle cx="200" cy="170" r="5" />
                        <circle cx="300" cy="130" r="5" />
                        <circle cx="400" cy="110" r="5" />
                        <circle cx="500" cy="120" r="5" />
                        <circle cx="600" cy="100" r="5" />
                        <circle cx="700" cy="90" r="5" />
                        <circle cx="800" cy="80" r="5" />
                        <circle cx="900" cy="70" r="5" />
                        <circle cx="1000" cy="60" r="5" />
                    </g>

                    <!-- 折线 -->
                    <polyline
                        points="100,150 200,170 300,130 400,110 500,120 600,100 700,90 800,80 900,70 1000,60"
                        fill="none"
                        stroke="#4CAF50"
                        stroke-width="3"
                    />

                    <!-- 区域填充 -->
                    <path
                        d="M100,150 L200,170 L300,130 L400,110 L500,120 L600,100 L700,90 L800,80 L900,70 L1000,60 L1000,250 L100,250 Z"
                        fill="url(#gradient)"
                        opacity="0.3"
                    />

                    <!-- 渐变定义 -->
                    <defs>
                        <linearGradient id="gradient" x1="0%" y1="0%" x2="0%" y2="100%">
                            <stop offset="0%" stop-color="#4CAF50" />
                            <stop offset="100%" stop-color="#ffffff" />
                        </linearGradient>
                    </defs>

                    <!-- 图例 -->
                    <rect x="900" y="20" width="15" height="15" fill="#4CAF50" />
                    <text x="925" y="33" fill="#333" font-size="12">实际排放量</text>
                </svg>
            </div>
        </div>

        <div class="card">
            <div class="card-title">
                <span>最近排放数据</span>
                <a href="#" class="btn btn-primary submit-emission-btn">提交新数据</a>
            </div>
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>排放源</th>
                        <th>排放量</th>
                        <th>计算方法</th>
                        <th>排放期间</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1001</td>
                        <td>燃煤锅炉</td>
                        <td>450 吨CO2e</td>
                        <td>排放因子法</td>
                        <td>2025-01-01 至 2025-01-31</td>
                        <td><span class="status status-verified">已核查</span></td>
                        <td><a href="/emission_detail?id=1001" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>1002</td>
                        <td>天然气锅炉</td>
                        <td>320 吨CO2e</td>
                        <td>排放因子法</td>
                        <td>2025-02-01 至 2025-02-28</td>
                        <td><span class="status status-pending">待核查</span></td>
                        <td><a href="/emission_detail?id=1002" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>1003</td>
                        <td>工业生产过程</td>
                        <td>780 吨CO2e</td>
                        <td>物料平衡法</td>
                        <td>2025-03-01 至 2025-03-31</td>
                        <td><span class="status status-rejected">已拒绝</span></td>
                        <td><a href="/emission_detail?id=1003" class="btn btn-primary">查看</a></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="card">
            <div class="card-title">最近交易记录</div>
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>交易方</th>
                        <th>数量</th>
                        <th>价格</th>
                        <th>总价</th>
                        <th>交易时间</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>2001</td>
                        <td>上海绿色能源有限公司</td>
                        <td>200 吨</td>
                        <td>45 元/吨</td>
                        <td>9,000 元</td>
                        <td>2025-04-15</td>
                        <td><span class="status status-verified">已完成</span></td>
                        <td><a href="/transaction_detail?id=2001" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>2002</td>
                        <td>广州环保科技有限公司</td>
                        <td>150 吨</td>
                        <td>50 元/吨</td>
                        <td>7,500 元</td>
                        <td>2025-05-20</td>
                        <td><span class="status status-pending">待确认</span></td>
                        <td><a href="/transaction_detail?id=2002" class="btn btn-primary">查看</a></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- 模态框示例 -->
    <div class="modal" id="emissionModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">提交排放数据</div>
                <button class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="emission-source">排放源</label>
                    <select id="emission-source">
                        <option value="">请选择排放源</option>
                        <option value="coal">燃煤锅炉</option>
                        <option value="gas">天然气锅炉</option>
                        <option value="process">工业生产过程</option>
                        <option value="transport">交通运输</option>
                        <option value="electricity">电力消耗</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="emission-amount">排放量</label>
                    <input type="number" id="emission-amount" placeholder="请输入排放量">
                </div>
                <div class="form-group">
                    <label for="emission-unit">排放单位</label>
                    <select id="emission-unit">
                        <option value="tCO2e">吨CO2e</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="calculation-method">计算方法</label>
                    <select id="calculation-method">
                        <option value="">请选择计算方法</option>
                        <option value="factor">排放因子法</option>
                        <option value="balance">物料平衡法</option>
                        <option value="monitor">连续监测法</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="period-start">排放期间开始</label>
                    <input type="date" id="period-start">
                </div>
                <div class="form-group">
                    <label for="period-end">排放期间结束</label>
                    <input type="date" id="period-end">
                </div>
                <div class="form-group">
                    <label for="proof-file">证明文件</label>
                    <input type="file" id="proof-file">
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-danger">取消</button>
                <button class="btn btn-primary">提交</button>
            </div>
        </div>
    </div>

    <script>
        // 简单的演示脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 导航切换 - 不需要额外的事件监听器，因为我们使用的是链接
            // 根据当前URL设置活动导航项
            const currentPath = window.location.pathname;
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                if (item.getAttribute('href') === currentPath) {
                    navItems.forEach(i => i.classList.remove('active'));
                    item.classList.add('active');
                }
            });

            // 模态框操作
            const modal = document.getElementById('emissionModal');
            const closeBtn = modal.querySelector('.close-btn');
            const cancelBtn = modal.querySelector('.btn-danger');

            // 关闭模态框
            function closeModal() {
                modal.style.display = 'none';
            }

            closeBtn.addEventListener('click', closeModal);
            cancelBtn.addEventListener('click', closeModal);

            // 点击"提交新数据"按钮时打开模态框（演示用）
            const submitBtn = document.querySelector('.submit-emission-btn');
            if (submitBtn) {
                submitBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    modal.style.display = 'flex';
                });
            }
        });
    </script>
{% endblock %}
