<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放核查系统 - 区块链配置</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <style>
        .gradient-custom {
            background: linear-gradient(135deg, #43a047 0%, #1de9b6 100%);
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            border-radius: 15px 15px 0 0;
            background: linear-gradient(135deg, #43a047 0%, #1de9b6 100%);
            color: white;
        }
        .btn-success {
            background: linear-gradient(135deg, #43a047 0%, #1de9b6 100%);
            border: none;
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #388e3c 0%, #00bfa5 100%);
        }
        .form-control:focus {
            border-color: #43a047;
            box-shadow: 0 0 0 0.25rem rgba(67, 160, 71, 0.25);
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container">
            <a class="navbar-brand" href="/"><i class="fas fa-leaf me-2"></i>碳排放核查系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise"><i class="fas fa-tachometer-alt me-1"></i>仪表板</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise/emissions"><i class="fas fa-cloud me-1"></i>排放数据</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise/quotas"><i class="fas fa-chart-pie me-1"></i>配额管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise/transactions"><i class="fas fa-exchange-alt me-1"></i>交易记录</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise/reports"><i class="fas fa-file-alt me-1"></i>报告管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/enterprise/blockchain/config"><i class="fas fa-link me-1"></i>区块链配置</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>{{ current_user.get('username', '企业用户') }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="/enterprise/profile"><i class="fas fa-id-card me-1"></i>企业资料</a></li>
                            <li><a class="dropdown-item" href="/enterprise/settings"><i class="fas fa-cog me-1"></i>账户设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-1"></i>退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mt-4">
        <!-- 消息提示 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-link me-2"></i>区块链配置</h1>
            <div>
                <span class="badge bg-primary">当前时间: <span id="current-time"></span></span>
            </div>
        </div>

        <!-- 区块链状态 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header gradient-custom">
                        <h5 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i>区块链状态</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                连接状态
                                {% if blockchain_status and blockchain_status.connected %}
                                    <span class="badge bg-success rounded-pill"><i class="fas fa-check"></i> 已连接</span>
                                {% else %}
                                    <span class="badge bg-warning rounded-pill"><i class="fas fa-exclamation-triangle"></i> 模拟模式</span>
                                {% endif %}
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                网络
                                <span class="badge bg-primary rounded-pill">{{ blockchain_status.network|default('本地测试网') }}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                当前区块
                                <span class="badge bg-info rounded-pill">{{ blockchain_status.block_number|default('未知') }}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                智能合约地址
                                <span class="text-muted">{{ blockchain_status.contract_address|default('未部署') }}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                企业区块链地址
                                <span class="text-muted">{{ blockchain_status.enterprise_address|default('未配置') }}</span>
                            </li>
                        </ul>
                    </div>
                    <div class="card-footer bg-light">
                        <button type="button" class="btn btn-success" id="refreshStatusBtn">
                            <i class="fas fa-sync-alt me-1"></i>刷新状态
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header gradient-custom">
                        <h5 class="card-title mb-0"><i class="fas fa-cog me-2"></i>区块链配置</h5>
                    </div>
                    <div class="card-body">
                        <form id="blockchainConfigForm">
                            <div class="mb-3">
                                <label for="ethereumNodeUrl" class="form-label">以太坊节点URL</label>
                                <input type="text" class="form-control" id="ethereumNodeUrl" name="ethereum_node_url" value="{{ blockchain_config.ethereum_node_url|default('http://127.0.0.1:8545') }}" required>
                                <div class="form-text">例如: http://127.0.0.1:8545 (本地Ganache)</div>
                            </div>
                            <div class="mb-3">
                                <label for="privateKey" class="form-label">企业私钥</label>
                                <input type="password" class="form-control" id="privateKey" name="private_key" value="{{ blockchain_config.private_key|default('') }}">
                                <div class="form-text">用于签署交易的私钥 (请勿在生产环境中明文存储)</div>
                            </div>
                            <div class="mb-3">
                                <label for="contractAddress" class="form-label">智能合约地址</label>
                                <input type="text" class="form-control" id="contractAddress" name="contract_address" value="{{ blockchain_config.contract_address|default('') }}">
                                <div class="form-text">已部署的碳排放智能合约地址</div>
                            </div>
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="simulationMode" name="simulation_mode" {% if blockchain_config.simulation_mode|default(true) %}checked{% endif %}>
                                <label class="form-check-label" for="simulationMode">启用模拟模式 (无需真实区块链)</label>
                            </div>
                        </form>
                    </div>
                    <div class="card-footer bg-light">
                        <button type="button" class="btn btn-success" id="saveConfigBtn">
                            <i class="fas fa-save me-1"></i>保存配置
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 区块链交易记录 -->
        <div class="card mb-4">
            <div class="card-header gradient-custom">
                <h5 class="card-title mb-0"><i class="fas fa-history me-2"></i>最近区块链交易记录</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>交易哈希</th>
                                <th>类型</th>
                                <th>区块号</th>
                                <th>时间</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if blockchain_transactions %}
                                {% for tx in blockchain_transactions %}
                                    <tr>
                                        <td><code>{{ tx.hash }}</code></td>
                                        <td>{{ tx.type }}</td>
                                        <td>{{ tx.block_number }}</td>
                                        <td>{{ tx.timestamp }}</td>
                                        <td>
                                            {% if tx.status == 'success' %}
                                                <span class="badge bg-success">成功</span>
                                            {% elif tx.status == 'pending' %}
                                                <span class="badge bg-warning">处理中</span>
                                            {% else %}
                                                <span class="badge bg-danger">失败</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="5" class="text-center">暂无交易记录</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 区块链操作 -->
        <div class="card mb-4">
            <div class="card-header gradient-custom">
                <h5 class="card-title mb-0"><i class="fas fa-tools me-2"></i>区块链操作</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="d-grid">
                            <button type="button" class="btn btn-success" id="testConnectionBtn">
                                <i class="fas fa-plug me-2"></i>测试连接
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="d-grid">
                            <button type="button" class="btn btn-success" id="registerAddressBtn">
                                <i class="fas fa-user-plus me-2"></i>注册企业地址
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="d-grid">
                            <button type="button" class="btn btn-success" id="viewBalanceBtn">
                                <i class="fas fa-wallet me-2"></i>查看账户余额
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">© 2025 碳排放核查系统 | 基于区块链技术</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/api.js') }}"></script>
    <script>
        // 显示当前时间
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleString('zh-CN');
        }
        updateTime();
        setInterval(updateTime, 1000);

        // 刷新状态按钮点击事件
        document.getElementById('refreshStatusBtn').addEventListener('click', function() {
            API.getBlockchainConfig()
                .then(response => {
                    if (response.success) {
                        alert('区块链状态已刷新');
                        location.reload();
                    } else {
                        alert('刷新失败: ' + response.message);
                    }
                })
                .catch(error => {
                    alert('刷新失败: ' + error.message);
                });
        });

        // 保存配置按钮点击事件
        document.getElementById('saveConfigBtn').addEventListener('click', function() {
            const form = document.getElementById('blockchainConfigForm');
            const formData = new FormData(form);
            const configData = {
                ethereum_node_url: formData.get('ethereum_node_url'),
                private_key: formData.get('private_key'),
                contract_address: formData.get('contract_address'),
                simulation_mode: formData.get('simulation_mode') === 'on'
            };

            API.updateBlockchainConfig(configData)
                .then(response => {
                    if (response.success) {
                        alert('配置已保存');
                        location.reload();
                    } else {
                        alert('保存失败: ' + response.message);
                    }
                })
                .catch(error => {
                    alert('保存失败: ' + error.message);
                });
        });

        // 测试连接按钮点击事件
        document.getElementById('testConnectionBtn').addEventListener('click', function() {
            document.getElementById('testConnectionBtn').disabled = true;
            document.getElementById('testConnectionBtn').innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>测试中...';

            API.testBlockchainConnection()
                .then(response => {
                    if (response.success) {
                        alert(`连接成功!\n网络ID: ${response.network_id}\n当前区块: ${response.block_number}`);
                    } else {
                        alert('连接失败: ' + response.message);
                    }
                })
                .catch(error => {
                    alert('连接测试失败: ' + error.message);
                })
                .finally(() => {
                    document.getElementById('testConnectionBtn').disabled = false;
                    document.getElementById('testConnectionBtn').innerHTML = '<i class="fas fa-plug me-2"></i>测试连接';
                });
        });

        // 注册企业地址按钮点击事件
        document.getElementById('registerAddressBtn').addEventListener('click', function() {
            alert('正在注册企业地址...');
            // 这里可以添加注册企业地址的API调用
        });

        // 查看账户余额按钮点击事件
        document.getElementById('viewBalanceBtn').addEventListener('click', function() {
            alert('正在查询账户余额...');
            // 这里可以添加查询账户余额的API调用
        });
    </script>
</body>
</html>
