"""
核查相关路由
"""

import hashlib
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity

from backend import db
from backend.models.user import User
from backend.models.emission import EmissionData
from backend.models.verification import Verification
from backend.models.activity import Activity
from backend.utils.auth import verifier_required

verification_bp = Blueprint('verification', __name__)

@verification_bp.route('/tasks', methods=['GET'])
@verifier_required
def get_verification_tasks():
    # 获取所有待核查的排放数据
    tasks = EmissionData.query.filter_by(status='pending').all()
    return jsonify({
        'tasks': [task.to_dict() for task in tasks]
    }), 200

@verification_bp.route('/emissions/<int:emission_id>', methods=['POST'])
@verifier_required
def verify_emission(emission_id):
    emission = EmissionData.query.get(emission_id)
    if not emission:
        return jsonify({'error': '排放数据不存在'}), 404

    # 检查状态
    if emission.status != 'pending':
        return jsonify({'error': '只能核查待核查的排放数据'}), 400

    data = request.get_json()

    # 验证必填字段
    required_fields = ['conclusion', 'comments']
    for field in required_fields:
        if field not in data:
            return jsonify({'error': f'缺少必填字段: {field}'}), 400

    # 验证结论
    valid_conclusions = ['approved', 'rejected']
    if data['conclusion'] not in valid_conclusions:
        return jsonify({'error': f'无效的核查结论: {data["conclusion"]}'}), 400

    # 创建核查记录
    verification = Verification(
        emission_data_id=emission.id,
        verifier_id=get_jwt_identity(),
        conclusion=data['conclusion'],
        comments=data['comments'],
        verification_time=datetime.now()
    )

    db.session.add(verification)

    # 更新排放数据状态
    emission.status = 'verified' if data['conclusion'] == 'approved' else 'rejected'

    db.session.commit()

    # 生成哈希值
    hash_value = hashlib.sha256(f"{verification.id}_{verification.emission_data_id}_{verification.verifier_id}_{verification.conclusion}_{verification.verification_time.isoformat()}".encode()).hexdigest()

    # 提交到区块链
    blockchain_result = current_app.blockchain_client.submit_verification_result(
        verification.id,
        verification.emission_data_id,
        verification.verifier_id,
        verification.conclusion,
        verification.verification_time.isoformat(),
        hash_value
    )

    # 更新区块链信息
    verification.blockchain_hash = blockchain_result['tx_hash']
    verification.blockchain_block = blockchain_result['block_number']
    db.session.commit()

    # 记录活动
    activity = Activity(
        user_id=get_jwt_identity(),
        activity_type='verify_emission',
        description=f'核查机构完成了排放数据核查，ID: {emission.id}，结论: {verification.conclusion}'
    )
    db.session.add(activity)
    db.session.commit()

    return jsonify({
        'message': '核查完成',
        'verification': verification.to_dict()
    }), 201

@verification_bp.route('/history', methods=['GET'])
@verifier_required
def get_verification_history():
    current_user_id = get_jwt_identity()

    # 获取当前核查机构的所有核查记录
    verifications = Verification.query.filter_by(verifier_id=current_user_id).all()
    return jsonify({
        'verifications': [verification.to_dict() for verification in verifications]
    }), 200

@verification_bp.route('/<int:verification_id>', methods=['GET'])
@jwt_required()
def get_verification(verification_id):
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)

    if not user:
        return jsonify({'error': '用户不存在'}), 404

    verification = Verification.query.get(verification_id)
    if not verification:
        return jsonify({'error': '核查记录不存在'}), 404

    # 检查权限
    if user.role == 'verifier' and verification.verifier_id != user.id:
        return jsonify({'error': '无权访问此核查记录'}), 403

    if user.role == 'enterprise':
        emission = EmissionData.query.get(verification.emission_data_id)
        if emission.enterprise_id != user.id:
            return jsonify({'error': '无权访问此核查记录'}), 403

    return jsonify({
        'verification': verification.to_dict()
    }), 200
