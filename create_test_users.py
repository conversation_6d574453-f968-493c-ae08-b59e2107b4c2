"""
创建测试用户
"""

import os
import sys
from datetime import datetime
import pymysql
from werkzeug.security import generate_password_hash

# 数据库连接信息
DB_HOST = '***********'
DB_PORT = 3306
DB_USER = 'wuhong'
DB_PASSWORD = 'D7mH8rZ7a7Z2kJa8'
DB_NAME = 'ces'

def get_db_connection():
    """获取数据库连接"""
    try:
        conn = pymysql.connect(
            host=DB_HOST,
            port=DB_PORT,
            user=DB_USER,
            password=DB_PASSWORD,
            database=DB_NAME,
            charset='utf8mb4'
        )
        return conn
    except Exception as e:
        print(f"数据库连接失败: {str(e)}")
        sys.exit(1)

def create_test_users():
    """创建测试用户"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 检查是否已存在用户
        cursor.execute("SELECT COUNT(*) FROM user")
        user_count = cursor.fetchone()[0]
        
        if user_count > 0:
            print(f"数据库中已存在 {user_count} 个用户，跳过创建测试用户")
            return
        
        # 测试用户数据
        test_users = [
            {
                'username': 'admin',
                'password': 'admin123',
                'email': '<EMAIL>',
                'role': 'admin',
                'company_name': '系统管理员',
                'contact_name': '管理员',
                'contact_phone': '13800000000',
                'address': '北京市海淀区',
                'blockchain_address': '0xFC2d0AD2AA3c85D397866772B03F5F6561c404EF'
            },
            {
                'username': 'enterprise1',
                'password': 'enterprise123',
                'email': '<EMAIL>',
                'role': 'enterprise',
                'company_name': '北京碳排放科技有限公司',
                'contact_name': '张三',
                'contact_phone': '13800000001',
                'address': '北京市朝阳区',
                'blockchain_address': '0x5B38Da6a701c568545dCfcB03FcB875f56beddC4'
            },
            {
                'username': 'enterprise2',
                'password': 'enterprise123',
                'email': '<EMAIL>',
                'role': 'enterprise',
                'company_name': '上海绿色能源有限公司',
                'contact_name': '李四',
                'contact_phone': '13800000002',
                'address': '上海市浦东新区',
                'blockchain_address': '0xAb8483F64d9C6d1EcF9b849Ae677dD3315835cb2'
            },
            {
                'username': 'enterprise3',
                'password': 'enterprise123',
                'email': '<EMAIL>',
                'role': 'enterprise',
                'company_name': '广州环保科技有限公司',
                'contact_name': '王五',
                'contact_phone': '13800000003',
                'address': '广州市天河区',
                'blockchain_address': '0x4B20993Bc481177ec7E8f571ceCaE8A9e22C02db'
            },
            {
                'username': 'verifier1',
                'password': 'verifier123',
                'email': '<EMAIL>',
                'role': 'verifier',
                'company_name': '北京碳核查认证中心',
                'contact_name': '赵六',
                'contact_phone': '13800000004',
                'address': '北京市西城区',
                'blockchain_address': '0x78731D3Ca6b7E34aC0F824c42a7cC18A495cabaB'
            },
            {
                'username': 'verifier2',
                'password': 'verifier123',
                'email': '<EMAIL>',
                'role': 'verifier',
                'company_name': '上海碳核查认证中心',
                'contact_name': '钱七',
                'contact_phone': '13800000005',
                'address': '上海市黄浦区',
                'blockchain_address': '0x617F2E2fD72FD9D5503197092aC168c91465E7f2'
            }
        ]
        
        # 插入用户数据
        for user in test_users:
            # 生成密码哈希
            password_hash = generate_password_hash(user['password'])
            
            # 插入用户
            cursor.execute(
                """
                INSERT INTO user 
                (username, password_hash, email, role, company_name, contact_name, contact_phone, address, blockchain_address, registration_time)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """,
                (
                    user['username'],
                    password_hash,
                    user['email'],
                    user['role'],
                    user['company_name'],
                    user['contact_name'],
                    user['contact_phone'],
                    user['address'],
                    user['blockchain_address'],
                    datetime.now()
                )
            )
        
        conn.commit()
        print(f"成功创建 {len(test_users)} 个测试用户")
    except Exception as e:
        conn.rollback()
        print(f"创建测试用户失败: {str(e)}")
    finally:
        cursor.close()
        conn.close()

def main():
    """主函数"""
    print("开始创建测试用户...")
    create_test_users()
    print("测试用户创建完成！")

if __name__ == "__main__":
    main()
