import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useAuth } from '../contexts/AuthContext';
import '../styles/CarbonTrading.css';

function CarbonTrading() {
  const [transactions, setTransactions] = useState([]);
  const [enterprises, setEnterprises] = useState([]);
  const [carbonQuotas, setCarbonQuotas] = useState([]);
  const [formData, setFormData] = useState({
    seller_id: '',
    amount: '',
    price: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const { user } = useAuth();

  useEffect(() => {
    fetchTransactions();
    fetchEnterprises();
    fetchCarbonQuotas();
  }, []);

  const fetchTransactions = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/transactions');
      setTransactions(response.data);
      setLoading(false);
    } catch (err) {
      setError('获取交易记录失败: ' + (err.response?.data?.error || err.message));
      setLoading(false);
    }
  };

  const fetchEnterprises = async () => {
    try {
      const response = await axios.get('/api/auth/enterprises');
      setEnterprises(response.data);
    } catch (err) {
      setError('获取企业列表失败: ' + (err.response?.data?.error || err.message));
    }
  };

  const fetchCarbonQuotas = async () => {
    try {
      const response = await axios.get('/api/transactions/quotas');
      setCarbonQuotas(response.data);
    } catch (err) {
      setError('获取碳配额信息失败: ' + (err.response?.data?.error || err.message));
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const response = await axios.post('/api/transactions', formData);
      setSuccess('交易创建成功！');
      setFormData({
        seller_id: '',
        amount: '',
        price: ''
      });
      fetchTransactions();
      fetchCarbonQuotas();
    } catch (err) {
      setError('创建交易失败: ' + (err.response?.data?.error || err.message));
    } finally {
      setLoading(false);
    }
  };

  const confirmTransaction = async (transactionId) => {
    try {
      setLoading(true);
      await axios.post(`/api/transactions/${transactionId}/confirm`);
      setSuccess('交易确认成功！');
      fetchTransactions();
      fetchCarbonQuotas();
    } catch (err) {
      setError('确认交易失败: ' + (err.response?.data?.error || err.message));
    } finally {
      setLoading(false);
    }
  };

  const cancelTransaction = async (transactionId) => {
    try {
      setLoading(true);
      await axios.post(`/api/transactions/${transactionId}/cancel`);
      setSuccess('交易取消成功！');
      fetchTransactions();
    } catch (err) {
      setError('取消交易失败: ' + (err.response?.data?.error || err.message));
    } finally {
      setLoading(false);
    }
  };

  // 获取当前用户的碳配额
  const getCurrentUserQuota = () => {
    const quota = carbonQuotas.find(q => q.enterprise_id === user?.id);
    return quota ? quota.current_amount : 0;
  };

  // 获取企业名称
  const getEnterpriseName = (id) => {
    const enterprise = enterprises.find(e => e.id === id);
    return enterprise ? enterprise.company_name : `企业 #${id}`;
  };

  return (
    <div className="carbon-trading">
      <h2>碳配额交易</h2>
      
      {error && <div className="error-message">{error}</div>}
      {success && <div className="success-message">{success}</div>}
      
      <div className="trading-container">
        <div className="quota-info">
          <h3>我的碳配额</h3>
          <div className="quota-card">
            <div className="quota-amount">{getCurrentUserQuota()} <span>吨</span></div>
            <div className="quota-label">当前可用配额</div>
          </div>
        </div>
        
        <div className="trading-form">
          <h3>创建新交易</h3>
          <form onSubmit={handleSubmit}>
            <div className="form-group">
              <label>卖方企业</label>
              <select 
                name="seller_id" 
                value={formData.seller_id}
                onChange={handleChange}
                required
              >
                <option value="">选择企业</option>
                {enterprises.map(enterprise => (
                  <option key={enterprise.id} value={enterprise.id}>
                    {enterprise.company_name}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="form-group">
              <label>交易数量 (吨)</label>
              <input
                type="number"
                name="amount"
                value={formData.amount}
                onChange={handleChange}
                min="1"
                step="0.01"
                required
              />
            </div>
            
            <div className="form-group">
              <label>单价 (元/吨)</label>
              <input
                type="number"
                name="price"
                value={formData.price}
                onChange={handleChange}
                min="1"
                step="0.01"
                required
              />
            </div>
            
            <div className="form-group total-price">
              <label>总价 (元)</label>
              <div className="calculated-value">
                {(formData.amount && formData.price) 
                  ? (parseFloat(formData.amount) * parseFloat(formData.price)).toFixed(2) 
                  : '0.00'}
              </div>
            </div>
            
            <button type="submit" className="submit-btn" disabled={loading}>
              {loading ? '提交中...' : '创建交易'}
            </button>
          </form>
        </div>
      </div>
      
      <div className="transactions-list">
        <h3>交易记录</h3>
        
        {loading ? (
          <div className="loading">加载中...</div>
        ) : transactions.length > 0 ? (
          <table>
            <thead>
              <tr>
                <th>交易ID</th>
                <th>交易方</th>
                <th>数量 (吨)</th>
                <th>单价 (元/吨)</th>
                <th>总价 (元)</th>
                <th>状态</th>
                <th>交易时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              {transactions.map(transaction => (
                <tr key={transaction.id} className={`status-${transaction.status}`}>
                  <td>{transaction.id}</td>
                  <td>
                    {transaction.buyer_id === user?.id 
                      ? `买入自: ${transaction.seller_name || getEnterpriseName(transaction.seller_id)}`
                      : `卖出给: ${transaction.buyer_name || getEnterpriseName(transaction.buyer_id)}`
                    }
                  </td>
                  <td>{transaction.amount}</td>
                  <td>{transaction.price}</td>
                  <td>{transaction.total_price}</td>
                  <td>
                    <span className={`status-badge ${transaction.status}`}>
                      {transaction.status === 'pending' ? '待确认' : 
                       transaction.status === 'completed' ? '已完成' : '已取消'}
                    </span>
                  </td>
                  <td>{new Date(transaction.transaction_time).toLocaleString()}</td>
                  <td>
                    {transaction.status === 'pending' && (
                      <div className="action-buttons">
                        {transaction.seller_id === user?.id && (
                          <button 
                            className="confirm-btn"
                            onClick={() => confirmTransaction(transaction.id)}
                          >
                            确认
                          </button>
                        )}
                        <button 
                          className="cancel-btn"
                          onClick={() => cancelTransaction(transaction.id)}
                        >
                          取消
                        </button>
                      </div>
                    )}
                    {transaction.status !== 'pending' && (
                      <span className="no-actions">无可用操作</span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <div className="no-data">暂无交易记录</div>
        )}
      </div>
      
      <div className="market-info">
        <h3>市场信息</h3>
        <div className="market-stats">
          <div className="stat-card">
            <div className="stat-value">{transactions.length}</div>
            <div className="stat-label">总交易数</div>
          </div>
          <div className="stat-card">
            <div className="stat-value">
              {transactions.filter(t => t.status === 'completed').length}
            </div>
            <div className="stat-label">已完成交易</div>
          </div>
          <div className="stat-card">
            <div className="stat-value">
              {transactions.filter(t => t.status === 'completed')
                .reduce((sum, t) => sum + t.amount, 0).toFixed(2)}
            </div>
            <div className="stat-label">总交易量 (吨)</div>
          </div>
          <div className="stat-card">
            <div className="stat-value">
              {transactions.length > 0 
                ? (transactions.filter(t => t.status === 'completed')
                    .reduce((sum, t) => sum + t.price, 0) / 
                   transactions.filter(t => t.status === 'completed').length).toFixed(2)
                : '0.00'}
            </div>
            <div className="stat-label">平均价格 (元/吨)</div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default CarbonTrading;
