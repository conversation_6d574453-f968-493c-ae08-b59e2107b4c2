"""
集成测试脚本
测试系统的完整功能和区块链集成
"""

import os
import sys
import time
import json
import requests
import logging
from dotenv import load_dotenv

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('integration_test')

# 加载环境变量
load_dotenv()

# 系统API基础URL
BASE_URL = 'http://localhost:5000/api'

# 测试用户凭证
ADMIN_CREDENTIALS = {
    'username': 'admin',
    'password': 'admin123'
}

ENTERPRISE_CREDENTIALS = {
    'username': 'enterprise1',
    'password': 'password123'
}

VERIFIER_CREDENTIALS = {
    'username': 'verifier1',
    'password': 'password123'
}

# 存储测试过程中创建的资源ID
test_resources = {
    'emission_id': None,
    'verification_id': None,
    'transaction_id': None,
    'penalty_id': None,
    'tokens': {}
}

def login(credentials):
    """登录并获取JWT令牌"""
    response = requests.post(f'{BASE_URL}/auth/login', json=credentials)
    if response.status_code == 200:
        token = response.json().get('access_token')
        test_resources['tokens'][credentials['username']] = token
        logger.info(f"用户 {credentials['username']} 登录成功")
        return token
    else:
        logger.error(f"用户 {credentials['username']} 登录失败: {response.text}")
        return None

def get_headers(username):
    """获取带有JWT令牌的请求头"""
    token = test_resources['tokens'].get(username)
    if not token:
        token = login({'username': username, 'password': 'password123'})
    
    return {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }

def test_blockchain_connection():
    """测试区块链连接"""
    logger.info("测试区块链连接...")
    
    # 登录管理员
    headers = get_headers('admin')
    
    # 获取区块链状态
    response = requests.get(f'{BASE_URL}/blockchain/status', headers=headers)
    
    if response.status_code == 200:
        status = response.json()
        logger.info(f"区块链连接状态: {status}")
        
        if status.get('connected'):
            logger.info("区块链连接测试通过")
            return True
        else:
            logger.warning("区块链未连接")
            return False
    else:
        logger.error(f"获取区块链状态失败: {response.text}")
        return False

def test_emission_data_submission():
    """测试排放数据提交"""
    logger.info("测试排放数据提交...")
    
    # 登录企业用户
    headers = get_headers('enterprise1')
    
    # 提交排放数据
    emission_data = {
        'emission_source': '工厂A',
        'emission_amount': 1000.5,
        'calculation_method': '直接测量法',
        'emission_time': '2025-01-15T08:00:00',
        'notes': '测试排放数据'
    }
    
    response = requests.post(f'{BASE_URL}/emissions', json=emission_data, headers=headers)
    
    if response.status_code == 201:
        result = response.json()
        emission_id = result.get('id')
        test_resources['emission_id'] = emission_id
        logger.info(f"排放数据提交成功，ID: {emission_id}")
        
        # 等待区块链确认
        time.sleep(5)
        
        # 验证数据是否上链
        response = requests.get(f'{BASE_URL}/emissions/{emission_id}', headers=headers)
        if response.status_code == 200:
            emission = response.json()
            if emission.get('blockchain_hash'):
                logger.info(f"排放数据已上链，交易哈希: {emission.get('blockchain_hash')}")
                return True
            else:
                logger.warning("排放数据未上链")
                return False
        else:
            logger.error(f"获取排放数据失败: {response.text}")
            return False
    else:
        logger.error(f"排放数据提交失败: {response.text}")
        return False

def test_verification_submission():
    """测试核查结果提交"""
    logger.info("测试核查结果提交...")
    
    if not test_resources.get('emission_id'):
        logger.error("没有可用的排放数据ID，无法测试核查结果提交")
        return False
    
    # 登录核查机构
    headers = get_headers('verifier1')
    
    # 提交核查结果
    verification_data = {
        'emission_data_id': test_resources['emission_id'],
        'conclusion': 'approved',
        'comments': '数据符合标准',
        'verification_time': '2025-01-20T10:00:00'
    }
    
    response = requests.post(f'{BASE_URL}/verifications', json=verification_data, headers=headers)
    
    if response.status_code == 201:
        result = response.json()
        verification_id = result.get('id')
        test_resources['verification_id'] = verification_id
        logger.info(f"核查结果提交成功，ID: {verification_id}")
        
        # 等待区块链确认
        time.sleep(5)
        
        # 验证数据是否上链
        response = requests.get(f'{BASE_URL}/verifications/{verification_id}', headers=headers)
        if response.status_code == 200:
            verification = response.json()
            if verification.get('blockchain_hash'):
                logger.info(f"核查结果已上链，交易哈希: {verification.get('blockchain_hash')}")
                return True
            else:
                logger.warning("核查结果未上链")
                return False
        else:
            logger.error(f"获取核查结果失败: {response.text}")
            return False
    else:
        logger.error(f"核查结果提交失败: {response.text}")
        return False

def test_transaction_creation():
    """测试交易创建"""
    logger.info("测试交易创建...")
    
    # 登录企业用户
    headers = get_headers('enterprise1')
    
    # 创建交易
    transaction_data = {
        'seller_id': 2,  # 假设ID为2的企业是卖方
        'amount': 500,
        'price': 20.5,
        'transaction_time': '2025-01-25T14:00:00',
        'notes': '测试交易'
    }
    
    response = requests.post(f'{BASE_URL}/transactions', json=transaction_data, headers=headers)
    
    if response.status_code == 201:
        result = response.json()
        transaction_id = result.get('id')
        test_resources['transaction_id'] = transaction_id
        logger.info(f"交易创建成功，ID: {transaction_id}")
        
        # 等待区块链确认
        time.sleep(5)
        
        # 验证数据是否上链
        response = requests.get(f'{BASE_URL}/transactions/{transaction_id}', headers=headers)
        if response.status_code == 200:
            transaction = response.json()
            if transaction.get('blockchain_hash'):
                logger.info(f"交易已上链，交易哈希: {transaction.get('blockchain_hash')}")
                return True
            else:
                logger.warning("交易未上链")
                return False
        else:
            logger.error(f"获取交易失败: {response.text}")
            return False
    else:
        logger.error(f"交易创建失败: {response.text}")
        return False

def test_penalty_creation():
    """测试惩罚记录创建"""
    logger.info("测试惩罚记录创建...")
    
    # 登录管理员
    headers = get_headers('admin')
    
    # 创建惩罚记录
    penalty_data = {
        'enterprise_id': 1,  # 假设ID为1的企业
        'amount': 1000,
        'reason': '排放数据造假',
        'penalty_time': '2025-01-30T09:00:00',
        'status': 'pending'
    }
    
    response = requests.post(f'{BASE_URL}/penalties', json=penalty_data, headers=headers)
    
    if response.status_code == 201:
        result = response.json()
        penalty_id = result.get('id')
        test_resources['penalty_id'] = penalty_id
        logger.info(f"惩罚记录创建成功，ID: {penalty_id}")
        
        # 等待区块链确认
        time.sleep(5)
        
        # 验证数据是否上链
        response = requests.get(f'{BASE_URL}/penalties/{penalty_id}', headers=headers)
        if response.status_code == 200:
            penalty = response.json()
            if penalty.get('blockchain_hash'):
                logger.info(f"惩罚记录已上链，交易哈希: {penalty.get('blockchain_hash')}")
                return True
            else:
                logger.warning("惩罚记录未上链")
                return False
        else:
            logger.error(f"获取惩罚记录失败: {response.text}")
            return False
    else:
        logger.error(f"惩罚记录创建失败: {response.text}")
        return False

def test_blockchain_verification():
    """测试区块链验证功能"""
    logger.info("测试区块链验证功能...")
    
    if not test_resources.get('emission_id'):
        logger.error("没有可用的排放数据ID，无法测试区块链验证")
        return False
    
    # 登录企业用户
    headers = get_headers('enterprise1')
    
    # 验证排放数据
    response = requests.get(
        f'{BASE_URL}/blockchain/verify?data_id={test_resources["emission_id"]}&data_type=emission',
        headers=headers
    )
    
    if response.status_code == 200:
        result = response.json()
        verified = result.get('verified')
        logger.info(f"排放数据验证结果: {verified}")
        
        if verified:
            logger.info("区块链验证测试通过")
            return True
        else:
            logger.warning(f"数据验证失败: {result.get('message')}")
            return False
    else:
        logger.error(f"区块链验证请求失败: {response.text}")
        return False

def run_all_tests():
    """运行所有测试"""
    logger.info("开始运行集成测试...")
    
    # 登录所有测试用户
    login(ADMIN_CREDENTIALS)
    login(ENTERPRISE_CREDENTIALS)
    login(VERIFIER_CREDENTIALS)
    
    # 运行测试
    tests = [
        test_blockchain_connection,
        test_emission_data_submission,
        test_verification_submission,
        test_transaction_creation,
        test_penalty_creation,
        test_blockchain_verification
    ]
    
    results = {}
    for test in tests:
        test_name = test.__name__
        try:
            result = test()
            results[test_name] = result
        except Exception as e:
            logger.error(f"测试 {test_name} 发生异常: {str(e)}")
            results[test_name] = False
    
    # 输出测试结果
    logger.info("\n测试结果汇总:")
    for test_name, result in results.items():
        status = "通过" if result else "失败"
        logger.info(f"{test_name}: {status}")
    
    # 计算通过率
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    pass_rate = (passed / total) * 100 if total > 0 else 0
    
    logger.info(f"\n测试通过率: {pass_rate:.2f}% ({passed}/{total})")
    
    return pass_rate == 100

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
