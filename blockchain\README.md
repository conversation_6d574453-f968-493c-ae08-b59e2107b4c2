# 区块链部署指南

本文档提供了如何使用Ganache部署和运行区块链部分的详细说明。

## 前提条件

1. 安装Ganache
   - 下载地址：https://trufflesuite.com/ganache/
   - 或者使用npm安装：`npm install -g ganache-cli`

2. 安装Python依赖
   ```bash
   pip install -r requirements.txt
   ```

3. 安装solc编译器（可选）
   - 如果您想要编译智能合约，需要安装py-solc-x
   ```bash
   pip install py-solc-x==1.1.1
   ```
   - 如果不安装，系统会使用预编译的合约文件

## 步骤1：启动Ganache

1. 启动Ganache应用程序或使用命令行启动
   ```bash
   ganache-cli -p 7545
   ```

2. 确保Ganache正在运行，默认端口为7545

## 步骤2：获取Ganache账户信息

1. 运行以下命令获取Ganache账户信息
   ```bash
   python blockchain/get_ganache_accounts.py
   ```

2. 记录输出的账户地址和私钥信息，用于配置`.env`文件

## 步骤3：配置环境变量

1. 复制`.env.example`文件为`.env`
   ```bash
   cp .env.example .env
   ```

2. 编辑`.env`文件，填写Ganache账户信息
   ```
   # 区块链配置 - Ganache
   ETHEREUM_NODE_URL=http://127.0.0.1:7545

   # 管理员账户
   ADMIN_PRIVATE_KEY=<Ganache中第一个账户的私钥>
   ADMIN_ADDRESS=<Ganache中第一个账户的地址>

   # 企业账户
   ENTERPRISE_1_KEY=<Ganache中第二个账户的私钥>
   ENTERPRISE_1_ADDRESS=<Ganache中第二个账户的地址>

   ENTERPRISE_2_KEY=<Ganache中第三个账户的私钥>
   ENTERPRISE_2_ADDRESS=<Ganache中第三个账户的地址>

   # 核查机构账户
   VERIFIER_1_KEY=<Ganache中第四个账户的私钥>
   VERIFIER_1_ADDRESS=<Ganache中第四个账户的地址>
   ```

## 步骤4：部署智能合约

1. 运行部署脚本
   ```bash
   python blockchain/deploy_contract.py
   ```

2. 部署成功后，脚本会自动更新`.env`文件中的`CONTRACT_ADDRESS`

## 步骤5：验证部署

1. 启动应用程序
   ```bash
   python run.py
   ```

2. 检查控制台输出，确认区块链客户端连接成功
   ```
   区块链客户端初始化成功，连接到: http://127.0.0.1:8545
   合约地址: 0x...
   管理员地址: 0x...
   ```

## 常见问题

### 1. 无法连接到Ganache

- 确保Ganache正在运行
- 检查`.env`文件中的`ETHEREUM_NODE_URL`是否正确
- 确保使用的URL为`http://127.0.0.1:8545`

### 2. 合约部署失败

- 检查Ganache账户是否有足够的ETH
- 检查`.env`文件中的`ADMIN_PRIVATE_KEY`是否正确
- 尝试重新启动Ganache

### 3. 交易失败

- 检查账户是否有足够的ETH
- 检查账户私钥是否正确
- 检查合约地址是否正确

## 区块链交互流程

系统中的三个主体（企业用户、核查机构、管理员）通过区块链进行以下交互：

1. **排放数据提交**
   - 企业用户提交排放数据
   - 数据上传到区块链
   - 触发`EmissionDataSubmitted`事件

2. **核查流程**
   - 核查机构提交核查结果
   - 结果上传到区块链
   - 触发`VerificationRecordCreated`事件

3. **碳交易**
   - 企业用户发起交易
   - 交易信息上传到区块链
   - 触发`TransactionCreated`事件
   - 卖方确认交易
   - 触发`TransactionConfirmed`事件
   - 或者任一方取消交易
   - 触发`TransactionCancelled`事件

4. **惩罚机制**
   - 管理员创建惩罚记录
   - 惩罚信息上传到区块链
   - 触发`PenaltyCreated`事件

## 区块链事件监听

系统启动时会自动启动区块链事件监听服务，监听以下事件：

- `EmissionDataSubmitted`：排放数据提交事件
- `VerificationRecordCreated`：核查结果提交事件
- `TransactionCreated`：交易创建事件
- `TransactionConfirmed`：交易确认事件
- `TransactionCancelled`：交易取消事件
- `PenaltyCreated`：惩罚创建事件

当这些事件发生时，系统会自动更新数据库中的相应记录。
