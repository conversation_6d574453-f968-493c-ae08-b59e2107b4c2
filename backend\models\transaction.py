"""
交易记录模型
"""

from datetime import datetime
from backend import db
from backend.models.user import User

class Transaction(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    seller_id = db.Column(db.<PERSON>teger, db.<PERSON><PERSON>ey('user.id'), nullable=False)
    buyer_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    price = db.Column(db.Float, nullable=False)
    total_price = db.Column(db.Float, nullable=False)
    transaction_time = db.Column(db.DateTime, default=datetime.utcnow)
    status = db.Column(db.String(20), default='pending')  # pending, completed, cancelled
    blockchain_hash = db.Column(db.String(66))
    blockchain_block = db.Column(db.Integer)

    seller = db.relationship('User', foreign_keys=[seller_id], backref=db.backref('sales', lazy=True))
    buyer = db.relationship('User', foreign_keys=[buyer_id], backref=db.backref('purchases', lazy=True))

    def to_dict(self):
        return {
            'id': self.id,
            'seller_id': self.seller_id,
            'seller_name': self.seller.company_name if self.seller else None,
            'buyer_id': self.buyer_id,
            'buyer_name': self.buyer.company_name if self.buyer else None,
            'amount': self.amount,
            'price': self.price,
            'total_price': self.total_price,
            'transaction_time': self.transaction_time.isoformat() if self.transaction_time else None,
            'status': self.status,
            'blockchain_hash': self.blockchain_hash,
            'blockchain_block': self.blockchain_block
        }
