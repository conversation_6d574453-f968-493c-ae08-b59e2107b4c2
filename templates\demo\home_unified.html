<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放核查系统 - 首页</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        :root {
            --primary-color: #43a047;
            --primary-light: #76d275;
            --primary-dark: #00701a;
            --secondary-color: #1de9b6;
            --secondary-light: #6effe8;
            --secondary-dark: #00b686;
            --text-on-primary: #ffffff;
            --text-on-secondary: #000000;
            --background-color: #f5f5f5;
            --card-background: #ffffff;
            --success-color: #4caf50;
            --warning-color: #ff9800;
            --danger-color: #f44336;
            --info-color: #2196f3;
        }
        
        body {
            font-family: 'Arial', 'Noto Sans SC', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .hero {
            background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('https://images.unsplash.com/photo-1569097387546-9d8e6c7a4e96?ixlib=rb-1.2.1&auto=format&fit=crop&w=1920&q=80');
            background-size: cover;
            background-position: center;
            color: white;
            padding: 100px 0;
            text-align: center;
        }
        
        .hero h1 {
            font-size: 48px;
            margin-bottom: 20px;
        }
        
        .hero p {
            font-size: 20px;
            max-width: 800px;
            margin: 0 auto 30px;
        }
        
        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            margin: 0 10px;
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        
        .btn-primary {
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            color: white;
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid white;
        }
        
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .features {
            padding: 80px 0;
            text-align: center;
        }
        
        .features h2 {
            font-size: 36px;
            margin-bottom: 50px;
            color: #2E7D32;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }
        
        .feature-item {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .feature-item:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        
        .feature-icon {
            font-size: 50px;
            margin-bottom: 20px;
            color: #4CAF50;
        }
        
        .feature-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        
        .about {
            padding: 80px 0;
            background: linear-gradient(135deg, #f1f8e9 0%, #dcedc8 100%);
        }
        
        .about-content {
            display: flex;
            align-items: center;
            gap: 50px;
        }
        
        .about-text {
            flex: 1;
        }
        
        .about-text h2 {
            font-size: 36px;
            margin-bottom: 20px;
            color: #2E7D32;
        }
        
        .about-text p {
            margin-bottom: 15px;
            line-height: 1.6;
        }
        
        .about-image {
            flex: 1;
        }
        
        .about-image img {
            width: 100%;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .cta {
            padding: 80px 0;
            text-align: center;
            background: linear-gradient(to right, #1B5E20, #388E3C);
            color: white;
        }
        
        .cta h2 {
            font-size: 36px;
            margin-bottom: 20px;
        }
        
        .cta p {
            font-size: 18px;
            max-width: 800px;
            margin: 0 auto 30px;
        }
        
        footer {
            background: #1B5E20;
            color: white;
            text-align: center;
            padding: 30px 0;
        }
        
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 36px;
            }
            
            .hero p {
                font-size: 16px;
            }
            
            .about-content {
                flex-direction: column;
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <section class="hero">
        <h1>碳排放管理系统</h1>
        <p>基于区块链技术的碳排放数据管理、核查和交易平台，助力企业实现碳中和目标</p>
        <div>
            <a href="/login" class="btn btn-primary">立即登录</a>
            <a href="/register" class="btn btn-secondary">注册账号</a>
        </div>
    </section>

    <section class="features">
        <div class="container">
            <h2>系统功能</h2>
            <div class="feature-grid">
                <div class="feature-item">
                    <div class="feature-icon"><i class="fas fa-cloud"></i></div>
                    <div class="feature-title">排放数据管理</div>
                    <p>企业可以提交、查看和管理碳排放数据，支持多种排放源和计算方法</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon"><i class="fas fa-clipboard-check"></i></div>
                    <div class="feature-title">第三方核查</div>
                    <p>核查机构可以审核企业提交的排放数据，确保数据的准确性和可靠性</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon"><i class="fas fa-exchange-alt"></i></div>
                    <div class="feature-title">碳配额交易</div>
                    <p>企业可以在平台上进行碳配额交易，优化碳资产管理，降低减排成本</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon"><i class="fas fa-link"></i></div>
                    <div class="feature-title">区块链技术</div>
                    <p>利用区块链技术确保数据的不可篡改性和透明性，提高系统的可信度</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon"><i class="fas fa-chart-line"></i></div>
                    <div class="feature-title">预测分析</div>
                    <p>基于历史数据进行排放趋势预测，帮助企业制定减排策略和目标</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon"><i class="fas fa-file-alt"></i></div>
                    <div class="feature-title">报告生成</div>
                    <p>自动生成排放报告、核查报告和交易报告，满足监管和披露要求</p>
                </div>
            </div>
        </div>
    </section>

    <section class="about" id="about">
        <div class="container">
            <div class="about-content">
                <div class="about-text">
                    <h2>关于系统</h2>
                    <p>碳排放管理系统是一个基于区块链技术的综合性平台，旨在帮助企业实现碳排放数据的精确管理、核查和交易。</p>
                    <p>系统采用前后端分离架构，结合区块链技术，确保数据的安全性、透明性和不可篡改性，为企业碳中和目标的实现提供有力支持。</p>
                    <p>系统支持企业用户、核查机构和管理员三种角色，满足不同用户的需求，实现碳排放管理的全流程数字化。</p>
                </div>
                <div class="about-image">
                    <img src="https://images.unsplash.com/photo-1569098644584-210bcd375b59?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" alt="碳排放管理系统">
                </div>
            </div>
        </div>
    </section>

    <section class="cta">
        <div class="container">
            <h2>开始使用碳排放管理系统</h2>
            <p>立即注册账号，体验全面的碳排放管理功能，助力企业实现碳中和目标</p>
            <a href="/register" class="btn btn-primary">立即注册</a>
        </div>
    </section>

    <footer>
        <div class="container">
            <p>© 2025 碳排放核查系统 | 基于区块链技术</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
