{% extends 'demo/layouts/enterprise_base_unified.html' %}

{% set active_page = 'dashboard' %}

{% block head %}
<style>
    .dashboard {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
    }
    .stat-card {
        text-align: center;
        padding: 30px 20px;
    }
    .stat-value {
        font-size: 36px;
        font-weight: bold;
        background: linear-gradient(to right, #4CAF50, #2E7D32);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        margin: 15px 0;
        text-shadow: 0 2px 5px rgba(0,0,0,0.05);
    }
    .stat-label {
        color: #7f8c8d;
    }
    .chart-container {
        height: 300px;
        margin-top: 20px;
    }
    .status {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        display: inline-block;
    }
    .status-pending {
        background: linear-gradient(to right, #f39c12, #e67e22);
        color: white;
    }
    .status-verified {
        background: linear-gradient(to right, #2ecc71, #27ae60);
        color: white;
    }
    .status-rejected {
        background: linear-gradient(to right, #e74c3c, #c0392b);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
    <h1>企业仪表板</h1>

    <div class="dashboard">
        <div class="card stat-card">
            <div class="stat-label">总排放量</div>
            <div class="stat-value">2,450</div>
            <div class="stat-label">吨CO2e</div>
        </div>
        <div class="card stat-card">
            <div class="stat-label">当前配额</div>
            <div class="stat-value">3,200</div>
            <div class="stat-label">吨CO2e</div>
        </div>
        <div class="card stat-card">
            <div class="stat-label">核查通过率</div>
            <div class="stat-value">85%</div>
            <div class="stat-label">本年度</div>
        </div>
    </div>

    <div class="card">
        <div class="card-title">排放数据趋势</div>
        <div class="chart-container">
            <canvas id="emissionTrendChart"></canvas>
        </div>
    </div>

    <div class="card">
        <div class="card-title">
            <span>最近排放数据</span>
            <a href="/emission_submit" class="btn btn-primary">提交新数据</a>
        </div>
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>排放源</th>
                    <th>排放量</th>
                    <th>计算方法</th>
                    <th>排放期间</th>
                    <th>状态</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>1001</td>
                    <td>燃煤锅炉</td>
                    <td>450 吨CO2e</td>
                    <td>排放因子法</td>
                    <td>2025-01-01 至 2025-01-31</td>
                    <td><span class="status status-verified">已核查</span></td>
                    <td><a href="/emission_detail?id=1001" class="btn btn-primary">查看</a></td>
                </tr>
                <tr>
                    <td>1002</td>
                    <td>天然气锅炉</td>
                    <td>320 吨CO2e</td>
                    <td>排放因子法</td>
                    <td>2025-02-01 至 2025-02-28</td>
                    <td><span class="status status-pending">待核查</span></td>
                    <td><a href="/emission_detail?id=1002" class="btn btn-primary">查看</a></td>
                </tr>
                <tr>
                    <td>1003</td>
                    <td>工业生产过程</td>
                    <td>780 吨CO2e</td>
                    <td>物料平衡法</td>
                    <td>2025-03-01 至 2025-03-31</td>
                    <td><span class="status status-rejected">已拒绝</span></td>
                    <td><a href="/emission_detail?id=1003" class="btn btn-primary">查看</a></td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="card">
        <div class="card-title">最近交易记录</div>
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>交易方</th>
                    <th>数量</th>
                    <th>价格</th>
                    <th>总价</th>
                    <th>交易时间</th>
                    <th>状态</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>2001</td>
                    <td>上海绿色能源有限公司</td>
                    <td>200 吨</td>
                    <td>45 元/吨</td>
                    <td>9,000 元</td>
                    <td>2025-04-15</td>
                    <td><span class="status status-verified">已完成</span></td>
                    <td><a href="/transaction_detail?id=2001" class="btn btn-primary">查看</a></td>
                </tr>
                <tr>
                    <td>2002</td>
                    <td>广州环保科技有限公司</td>
                    <td>150 吨</td>
                    <td>50 元/吨</td>
                    <td>7,500 元</td>
                    <td>2025-05-20</td>
                    <td><span class="status status-pending">待确认</span></td>
                    <td><a href="/transaction_detail?id=2002" class="btn btn-primary">查看</a></td>
                </tr>
            </tbody>
        </table>
    </div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 排放趋势图表
        const emissionTrendCtx = document.getElementById('emissionTrendChart').getContext('2d');
        const emissionTrendChart = new Chart(emissionTrendCtx, {
            type: 'line',
            data: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月'],
                datasets: [{
                    label: '实际排放量 (吨CO2e)',
                    data: [450, 320, 780, 560, 490, 610, 700, 750, 800, 850],
                    borderColor: '#4CAF50',
                    backgroundColor: 'rgba(76, 175, 80, 0.1)',
                    borderWidth: 3,
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '排放量 (吨CO2e)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '月份'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: '月度碳排放趋势',
                        font: {
                            size: 16,
                            weight: 'bold'
                        },
                        color: '#2E7D32'
                    },
                    legend: {
                        position: 'top'
                    }
                }
            }
        });
    });
</script>
{% endblock %}
