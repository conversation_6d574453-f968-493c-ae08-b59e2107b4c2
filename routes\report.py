"""
报告模块路由
处理自动化报告生成相关的API请求
"""

from flask import Blueprint, request, jsonify, current_app, send_file
from flask_jwt_extended import jwt_required, get_jwt_identity
import os
from datetime import datetime, timedelta
import pandas as pd

from models import db, User, EmissionData, Report, Activity
from utils.report_generator import ReportGenerator
from utils.prediction import EmissionPredictor

report_bp = Blueprint('report', __name__)

@report_bp.route('', methods=['GET'])
@jwt_required()
def get_reports():
    """获取报告列表"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    if not user:
        return jsonify({'error': '用户不存在'}), 404
    
    # 获取查询参数
    report_type = request.args.get('type')
    
    # 构建查询
    query = Report.query
    
    # 根据用户角色过滤
    if user.role == 'enterprise':
        query = query.filter_by(enterprise_id=current_user_id)
    
    if report_type:
        query = query.filter_by(report_type=report_type)
    
    # 执行查询
    reports = query.order_by(Report.created_at.desc()).all()
    
    return jsonify([r.to_dict() for r in reports]), 200

@report_bp.route('/<int:report_id>', methods=['GET'])
@jwt_required()
def get_report(report_id):
    """获取报告详情"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    if not user:
        return jsonify({'error': '用户不存在'}), 404
    
    # 获取报告
    report = Report.query.get(report_id)
    
    if not report:
        return jsonify({'error': '报告不存在'}), 404
    
    # 验证权限
    if user.role == 'enterprise' and report.enterprise_id != current_user_id:
        return jsonify({'error': '无权访问此报告'}), 403
    
    return jsonify(report.to_dict()), 200

@report_bp.route('/generate/emission', methods=['POST'])
@jwt_required()
def generate_emission_report():
    """生成碳排放报告"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    if not user:
        return jsonify({'error': '用户不存在'}), 404
    
    data = request.json
    
    if not data:
        return jsonify({'error': '缺少报告参数'}), 400
    
    # 获取参数
    enterprise_id = data.get('enterprise_id', current_user_id)
    title = data.get('title', f'{datetime.now().year}年碳排放报告')
    period_start = data.get('period_start')
    period_end = data.get('period_end')
    include_prediction = data.get('include_prediction', True)
    
    # 验证权限
    if user.role == 'enterprise' and enterprise_id != current_user_id:
        return jsonify({'error': '无权为其他企业生成报告'}), 403
    
    # 如果未指定日期范围，默认为当年
    if not period_start or not period_end:
        today = datetime.now()
        period_start = f"{today.year}-01-01"
        period_end = f"{today.year}-12-31"
    
    # 获取企业信息
    enterprise = User.query.get(enterprise_id)
    
    if not enterprise or enterprise.role != 'enterprise':
        return jsonify({'error': '企业不存在'}), 404
    
    # 获取排放数据
    query = EmissionData.query.filter_by(
        enterprise_id=enterprise_id,
        status='verified'
    )
    
    if period_start:
        query = query.filter(EmissionData.emission_period_start >= period_start)
    
    if period_end:
        query = query.filter(EmissionData.emission_period_end <= period_end)
    
    emissions = query.order_by(EmissionData.emission_period_start).all()
    
    if not emissions:
        return jsonify({'error': '没有可用的排放数据'}), 400
    
    # 转换为字典列表
    emission_data = [e.to_dict() for e in emissions]
    
    # 获取预测数据
    prediction_data = None
    if include_prediction:
        try:
            # 获取最新模型
            from models import PredictionModel
            model = PredictionModel.query.order_by(PredictionModel.last_trained.desc()).first()
            
            if model:
                # 加载模型
                predictor = EmissionPredictor(model.model_path)
                
                # 获取最后一条数据作为预测模板
                last_emission = emissions[-1].to_dict()
                
                # 预测未来6个月
                prediction_data = predictor.predict_future(last_emission, periods=6)
        except Exception as e:
            # 预测失败时不包含预测数据
            pass
    
    # 生成报告
    report_generator = ReportGenerator()
    html_content = report_generator.generate_emission_report(
        enterprise.to_dict(),
        emission_data,
        period_start,
        period_end,
        prediction_data
    )
    
    # 保存报告
    report_dir = os.path.join(current_app.instance_path, 'reports')
    os.makedirs(report_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    pdf_filename = f"emission_report_{enterprise.id}_{timestamp}.pdf"
    pdf_path = os.path.join(report_dir, pdf_filename)
    
    # 生成PDF
    report_generator.generate_pdf_report(html_content, pdf_path)
    
    # 创建报告记录
    report = Report(
        enterprise_id=enterprise_id,
        title=title,
        report_type='emission',
        period_start=datetime.strptime(period_start, '%Y-%m-%d').date(),
        period_end=datetime.strptime(period_end, '%Y-%m-%d').date(),
        file_path=pdf_path
    )
    report.set_content(html_content)
    
    db.session.add(report)
    
    # 记录活动
    activity = Activity(
        user_id=current_user_id,
        activity_type='report_generate',
        description=f"生成了 '{title}' 报告"
    )
    db.session.add(activity)
    
    db.session.commit()
    
    return jsonify({
        'message': '报告生成成功',
        'report': report.to_dict()
    }), 201

@report_bp.route('/<int:report_id>/download', methods=['GET'])
@jwt_required()
def download_report(report_id):
    """下载报告文件"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    if not user:
        return jsonify({'error': '用户不存在'}), 404
    
    # 获取报告
    report = Report.query.get(report_id)
    
    if not report:
        return jsonify({'error': '报告不存在'}), 404
    
    # 验证权限
    if user.role == 'enterprise' and report.enterprise_id != current_user_id:
        return jsonify({'error': '无权下载此报告'}), 403
    
    # 验证文件存在
    if not report.file_path or not os.path.exists(report.file_path):
        return jsonify({'error': '报告文件不存在'}), 404
    
    # 下载文件
    return send_file(
        report.file_path,
        as_attachment=True,
        download_name=f"{report.title}.pdf",
        mimetype='application/pdf'
    )
