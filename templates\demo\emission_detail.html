<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放管理系统 - 排放数据详情</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/unified-nav.css') }}">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background-color: #2c3e50;
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-name {
            margin-right: 15px;
        }
        .logout-btn {
            background-color: #e74c3c;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
        }
        nav {
            background-color: #34495e;
            padding: 10px 0;
        }
        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
        }
        .nav-item {
            color: white;
            text-decoration: none;
            padding: 10px 15px;
            margin-right: 5px;
            border-radius: 4px;
        }
        .nav-item:hover, .nav-item.active {
            background-color: #2c3e50;
        }
        .card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .card-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .btn {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
        }
        .btn-primary {
            background-color: #3498db;
            color: white;
        }
        .btn-success {
            background-color: #2ecc71;
            color: white;
        }
        .btn-danger {
            background-color: #e74c3c;
            color: white;
        }
        .detail-section {
            margin-bottom: 30px;
        }
        .detail-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        .detail-row {
            display: flex;
            margin-bottom: 10px;
        }
        .detail-label {
            width: 150px;
            font-weight: bold;
            color: #7f8c8d;
        }
        .detail-value {
            flex: 1;
        }
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }
        .status-pending {
            background-color: #f39c12;
        }
        .status-verified {
            background-color: #2ecc71;
        }
        .status-rejected {
            background-color: #e74c3c;
        }
        .blockchain-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .blockchain-hash {
            font-family: monospace;
            background-color: #eee;
            padding: 2px 5px;
            border-radius: 3px;
        }
        .verification-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .verification-comments {
            margin-top: 10px;
            padding: 10px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo">碳排放管理系统</div>
            <div class="user-info">
                <span class="user-name">北京碳排放科技有限公司</span>
                <a href="/login" class="logout-btn">退出登录</a>
            </div>
        </div>
    </header>

    <div class="demo-nav">
        <div class="nav-content">
            <a href="/dashboard" class="nav-item"><i class="fas fa-tachometer-alt mr-2"></i>仪表板</a>
            <a href="/emissions" class="nav-item active"><i class="fas fa-cloud mr-2"></i>排放数据</a>
            <a href="/verifications" class="nav-item"><i class="fas fa-clipboard-check mr-2"></i>核查记录</a>
            <a href="/transactions" class="nav-item"><i class="fas fa-exchange-alt mr-2"></i>碳交易</a>
            <a href="/calculator" class="nav-item"><i class="fas fa-calculator mr-2"></i>碳计算器</a>
            <a href="/predictions" class="nav-item"><i class="fas fa-chart-line mr-2"></i>预测分析</a>
            <a href="/reports" class="nav-item"><i class="fas fa-file-alt mr-2"></i>报告生成</a>
            <a href="/enterprise_blockchain_config" class="nav-item"><i class="fas fa-link mr-2"></i>区块链配置</a>
        </div>
    </div>

    <div class="container">
        <div class="card-title">
            <h1>排放数据详情</h1>
            <div>
                <a href="/emissions" class="btn btn-primary">返回列表</a>
            </div>
        </div>

        <div class="card">
            <div class="detail-section">
                <div class="detail-title">基本信息</div>

                <div class="detail-row">
                    <div class="detail-label">ID</div>
                    <div class="detail-value">1001</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">排放源</div>
                    <div class="detail-value">燃煤锅炉</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">排放量</div>
                    <div class="detail-value">450 吨CO2e</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">计算方法</div>
                    <div class="detail-value">排放因子法</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">排放期间</div>
                    <div class="detail-value">2023-01-01 至 2023-01-31</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">提交时间</div>
                    <div class="detail-value">2023-02-05 10:30:45</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">状态</div>
                    <div class="detail-value">
                        <span class="status status-verified">已核查</span>
                    </div>
                </div>
            </div>

            <div class="detail-section">
                <div class="detail-title">计算详情</div>

                <div class="detail-row">
                    <div class="detail-label">活动数据</div>
                    <div class="detail-value">180 吨标准煤</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">排放因子</div>
                    <div class="detail-value">2.5 吨CO2e/吨标准煤</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">计算公式</div>
                    <div class="detail-value">排放量 = 活动数据 × 排放因子 = 180 × 2.5 = 450 吨CO2e</div>
                </div>
            </div>

            <div class="detail-section">
                <div class="detail-title">核查信息</div>

                <div class="verification-info">
                    <div class="detail-row">
                        <div class="detail-label">核查机构</div>
                        <div class="detail-value">国家碳排放核查中心</div>
                    </div>

                    <div class="detail-row">
                        <div class="detail-label">核查时间</div>
                        <div class="detail-value">2023-02-15 14:20:30</div>
                    </div>

                    <div class="detail-row">
                        <div class="detail-label">核查结论</div>
                        <div class="detail-value">
                            <span class="status status-verified">通过</span>
                        </div>
                    </div>

                    <div class="detail-row">
                        <div class="detail-label">核查意见</div>
                        <div class="detail-value">
                            <div class="verification-comments">
                                经核查，该排放数据计算方法正确，活动数据来源可靠，排放因子选择合理，计算结果准确。建议企业进一步完善能源消耗记录，提高数据质量。
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="detail-section">
                <div class="detail-title">区块链记录</div>

                <div class="blockchain-info">
                    <div class="detail-row">
                        <div class="detail-label">交易哈希</div>
                        <div class="detail-value">
                            <span class="blockchain-hash">0xa1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4e5f6a1b2</span>
                        </div>
                    </div>

                    <div class="detail-row">
                        <div class="detail-label">区块号</div>
                        <div class="detail-value">15790123</div>
                    </div>

                    <div class="detail-row">
                        <div class="detail-label">时间戳</div>
                        <div class="detail-value">2023-02-05 10:35:12</div>
                    </div>

                    <div class="detail-row">
                        <div class="detail-label">数据哈希</div>
                        <div class="detail-value">
                            <span class="blockchain-hash">0xf6e5d4c3b2a1f6e5d4c3b2a1f6e5d4c3b2a1f6e5d4c3b2a1f6e5d4c3b2a1f6e5</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="actions">
                <a href="/emissions" class="btn btn-primary">返回列表</a>
                <a href="/reports/emission_report.html" target="_blank" class="btn btn-success">生成报告</a>
            </div>
        </div>
    </div>
</body>
</html>
