<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放管理系统 - 提交排放数据</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background-color: #2c3e50;
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-name {
            margin-right: 15px;
        }
        .logout-btn {
            background-color: #e74c3c;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
        }
        nav {
            background-color: #34495e;
            padding: 10px 0;
        }
        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
        }
        .nav-item {
            color: white;
            text-decoration: none;
            padding: 10px 15px;
            margin-right: 5px;
            border-radius: 4px;
        }
        .nav-item:hover, .nav-item.active {
            background-color: #2c3e50;
        }
        .card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .card-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        .btn {
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
        }
        .btn-primary {
            background-color: #3498db;
            color: white;
        }
        .btn-secondary {
            background-color: #95a5a6;
            color: white;
        }
        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }
        .form-section {
            margin-bottom: 30px;
            border-bottom: 1px solid #eee;
            padding-bottom: 20px;
        }
        .form-section-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
        }
        .form-col {
            flex: 1;
        }
        .help-text {
            font-size: 12px;
            color: #7f8c8d;
            margin-top: 5px;
        }
        .required::after {
            content: "*";
            color: #e74c3c;
            margin-left: 3px;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo">碳排放管理系统</div>
            <div class="user-info">
                <span class="user-name">北京碳排放科技有限公司</span>
                <a href="/login" class="logout-btn">退出登录</a>
            </div>
        </div>
    </header>

    <nav>
        <div class="nav-content">
            <a href="/dashboard" class="nav-item">仪表板</a>
            <a href="/emissions" class="nav-item active">排放数据</a>
            <a href="/verifications" class="nav-item">核查记录</a>
            <a href="/transactions" class="nav-item">碳交易</a>
            <a href="/calculator" class="nav-item">碳计算器</a>
            <a href="/predictions" class="nav-item">预测分析</a>
            <a href="/reports" class="nav-item">报告生成</a>
        </div>
    </nav>

    <div class="container">
        <h1>提交排放数据</h1>

        <div class="card">
            <form id="emission-form">
                <div class="form-section">
                    <div class="form-section-title">基本信息</div>

                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label for="emission-source" class="required">排放源</label>
                                <select id="emission-source" name="emission_source" required>
                                    <option value="">请选择排放源</option>
                                    <option value="coal">燃煤锅炉</option>
                                    <option value="gas">天然气锅炉</option>
                                    <option value="process">工业生产过程</option>
                                    <option value="transport">交通运输</option>
                                    <option value="electricity">电力消耗</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label for="calculation-method" class="required">计算方法</label>
                                <select id="calculation-method" name="calculation_method" required>
                                    <option value="">请选择计算方法</option>
                                    <option value="factor">排放因子法</option>
                                    <option value="balance">物料平衡法</option>
                                    <option value="monitor">连续监测法</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label for="emission-amount" class="required">排放量</label>
                                <input type="number" id="emission-amount" name="emission_amount" step="0.01" min="0" required>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label for="emission-unit" class="required">排放单位</label>
                                <select id="emission-unit" name="emission_unit" required>
                                    <option value="tCO2e">吨CO2e</option>
                                    <option value="kgCO2e">千克CO2e</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <div class="form-section-title">排放期间</div>

                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label for="period-start" class="required">开始日期</label>
                                <input type="date" id="period-start" name="period_start" required>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label for="period-end" class="required">结束日期</label>
                                <input type="date" id="period-end" name="period_end" required>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <div class="form-section-title">证明材料</div>

                    <div class="form-group">
                        <label for="proof-file">证明文件</label>
                        <input type="file" id="proof-file" name="proof_file">
                        <div class="help-text">支持PDF、Word、Excel格式，最大10MB</div>
                    </div>

                    <div class="form-group">
                        <label for="remarks">备注</label>
                        <textarea id="remarks" name="remarks" placeholder="请输入备注信息"></textarea>
                    </div>
                </div>

                <div class="form-actions">
                    <a href="/emissions" class="btn btn-secondary">取消</a>
                    <button type="submit" class="btn btn-primary">提交</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 表单提交事件
            const form = document.getElementById('emission-form');
            form.addEventListener('submit', function(e) {
                e.preventDefault();

                // 在实际应用中，这里应该发送AJAX请求提交数据
                // 这里只是演示，直接跳转到排放数据页面
                alert('排放数据提交成功！');
                window.location.href = '/emissions';
            });

            // 设置默认日期
            const today = new Date();
            const lastMonth = new Date(today);
            lastMonth.setMonth(today.getMonth() - 1);

            const formatDate = date => {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                return `${year}-${month}-${day}`;
            };

            document.getElementById('period-start').value = formatDate(lastMonth);
            document.getElementById('period-end').value = formatDate(today);
        });
    </script>
</body>
</html>
