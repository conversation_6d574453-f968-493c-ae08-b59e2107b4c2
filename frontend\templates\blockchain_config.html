{% extends "base.html" %}

{% block title %}区块链配置{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="card shadow">
        <div class="card-header bg-primary text-white">
            <h4 class="mb-0">区块链配置</h4>
        </div>
        <div class="card-body">
            {% if success_message %}
            <div class="alert alert-success" role="alert">
                {{ success_message }}
            </div>
            {% endif %}

            {% if error_message %}
            <div class="alert alert-danger" role="alert">
                {{ error_message }}
            </div>
            {% endif %}

            <form method="POST" action="{{ url_for('blockchain_config.blockchain_config') }}">
                <div class="mb-4">
                    <h5 class="border-bottom pb-2">区块链连接信息</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="ethereum_node_url" class="form-label">以太坊节点URL</label>
                                <input type="text" class="form-control" id="ethereum_node_url" name="ethereum_node_url"
                                       value="{{ config.get('ETHEREUM_NODE_URL', 'http://127.0.0.1:8545') }}" required>
                                <div class="form-text">Ganache默认URL: http://127.0.0.1:8545</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="contract_address" class="form-label">合约地址</label>
                                <input type="text" class="form-control" id="contract_address" name="contract_address"
                                       value="{{ config.get('CONTRACT_ADDRESS', '') }}" placeholder="部署合约后自动填充">
                                <div class="form-text">部署合约后会自动更新</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <h5 class="border-bottom pb-2">管理员账户</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="admin_address" class="form-label">管理员地址</label>
                                <input type="text" class="form-control" id="admin_address" name="admin_address"
                                       value="{{ config.get('ADMIN_ADDRESS', '') }}" required>
                                <div class="form-text">Ganache中第1个账户的地址</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="admin_private_key" class="form-label">管理员私钥</label>
                                <input type="text" class="form-control" id="admin_private_key" name="admin_private_key"
                                       value="{{ config.get('ADMIN_PRIVATE_KEY', '') }}" required>
                                <div class="form-text">Ganache中第1个账户的私钥</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <h5 class="border-bottom pb-2">企业账户</h5>

                    <!-- 企业1 -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="enterprise_1_address" class="form-label">企业1地址</label>
                                <input type="text" class="form-control" id="enterprise_1_address" name="enterprise_1_address"
                                       value="{{ config.get('ENTERPRISE_1_ADDRESS', '') }}" required>
                                <div class="form-text">Ganache中第2个账户的地址</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="enterprise_1_key" class="form-label">企业1私钥</label>
                                <input type="text" class="form-control" id="enterprise_1_key" name="enterprise_1_key"
                                       value="{{ config.get('ENTERPRISE_1_KEY', '') }}" required>
                                <div class="form-text">Ganache中第2个账户的私钥</div>
                            </div>
                        </div>
                    </div>

                    <!-- 企业2 -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="enterprise_2_address" class="form-label">企业2地址</label>
                                <input type="text" class="form-control" id="enterprise_2_address" name="enterprise_2_address"
                                       value="{{ config.get('ENTERPRISE_2_ADDRESS', '') }}" required>
                                <div class="form-text">Ganache中第3个账户的地址</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="enterprise_2_key" class="form-label">企业2私钥</label>
                                <input type="text" class="form-control" id="enterprise_2_key" name="enterprise_2_key"
                                       value="{{ config.get('ENTERPRISE_2_KEY', '') }}" required>
                                <div class="form-text">Ganache中第3个账户的私钥</div>
                            </div>
                        </div>
                    </div>

                    <!-- 企业3 -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="enterprise_3_address" class="form-label">企业3地址</label>
                                <input type="text" class="form-control" id="enterprise_3_address" name="enterprise_3_address"
                                       value="{{ config.get('ENTERPRISE_3_ADDRESS', '') }}" required>
                                <div class="form-text">Ganache中第4个账户的地址</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="enterprise_3_key" class="form-label">企业3私钥</label>
                                <input type="text" class="form-control" id="enterprise_3_key" name="enterprise_3_key"
                                       value="{{ config.get('ENTERPRISE_3_KEY', '') }}" required>
                                <div class="form-text">Ganache中第4个账户的私钥</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <h5 class="border-bottom pb-2">核查机构账户</h5>

                    <!-- 核查机构1 -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="verifier_1_address" class="form-label">核查机构1地址</label>
                                <input type="text" class="form-control" id="verifier_1_address" name="verifier_1_address"
                                       value="{{ config.get('VERIFIER_1_ADDRESS', '') }}" required>
                                <div class="form-text">Ganache中第5个账户的地址</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="verifier_1_key" class="form-label">核查机构1私钥</label>
                                <input type="text" class="form-control" id="verifier_1_key" name="verifier_1_key"
                                       value="{{ config.get('VERIFIER_1_KEY', '') }}" required>
                                <div class="form-text">Ganache中第5个账户的私钥</div>
                            </div>
                        </div>
                    </div>

                    <!-- 核查机构2 -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="verifier_2_address" class="form-label">核查机构2地址</label>
                                <input type="text" class="form-control" id="verifier_2_address" name="verifier_2_address"
                                       value="{{ config.get('VERIFIER_2_ADDRESS', '') }}" required>
                                <div class="form-text">Ganache中第6个账户的地址</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="verifier_2_key" class="form-label">核查机构2私钥</label>
                                <input type="text" class="form-control" id="verifier_2_key" name="verifier_2_key"
                                       value="{{ config.get('VERIFIER_2_KEY', '') }}" required>
                                <div class="form-text">Ganache中第6个账户的私钥</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-between">
                    <button type="submit" class="btn btn-primary">保存配置</button>
                    <div>
                        <a href="{{ url_for('blockchain_config.fetch_ganache_accounts') }}" class="btn btn-info me-2">获取Ganache账户</a>
                        <a href="{{ url_for('blockchain_config.deploy_contract') }}" class="btn btn-success">部署智能合约</a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {% if ganache_accounts %}
    <div class="card shadow mt-4">
        <div class="card-header bg-info text-white">
            <h4 class="mb-0">Ganache账户信息</h4>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>账户</th>
                            <th>地址</th>
                            <th>余额</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for account in ganache_accounts %}
                        <tr>
                            <td>账户 {{ loop.index }}</td>
                            <td>{{ account.address }}</td>
                            <td>{{ account.balance }} ETH</td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary copy-address" data-address="{{ account.address }}">
                                    复制地址
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            <div class="alert alert-warning mt-3">
                <strong>注意:</strong> Ganache提供的账户私钥可以在Ganache UI界面中查看。请从Ganache界面获取私钥并填写到上面的表单中。
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 复制地址功能
    const copyButtons = document.querySelectorAll('.copy-address');
    copyButtons.forEach(button => {
        button.addEventListener('click', function() {
            const address = this.getAttribute('data-address');
            navigator.clipboard.writeText(address).then(() => {
                const originalText = this.textContent;
                this.textContent = '已复制!';
                this.classList.remove('btn-outline-primary');
                this.classList.add('btn-success');

                setTimeout(() => {
                    this.textContent = originalText;
                    this.classList.remove('btn-success');
                    this.classList.add('btn-outline-primary');
                }, 2000);
            });
        });
    });
});
</script>
{% endblock %}
