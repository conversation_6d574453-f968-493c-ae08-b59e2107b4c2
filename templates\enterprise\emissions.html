<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放核查系统 - 排放管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <style>
        .gradient-custom {
            background: linear-gradient(135deg, #43a047 0%, #1de9b6 100%);
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            margin-bottom: 20px;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }
        .card-header {
            border-radius: 15px 15px 0 0 !important;
            background: linear-gradient(135deg, #43a047 0%, #1de9b6 100%) !important;
            color: white;
            font-weight: 600;
            padding: 1rem 1.5rem;
            border: none;
        }
        .stat-card {
            text-align: center;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }
        .stat-card .card-body {
            padding: 1.5rem;
        }
        .stat-card .icon {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        .stat-card .stat-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        .stat-card .stat-label {
            font-size: 1rem;
            color: rgba(255, 255, 255, 0.8);
        }
        .btn-success {
            background: linear-gradient(135deg, #43a047 0%, #1de9b6 100%);
            border: none;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #388e3c 0%, #00bfa5 100%);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }
        .table th {
            background-color: rgba(67, 160, 71, 0.1);
            font-weight: 600;
            color: #444;
        }
        .badge {
            padding: 0.5em 0.8em;
            border-radius: 10px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container">
            <a class="navbar-brand" href="/"><i class="fas fa-leaf me-2"></i>碳排放核查系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise"><i class="fas fa-tachometer-alt me-1"></i>仪表板</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/enterprise/emissions"><i class="fas fa-cloud me-1"></i>排放管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise/transactions"><i class="fas fa-exchange-alt me-1"></i>交易管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise/reports"><i class="fas fa-file-alt me-1"></i>报告管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise/calculator"><i class="fas fa-calculator me-1"></i>碳计算器</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>{{ current_user.get('username', '企业用户') }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="/enterprise/profile"><i class="fas fa-id-card me-1"></i>企业资料</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-1"></i>退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mt-4">
        <!-- 消息提示 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-cloud me-2"></i>排放管理</h1>
            <a href="/enterprise/emissions/add" class="btn btn-success">
                <i class="fas fa-plus me-1"></i>添加排放数据
            </a>
        </div>

        <!-- 排放统计 -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="stat-card" style="background: linear-gradient(135deg, #2196f3 0%, #64b5f6 100%);">
                    <div class="card-body text-white">
                        <i class="fas fa-cloud icon"></i>
                        <h2 class="stat-value">{{ stats.total|default('0') }} <small>tCO2e</small></h2>
                        <p class="stat-label">总排放量</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card" style="background: linear-gradient(135deg, #43a047 0%, #76d275 100%);">
                    <div class="card-body text-white">
                        <i class="fas fa-check-circle icon"></i>
                        <h2 class="stat-value">{{ stats.verified|default('0') }} <small>tCO2e</small></h2>
                        <p class="stat-label">已核查</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card" style="background: linear-gradient(135deg, #ff9800 0%, #ffb74d 100%);">
                    <div class="card-body text-white">
                        <i class="fas fa-clock icon"></i>
                        <h2 class="stat-value">{{ stats.pending|default('0') }} <small>tCO2e</small></h2>
                        <p class="stat-label">待核查</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 排放筛选 -->
        <div class="card mb-4">
            <div class="card-header gradient-custom">
                <h5 class="card-title mb-0"><i class="fas fa-filter me-2"></i>筛选排放数据</h5>
            </div>
            <div class="card-body">
                <form method="get" action="/enterprise/emissions" class="row g-3">
                    <div class="col-md-3">
                        <label for="source" class="form-label">排放源</label>
                        <select class="form-select" id="source" name="source">
                            <option value="">全部来源</option>
                            <option value="电力消耗">电力消耗</option>
                            <option value="燃料燃烧">燃料燃烧</option>
                            <option value="工业过程">工业过程</option>
                            <option value="废弃物处理">废弃物处理</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="date_from" class="form-label">开始日期</label>
                        <input type="date" class="form-control" id="date_from" name="date_from">
                    </div>
                    <div class="col-md-3">
                        <label for="date_to" class="form-label">结束日期</label>
                        <input type="date" class="form-control" id="date_to" name="date_to">
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">状态</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">全部状态</option>
                            <option value="待提交">待提交</option>
                            <option value="已提交">已提交</option>
                            <option value="待核查">待核查</option>
                            <option value="已核查">已核查</option>
                            <option value="已拒绝">已拒绝</option>
                        </select>
                    </div>
                    <div class="col-12 d-flex justify-content-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>搜索
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 排放数据列表 -->
        <div class="card">
            <div class="card-header gradient-custom">
                <h5 class="card-title mb-0"><i class="fas fa-list me-2"></i>排放数据列表</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>排放源</th>
                                <th>排放量 (tCO2e)</th>
                                <th>日期</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if emissions %}
                                {% for emission in emissions %}
                                    <tr>
                                        <td>{{ emission.id }}</td>
                                        <td>{{ emission.source }}</td>
                                        <td>{{ emission.amount }}</td>
                                        <td>{{ emission.date }}</td>
                                        <td>
                                            {% if emission.status == '待提交' or emission.status == 'draft' %}
                                                <span class="badge" style="background: linear-gradient(135deg, #607d8b 0%, #90a4ae 100%);">待提交</span>
                                            {% elif emission.status == '已提交' or emission.status == 'submitted' %}
                                                <span class="badge" style="background: linear-gradient(135deg, #2196f3 0%, #64b5f6 100%);">已提交</span>
                                            {% elif emission.status == '待核查' or emission.status == 'pending' %}
                                                <span class="badge" style="background: linear-gradient(135deg, #ff9800 0%, #ffb74d 100%);">待核查</span>
                                            {% elif emission.status == '已核查' or emission.status == 'verified' %}
                                                <span class="badge" style="background: linear-gradient(135deg, #43a047 0%, #76d275 100%);">已核查</span>
                                            {% elif emission.status == '已拒绝' or emission.status == 'rejected' %}
                                                <span class="badge" style="background: linear-gradient(135deg, #f44336 0%, #ef5350 100%);">已拒绝</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ emission.status }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="/enterprise/emissions/{{ emission.id }}" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                {% if emission.status == '待提交' or emission.status == 'draft' or emission.status == '已拒绝' or emission.status == 'rejected' %}
                                                    <a href="/enterprise/emissions/{{ emission.id }}/edit" class="btn btn-sm btn-primary" title="编辑">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="/enterprise/emissions/{{ emission.id }}/submit" class="btn btn-sm btn-success" title="提交">
                                                        <i class="fas fa-paper-plane"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ emission.id }}" title="删除">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                {% elif emission.status == '已提交' or emission.status == 'submitted' %}
                                                    <button type="button" class="btn btn-sm btn-warning" title="撤回" onclick="withdrawEmission({{ emission.id }})">
                                                        <i class="fas fa-undo"></i>
                                                    </button>
                                                {% endif %}
                                            </div>

                                            <!-- 删除确认模态框 -->
                                            {% if emission.status == '待提交' or emission.status == 'draft' or emission.status == '已拒绝' or emission.status == 'rejected' %}
                                                <div class="modal fade" id="deleteModal{{ emission.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ emission.id }}" aria-hidden="true">
                                                    <div class="modal-dialog">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h5 class="modal-title" id="deleteModalLabel{{ emission.id }}">确认删除</h5>
                                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                            </div>
                                                            <div class="modal-body">
                                                                确定要删除这条排放数据吗？此操作不可逆。
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                                                <form action="/enterprise/emissions/{{ emission.id }}/delete" method="post" style="display: inline;">
                                                                    <button type="submit" class="btn btn-danger">确认删除</button>
                                                                </form>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="6" class="text-center">暂无排放数据</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1" aria-disabled="true">上一页</a>
                        </li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                        <li class="page-item">
                            <a class="page-link" href="#">下一页</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">© 2025 碳排放核查系统 | 基于区块链技术</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="{{ url_for('static', filename='js/api.js') }}"></script>
    <script>
        // 撤回排放数据
        function withdrawEmission(emissionId) {
            if (confirm('确定要撤回此排放数据吗？')) {
                fetch(`/enterprise/emissions/${emissionId}/withdraw`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('排放数据已撤回');
                        location.reload();
                    } else {
                        alert('撤回失败: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('撤回失败: ' + error);
                });
            }
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            // 设置筛选表单的默认值
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.has('source')) {
                document.getElementById('source').value = urlParams.get('source');
            }
            if (urlParams.has('date_from')) {
                document.getElementById('date_from').value = urlParams.get('date_from');
            }
            if (urlParams.has('date_to')) {
                document.getElementById('date_to').value = urlParams.get('date_to');
            }
            if (urlParams.has('status')) {
                document.getElementById('status').value = urlParams.get('status');
            }
        });
    </script>
</body>
</html>
