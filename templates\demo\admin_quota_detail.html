<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放管理系统 - 配额详情</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background-color: #2c3e50;
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-name {
            margin-right: 15px;
        }
        .logout-btn {
            background-color: #e74c3c;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
        }
        nav {
            background-color: #34495e;
            padding: 10px 0;
        }
        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
        }
        .nav-item {
            color: white;
            text-decoration: none;
            padding: 10px 15px;
            margin-right: 5px;
            border-radius: 4px;
        }
        .nav-item:hover, .nav-item.active {
            background-color: #2c3e50;
        }
        .card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .card-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .btn {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
        }
        .btn-primary {
            background-color: #3498db;
            color: white;
        }
        .btn-success {
            background-color: #2ecc71;
            color: white;
        }
        .btn-danger {
            background-color: #e74c3c;
            color: white;
        }
        .detail-section {
            margin-bottom: 30px;
        }
        .detail-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        .detail-row {
            display: flex;
            margin-bottom: 10px;
        }
        .detail-label {
            width: 150px;
            font-weight: bold;
            color: #7f8c8d;
        }
        .detail-value {
            flex: 1;
        }
        .chart-container {
            height: 300px;
            margin-top: 20px;
        }
        .chart-placeholder {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 4px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }
        .blockchain-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .blockchain-hash {
            font-family: monospace;
            background-color: #eee;
            padding: 2px 5px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo">碳排放管理系统</div>
            <div class="user-info">
                <span class="user-name">管理员</span>
                <a href="/login" class="logout-btn">退出登录</a>
            </div>
        </div>
    </header>
    
    <nav>
        <div class="nav-content">
            <a href="/admin" class="nav-item">系统概览</a>
            <a href="/admin_users" class="nav-item">用户管理</a>
            <a href="/admin_quotas" class="nav-item active">配额管理</a>
            <a href="/admin_verifications" class="nav-item">核查管理</a>
            <a href="/admin_transactions" class="nav-item">交易管理</a>
            <a href="/admin_settings" class="nav-item">系统配置</a>
            <a href="/admin_logs" class="nav-item">日志查看</a>
        </div>
    </nav>
    
    <div class="container">
        <div class="card-title">
            <h1>配额详情</h1>
            <div>
                <a href="/admin_quotas" class="btn btn-primary">返回列表</a>
            </div>
        </div>
        
        <div class="card">
            <div class="detail-section">
                <div class="detail-title">企业信息</div>
                
                <div class="detail-row">
                    <div class="detail-label">企业ID</div>
                    <div class="detail-value">1</div>
                </div>
                
                <div class="detail-row">
                    <div class="detail-label">企业名称</div>
                    <div class="detail-value">北京碳排放科技有限公司</div>
                </div>
                
                <div class="detail-row">
                    <div class="detail-label">行业</div>
                    <div class="detail-value">能源生产</div>
                </div>
                
                <div class="detail-row">
                    <div class="detail-label">统一社会信用代码</div>
                    <div class="detail-value">91110000123456789A</div>
                </div>
            </div>
            
            <div class="detail-section">
                <div class="detail-title">配额信息</div>
                
                <div class="detail-row">
                    <div class="detail-label">配额ID</div>
                    <div class="detail-value">1</div>
                </div>
                
                <div class="detail-row">
                    <div class="detail-label">年度</div>
                    <div class="detail-value">2023</div>
                </div>
                
                <div class="detail-row">
                    <div class="detail-label">年度配额</div>
                    <div class="detail-value">5,000 吨CO2e</div>
                </div>
                
                <div class="detail-row">
                    <div class="detail-label">已使用配额</div>
                    <div class="detail-value">2,350 吨CO2e</div>
                </div>
                
                <div class="detail-row">
                    <div class="detail-label">剩余配额</div>
                    <div class="detail-value">2,650 吨CO2e</div>
                </div>
                
                <div class="detail-row">
                    <div class="detail-label">分配时间</div>
                    <div class="detail-value">2023-01-01</div>
                </div>
                
                <div class="detail-row">
                    <div class="detail-label">分配方法</div>
                    <div class="detail-value">历史排放法</div>
                </div>
                
                <div class="detail-row">
                    <div class="detail-label">分配说明</div>
                    <div class="detail-value">根据企业2022年度排放量和行业基准线进行分配</div>
                </div>
            </div>
            
            <div class="detail-section">
                <div class="detail-title">配额使用趋势</div>
                
                <div class="chart-container">
                    <img src="https://via.placeholder.com/1160x300?text=配额使用趋势图表" alt="配额使用趋势图表" class="chart-placeholder">
                </div>
            </div>
            
            <div class="detail-section">
                <div class="detail-title">交易记录</div>
                
                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>交易类型</th>
                            <th>交易方</th>
                            <th>数量</th>
                            <th>价格</th>
                            <th>总价</th>
                            <th>交易时间</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>2001</td>
                            <td>出售</td>
                            <td>上海绿色能源有限公司</td>
                            <td>200 吨</td>
                            <td>45 元/吨</td>
                            <td>9,000 元</td>
                            <td>2023-04-15</td>
                            <td>已完成</td>
                        </tr>
                        <tr>
                            <td>2002</td>
                            <td>购买</td>
                            <td>广州环保科技有限公司</td>
                            <td>150 吨</td>
                            <td>50 元/吨</td>
                            <td>7,500 元</td>
                            <td>2023-05-20</td>
                            <td>待确认</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="detail-section">
                <div class="detail-title">区块链记录</div>
                
                <div class="blockchain-info">
                    <div class="detail-row">
                        <div class="detail-label">交易哈希</div>
                        <div class="detail-value">
                            <span class="blockchain-hash">0xa1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4e5f6a1b2</span>
                        </div>
                    </div>
                    
                    <div class="detail-row">
                        <div class="detail-label">区块号</div>
                        <div class="detail-value">15790123</div>
                    </div>
                    
                    <div class="detail-row">
                        <div class="detail-label">时间戳</div>
                        <div class="detail-value">2023-01-01 00:00:12</div>
                    </div>
                    
                    <div class="detail-row">
                        <div class="detail-label">数据哈希</div>
                        <div class="detail-value">
                            <span class="blockchain-hash">0xf6e5d4c3b2a1f6e5d4c3b2a1f6e5d4c3b2a1f6e5d4c3b2a1f6e5d4c3b2a1f6e5</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="actions">
                <a href="/admin_quotas" class="btn btn-primary">返回列表</a>
                <a href="#" class="btn btn-success">调整配额</a>
                <a href="/reports/quota_report.html" target="_blank" class="btn btn-primary">生成报告</a>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 导航切换 - 不需要额外的事件监听器，因为我们使用的是链接
            // 根据当前URL设置活动导航项
            const currentPath = window.location.pathname;
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                if (item.getAttribute('href') === currentPath) {
                    navItems.forEach(i => i.classList.remove('active'));
                    item.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>
