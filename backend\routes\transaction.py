"""
交易相关路由
"""

import hashlib
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity

from backend import db
from backend.models.user import User
from backend.models.transaction import Transaction
from backend.models.carbon_quota import CarbonQuota
from backend.models.activity import Activity
from backend.utils.auth import enterprise_required

transaction_bp = Blueprint('transaction', __name__)

@transaction_bp.route('', methods=['POST'])
@enterprise_required
def create_transaction():
    data = request.get_json()

    # 验证必填字段
    required_fields = ['buyer_id', 'amount', 'price']
    for field in required_fields:
        if field not in data:
            return jsonify({'error': f'缺少必填字段: {field}'}), 400

    seller_id = get_jwt_identity()
    buyer_id = data['buyer_id']
    amount = data['amount']
    price = data['price']

    # 验证买家是否存在
    buyer = User.query.get(buyer_id)
    if not buyer or buyer.role != 'enterprise':
        return jsonify({'error': '买家不存在或不是企业用户'}), 400

    # 验证卖家和买家不是同一个用户
    if seller_id == buyer_id:
        return jsonify({'error': '不能与自己交易'}), 400

    # 验证卖家是否有足够的配额
    current_year = datetime.now().year
    seller_quota = CarbonQuota.query.filter_by(enterprise_id=seller_id, year=current_year).first()

    if not seller_quota or seller_quota.current_amount < amount:
        return jsonify({'error': '配额不足'}), 400

    # 计算总价
    total_price = amount * price

    # 创建交易记录
    transaction = Transaction(
        seller_id=seller_id,
        buyer_id=buyer_id,
        amount=amount,
        price=price,
        total_price=total_price,
        transaction_time=datetime.now(),
        status='pending'
    )

    db.session.add(transaction)
    db.session.commit()

    # 生成哈希值
    hash_value = hashlib.sha256(f"{transaction.id}_{transaction.seller_id}_{transaction.buyer_id}_{transaction.amount}_{transaction.price}_{transaction.transaction_time.isoformat()}".encode()).hexdigest()

    # 提交到区块链
    blockchain_result = current_app.blockchain_client.create_transaction(
        transaction.id,
        transaction.seller_id,
        transaction.buyer_id,
        transaction.amount,
        transaction.price,
        transaction.transaction_time.isoformat(),
        hash_value
    )

    # 更新区块链信息
    transaction.blockchain_hash = blockchain_result['tx_hash']
    transaction.blockchain_block = blockchain_result['block_number']
    db.session.commit()

    # 记录活动
    activity = Activity(
        user_id=seller_id,
        activity_type='create_transaction',
        description=f'企业创建了交易，ID: {transaction.id}，金额: {transaction.total_price}'
    )
    db.session.add(activity)
    db.session.commit()

    return jsonify({
        'message': '交易创建成功',
        'transaction': transaction.to_dict()
    }), 201

@transaction_bp.route('', methods=['GET'])
@enterprise_required
def get_transactions():
    current_user_id = get_jwt_identity()

    # 获取与当前用户相关的所有交易
    transactions = Transaction.query.filter(
        (Transaction.seller_id == current_user_id) | (Transaction.buyer_id == current_user_id)
    ).all()

    return jsonify({
        'transactions': [transaction.to_dict() for transaction in transactions]
    }), 200

@transaction_bp.route('/<int:transaction_id>', methods=['GET'])
@enterprise_required
def get_transaction(transaction_id):
    current_user_id = get_jwt_identity()

    transaction = Transaction.query.get(transaction_id)
    if not transaction:
        return jsonify({'error': '交易不存在'}), 404

    # 检查权限
    if transaction.seller_id != current_user_id and transaction.buyer_id != current_user_id:
        return jsonify({'error': '无权访问此交易'}), 403

    return jsonify({
        'transaction': transaction.to_dict()
    }), 200

@transaction_bp.route('/<int:transaction_id>/confirm', methods=['POST'])
@enterprise_required
def confirm_transaction(transaction_id):
    current_user_id = get_jwt_identity()

    transaction = Transaction.query.get(transaction_id)
    if not transaction:
        return jsonify({'error': '交易不存在'}), 404

    # 检查权限
    if transaction.buyer_id != current_user_id:
        return jsonify({'error': '只有买家可以确认交易'}), 403

    # 检查状态
    if transaction.status != 'pending':
        return jsonify({'error': '只能确认待处理的交易'}), 400

    # 更新交易状态
    transaction.status = 'completed'

    # 更新配额
    current_year = datetime.now().year
    seller_quota = CarbonQuota.query.filter_by(enterprise_id=transaction.seller_id, year=current_year).first()
    buyer_quota = CarbonQuota.query.filter_by(enterprise_id=transaction.buyer_id, year=current_year).first()

    if not seller_quota or seller_quota.current_amount < transaction.amount:
        return jsonify({'error': '卖家配额不足'}), 400

    seller_quota.current_amount -= transaction.amount

    if not buyer_quota:
        # 如果买家没有当年的配额记录，创建一个
        buyer_quota = CarbonQuota(
            enterprise_id=transaction.buyer_id,
            year=current_year,
            initial_amount=transaction.amount,
            current_amount=transaction.amount,
            last_updated=datetime.now()
        )
        db.session.add(buyer_quota)
    else:
        buyer_quota.current_amount += transaction.amount
        buyer_quota.last_updated = datetime.now()

    # 更新区块链
    blockchain_result = current_app.blockchain_client.update_transaction_status(
        transaction.id,
        'completed'
    )

    db.session.commit()

    # 记录活动
    activity = Activity(
        user_id=current_user_id,
        activity_type='confirm_transaction',
        description=f'企业确认了交易，ID: {transaction.id}'
    )
    db.session.add(activity)
    db.session.commit()

    return jsonify({
        'message': '交易确认成功',
        'transaction': transaction.to_dict()
    }), 200

@transaction_bp.route('/<int:transaction_id>/cancel', methods=['POST'])
@enterprise_required
def cancel_transaction(transaction_id):
    current_user_id = get_jwt_identity()

    transaction = Transaction.query.get(transaction_id)
    if not transaction:
        return jsonify({'error': '交易不存在'}), 404

    # 检查权限
    if transaction.seller_id != current_user_id and transaction.buyer_id != current_user_id:
        return jsonify({'error': '无权取消此交易'}), 403

    # 检查状态
    if transaction.status != 'pending':
        return jsonify({'error': '只能取消待处理的交易'}), 400

    # 更新交易状态
    transaction.status = 'cancelled'

    # 更新区块链
    blockchain_result = current_app.blockchain_client.update_transaction_status(
        transaction.id,
        'cancelled'
    )

    db.session.commit()

    # 记录活动
    activity = Activity(
        user_id=current_user_id,
        activity_type='cancel_transaction',
        description=f'企业取消了交易，ID: {transaction.id}'
    )
    db.session.add(activity)
    db.session.commit()

    return jsonify({
        'message': '交易取消成功',
        'transaction': transaction.to_dict()
    }), 200
