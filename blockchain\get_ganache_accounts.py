"""
获取Ganache账户信息
用于获取Ganache中的账户地址和私钥，便于配置.env文件
"""

import argparse
from web3 import Web3
from web3.middleware import geth_poa_middleware

def get_ganache_accounts(node_url):
    """获取Ganache账户信息"""
    print(f"连接到Ganache节点: {node_url}")

    # 连接到Ganache
    w3 = Web3(Web3.HTTPProvider(node_url))

    # 如果使用的是PoA共识的网络，需要添加这个中间件
    w3.middleware_onion.inject(geth_poa_middleware, layer=0)

    # 检查连接
    if not w3.is_connected():
        raise Exception("无法连接到Ganache节点")

    print(f"成功连接到Ganache节点，当前区块号: {w3.eth.block_number}")

    # 获取账户列表
    accounts = w3.eth.accounts

    print(f"找到 {len(accounts)} 个账户:")

    # 打印账户信息
    for i, account in enumerate(accounts):
        balance = w3.eth.get_balance(account)
        balance_eth = w3.from_wei(balance, 'ether')
        print(f"\n账户 {i+1}:")
        print(f"  地址: {account}")
        print(f"  余额: {balance_eth} ETH")

    # 提示如何获取私钥
    print("\n注意: Ganache提供的账户私钥可以在Ganache UI界面中查看")
    print("或者，如果您使用的是Ganache CLI，私钥会在启动时显示")

    # 生成.env文件配置示例
    print("\n.env文件配置示例:")
    print("# 区块链配置 - Ganache")
    print(f"ETHEREUM_NODE_URL={node_url}")
    print("CONTRACT_ADDRESS=******************************************  # 部署合约后填写")
    print("\n# 管理员账户")
    if len(accounts) > 0:
        print(f"ADMIN_ADDRESS={accounts[0]}")
        print("ADMIN_PRIVATE_KEY=  # 填写Ganache中第一个账户的私钥")

    print("\n# 企业账户")
    if len(accounts) > 1:
        print(f"ENTERPRISE_1_ADDRESS={accounts[1]}")
        print("ENTERPRISE_1_KEY=  # 填写Ganache中第二个账户的私钥")

    if len(accounts) > 2:
        print(f"ENTERPRISE_2_ADDRESS={accounts[2]}")
        print("ENTERPRISE_2_KEY=  # 填写Ganache中第三个账户的私钥")

    print("\n# 核查机构账户")
    if len(accounts) > 3:
        print(f"VERIFIER_1_ADDRESS={accounts[3]}")
        print("VERIFIER_1_KEY=  # 填写Ganache中第四个账户的私钥")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='获取Ganache账户信息')
    parser.add_argument('--node', default='http://127.0.0.1:8545', help='Ganache节点URL')
    args = parser.parse_args()

    try:
        get_ganache_accounts(args.node)
    except Exception as e:
        print(f"获取账户信息失败: {str(e)}")

if __name__ == '__main__':
    main()
