<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放管理系统 - 企业管理</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/unified-nav.css') }}">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background: linear-gradient(to right, #1B5E20, #388E3C);
            color: white;
            padding: 20px 0;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-name {
            margin-right: 15px;
        }
        .logout-btn {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 30px;
            cursor: pointer;
            text-decoration: none;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .logout-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        nav {
            background: linear-gradient(to right, #2E7D32, #43A047);
            padding: 10px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
        }
        .nav-item {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            margin-right: 5px;
            border-radius: 30px;
            transition: all 0.3s ease;
        }
        .nav-item:hover, .nav-item.active {
            background: linear-gradient(to right, #1B5E20, #2E7D32);
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        .card {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            transform: translateY(-5px);
        }
        .card-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        th, td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        th {
            background: linear-gradient(to right, #43A047, #66BB6A);
            color: white;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 14px;
            letter-spacing: 0.5px;
        }
        tr:last-child td {
            border-bottom: none;
        }
        tr:nth-child(even) {
            background-color: rgba(0,0,0,0.02);
        }
        tr:hover {
            background-color: rgba(76, 175, 80, 0.05);
        }
        .status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            color: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            display: inline-block;
        }
        .status-active {
            background: linear-gradient(to right, #2ecc71, #27ae60);
        }
        .status-inactive {
            background: linear-gradient(to right, #e74c3c, #c0392b);
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        .btn-primary {
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            color: white;
        }
        .btn-success {
            background: linear-gradient(to right, #2ecc71, #27ae60);
            color: white;
        }
        .btn-danger {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
        }
        .filters {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }
        .filter-group {
            display: flex;
            align-items: center;
        }
        .filter-group label {
            margin-right: 5px;
            font-weight: bold;
        }
        .filter-group select {
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 30px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .filter-group select:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 2px 10px rgba(76, 175, 80, 0.2);
        }
        .enterprise-cards {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        .enterprise-card {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 25px;
            display: flex;
            flex-direction: column;
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .enterprise-card:hover {
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            transform: translateY(-5px);
        }
        .enterprise-name {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            background: linear-gradient(to right, #1B5E20, #388E3C);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .enterprise-info {
            margin-bottom: 5px;
            color: #7f8c8d;
        }
        .enterprise-info strong {
            color: #333;
        }
        .enterprise-actions {
            margin-top: auto;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo">碳排放管理系统</div>
            <div class="user-info">
                <span class="user-name">国家碳排放核查中心</span>
                <a href="/login" class="logout-btn">退出登录</a>
            </div>
        </div>
    </header>

    <div class="demo-nav">
        <div class="nav-content">
            <a href="/verifier" class="nav-item"><i class="fas fa-tachometer-alt mr-2"></i>仪表板</a>
            <a href="/verifier_tasks" class="nav-item"><i class="fas fa-tasks mr-2"></i>待核查任务</a>
            <a href="/verifier_records" class="nav-item"><i class="fas fa-clipboard-check mr-2"></i>核查记录</a>
            <a href="/verifier_enterprises" class="nav-item active"><i class="fas fa-building mr-2"></i>企业管理</a>
            <a href="/verifier_reports" class="nav-item"><i class="fas fa-file-alt mr-2"></i>报告生成</a>
            <a href="/verifier_blockchain_config" class="nav-item"><i class="fas fa-link mr-2"></i>区块链配置</a>
        </div>
    </div>

    <div class="container">
        <h1>企业管理</h1>

        <div class="filters">
            <div class="filter-group">
                <label for="industry-filter">行业:</label>
                <select id="industry-filter">
                    <option value="all">全部</option>
                    <option value="energy">能源生产</option>
                    <option value="manufacturing">制造业</option>
                    <option value="chemical">化工行业</option>
                    <option value="building">建筑业</option>
                    <option value="transportation">交通运输</option>
                </select>
            </div>
            <div class="filter-group">
                <label for="status-filter">状态:</label>
                <select id="status-filter">
                    <option value="all">全部</option>
                    <option value="active">活跃</option>
                    <option value="inactive">非活跃</option>
                </select>
            </div>
        </div>

        <div class="enterprise-cards">
            <div class="enterprise-card">
                <div class="enterprise-name">北京碳排放科技有限公司</div>
                <div class="enterprise-info"><strong>行业:</strong> 能源生产</div>
                <div class="enterprise-info"><strong>统一社会信用代码:</strong> 91110000123456789A</div>
                <div class="enterprise-info"><strong>注册时间:</strong> 2023-01-15</div>
                <div class="enterprise-info"><strong>状态:</strong> <span class="status status-active">活跃</span></div>
                <div class="enterprise-info"><strong>核查任务数:</strong> 8</div>
                <div class="enterprise-actions">
                    <a href="/enterprise_detail?id=1" class="btn btn-primary">查看详情</a>
                </div>
            </div>

            <div class="enterprise-card">
                <div class="enterprise-name">上海绿色能源有限公司</div>
                <div class="enterprise-info"><strong>行业:</strong> 制造业</div>
                <div class="enterprise-info"><strong>统一社会信用代码:</strong> 91310000123456789B</div>
                <div class="enterprise-info"><strong>注册时间:</strong> 2023-01-20</div>
                <div class="enterprise-info"><strong>状态:</strong> <span class="status status-active">活跃</span></div>
                <div class="enterprise-info"><strong>核查任务数:</strong> 5</div>
                <div class="enterprise-actions">
                    <a href="/enterprise_detail?id=2" class="btn btn-primary">查看详情</a>
                </div>
            </div>

            <div class="enterprise-card">
                <div class="enterprise-name">广州环保科技有限公司</div>
                <div class="enterprise-info"><strong>行业:</strong> 化工行业</div>
                <div class="enterprise-info"><strong>统一社会信用代码:</strong> 91440000123456789C</div>
                <div class="enterprise-info"><strong>注册时间:</strong> 2023-02-05</div>
                <div class="enterprise-info"><strong>状态:</strong> <span class="status status-active">活跃</span></div>
                <div class="enterprise-info"><strong>核查任务数:</strong> 6</div>
                <div class="enterprise-actions">
                    <a href="/enterprise_detail?id=3" class="btn btn-primary">查看详情</a>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-title">
                <span>企业列表</span>
                <a href="#" class="btn btn-primary add-enterprise-btn">添加企业</a>
            </div>
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>企业名称</th>
                        <th>行业</th>
                        <th>统一社会信用代码</th>
                        <th>注册时间</th>
                        <th>状态</th>
                        <th>核查任务数</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>北京碳排放科技有限公司</td>
                        <td>能源生产</td>
                        <td>91110000123456789A</td>
                        <td>2023-01-15</td>
                        <td><span class="status status-active">活跃</span></td>
                        <td>8</td>
                        <td><a href="/enterprise_detail?id=1" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>上海绿色能源有限公司</td>
                        <td>制造业</td>
                        <td>91310000123456789B</td>
                        <td>2023-01-20</td>
                        <td><span class="status status-active">活跃</span></td>
                        <td>5</td>
                        <td><a href="/enterprise_detail?id=2" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td>广州环保科技有限公司</td>
                        <td>化工行业</td>
                        <td>91440000123456789C</td>
                        <td>2023-02-05</td>
                        <td><span class="status status-active">活跃</span></td>
                        <td>6</td>
                        <td><a href="/enterprise_detail?id=3" class="btn btn-primary">查看</a></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 导航切换 - 不需要额外的事件监听器，因为我们使用的是链接
            // 根据当前URL设置活动导航项
            const currentPath = window.location.pathname;
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                if (item.getAttribute('href') === currentPath) {
                    navItems.forEach(i => i.classList.remove('active'));
                    item.classList.add('active');
                }
            });

            // 筛选功能
            const industryFilter = document.getElementById('industry-filter');
            const statusFilter = document.getElementById('status-filter');

            // 这里只是演示，实际应用中应该根据筛选条件过滤数据
            industryFilter.addEventListener('change', function() {
                console.log('行业筛选:', this.value);
            });

            statusFilter.addEventListener('change', function() {
                console.log('状态筛选:', this.value);
            });

            // 添加企业按钮点击事件
            const addEnterpriseBtn = document.querySelector('.add-enterprise-btn');
            addEnterpriseBtn.addEventListener('click', function(e) {
                e.preventDefault();
                alert('添加企业功能尚未实现');
            });

            // 添加企业行业分布图表
            const chartContainer = document.createElement('div');
            chartContainer.style.height = '300px';
            chartContainer.style.marginBottom = '30px';
            chartContainer.innerHTML = '<canvas id="industryDistributionChart"></canvas>';

            // 将图表插入到企业列表卡片上方
            const enterpriseListCard = document.querySelector('.card');
            enterpriseListCard.parentNode.insertBefore(chartContainer, enterpriseListCard);

            // 创建图表
            const ctx = document.getElementById('industryDistributionChart').getContext('2d');
            new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: ['能源生产', '制造业', '化工行业', '建筑业', '交通运输'],
                    datasets: [{
                        data: [8, 12, 6, 5, 4],
                        backgroundColor: [
                            'rgba(46, 204, 113, 0.8)',
                            'rgba(52, 152, 219, 0.8)',
                            'rgba(155, 89, 182, 0.8)',
                            'rgba(241, 196, 15, 0.8)',
                            'rgba(230, 126, 34, 0.8)'
                        ],
                        borderColor: [
                            'rgba(46, 204, 113, 1)',
                            'rgba(52, 152, 219, 1)',
                            'rgba(155, 89, 182, 1)',
                            'rgba(241, 196, 15, 1)',
                            'rgba(230, 126, 34, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                        },
                        title: {
                            display: true,
                            text: '企业行业分布',
                            font: {
                                size: 16
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.chart.data.datasets[0].data.reduce(
                                        (sum, value) => sum + value, 0
                                    );
                                    const percentage = Math.round((value * 100) / total);
                                    return `${label}: ${value} 家 (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });

            // 添加企业核查任务统计图表
            const barContainer = document.createElement('div');
            barContainer.style.height = '300px';
            barContainer.style.marginBottom = '30px';
            barContainer.innerHTML = '<canvas id="enterpriseTasksChart"></canvas>';

            // 将图表插入到饼图下方
            chartContainer.parentNode.insertBefore(barContainer, chartContainer.nextSibling);

            // 创建图表
            const barCtx = document.getElementById('enterpriseTasksChart').getContext('2d');
            new Chart(barCtx, {
                type: 'bar',
                data: {
                    labels: ['北京碳排放科技有限公司', '上海绿色能源有限公司', '广州环保科技有限公司', '深圳新能源有限公司', '其他企业'],
                    datasets: [
                        {
                            label: '已完成核查',
                            data: [6, 4, 5, 3, 7],
                            backgroundColor: 'rgba(46, 204, 113, 0.8)',
                            borderColor: 'rgba(46, 204, 113, 1)',
                            borderWidth: 1
                        },
                        {
                            label: '待核查',
                            data: [2, 1, 1, 2, 3],
                            backgroundColor: 'rgba(243, 156, 18, 0.8)',
                            borderColor: 'rgba(243, 156, 18, 1)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            stacked: true,
                        },
                        y: {
                            stacked: true,
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '核查任务数量'
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: '企业核查任务统计',
                            font: {
                                size: 16
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.dataset.label || '';
                                    const value = context.raw || 0;
                                    return `${label}: ${value} 个`;
                                }
                            }
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>
