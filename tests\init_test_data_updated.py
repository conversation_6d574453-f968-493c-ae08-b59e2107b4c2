"""
初始化测试数据脚本 (更新版)
用于创建测试用户和测试数据，适配实际数据库结构
"""

import os
import sys
import pymysql
import logging
from datetime import datetime, timedelta
from werkzeug.security import generate_password_hash

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('init_test_data')

# 数据库配置
DB_CONFIG = {
    'host': '***********',
    'port': 3306,
    'user': 'wuhong',
    'password': 'D7mH8rZ7a7Z2kJa8',
    'db': 'ces',
    'charset': 'utf8mb4'
}

def connect_to_database():
    """连接到数据库"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        logger.info(f"成功连接到数据库 {DB_CONFIG['db']} @ {DB_CONFIG['host']}")
        return connection
    except Exception as e:
        logger.error(f"连接数据库失败: {str(e)}")
        sys.exit(1)

def create_test_users(connection):
    """创建测试用户"""
    try:
        # 清空现有用户数据
        with connection.cursor() as cursor:
            cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
            cursor.execute("TRUNCATE TABLE user")
            cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
        
        # 创建测试用户
        users = [
            ('admin', '<EMAIL>', 'password123', 'admin', '碳排放管理系统', '123456789012345678', datetime.now(), datetime.now()),
            ('enterprise1', '<EMAIL>', 'password123', 'enterprise', '绿色能源有限公司', '123456789012345679', datetime.now(), datetime.now()),
            ('enterprise2', '<EMAIL>', 'password123', 'enterprise', '蓝天制造股份公司', '123456789012345680', datetime.now(), datetime.now()),
            ('enterprise3', '<EMAIL>', 'password123', 'enterprise', '未来科技集团', '123456789012345681', datetime.now(), datetime.now()),
            ('verifier1', '<EMAIL>', 'password123', 'verifier', '环保核查中心', '123456789012345682', datetime.now(), datetime.now()),
            ('verifier2', '<EMAIL>', 'password123', 'verifier', '绿色认证联盟', '123456789012345683', datetime.now(), datetime.now())
        ]
        
        user_ids = {}
        with connection.cursor() as cursor:
            for username, email, password, role, company_name, credit_code, created_at, last_login in users:
                # 生成密码哈希
                password_hash = generate_password_hash(password)
                
                # 插入用户
                cursor.execute(
                    """INSERT INTO user 
                    (username, email, password_hash, role, company_name, credit_code, created_at, last_login) 
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)""",
                    (username, email, password_hash, role, company_name, credit_code, created_at, last_login)
                )
                
                # 获取用户ID
                cursor.execute("SELECT LAST_INSERT_ID()")
                user_id = cursor.fetchone()[0]
                user_ids[username] = user_id
        
        connection.commit()
        logger.info("成功创建测试用户")
        return user_ids
    except Exception as e:
        logger.error(f"创建测试用户失败: {str(e)}")
        connection.rollback()
        return {}

def create_carbon_quotas(connection, user_ids):
    """创建碳配额记录"""
    try:
        # 清空现有碳配额数据
        with connection.cursor() as cursor:
            cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
            cursor.execute("TRUNCATE TABLE carbon_quota")
            cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
        
        # 创建碳配额记录
        quotas = [
            (user_ids['enterprise1'], 2025, 10000.0, 10000.0, datetime.now()),
            (user_ids['enterprise2'], 2025, 8000.0, 8000.0, datetime.now()),
            (user_ids['enterprise3'], 2025, 5000.0, 5000.0, datetime.now())
        ]
        
        with connection.cursor() as cursor:
            for enterprise_id, year, initial_amount, current_amount, last_updated in quotas:
                cursor.execute(
                    """INSERT INTO carbon_quota 
                    (enterprise_id, year, initial_amount, current_amount, last_updated) 
                    VALUES (%s, %s, %s, %s, %s)""",
                    (enterprise_id, year, initial_amount, current_amount, last_updated)
                )
        
        connection.commit()
        logger.info("成功创建碳配额记录")
        return True
    except Exception as e:
        logger.error(f"创建碳配额记录失败: {str(e)}")
        connection.rollback()
        return False

def create_emission_data(connection, user_ids):
    """创建排放数据"""
    try:
        # 清空现有排放数据
        with connection.cursor() as cursor:
            cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
            cursor.execute("TRUNCATE TABLE emission_data")
            cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
        
        # 创建排放数据
        now = datetime.now()
        emission_data = [
            # 企业1的排放数据
            (user_ids['enterprise1'], '工厂A', 1200.5, '吨CO2', '直接测量法', '2025-01-01', '2025-03-31', 'pending', now - timedelta(days=50), 'proof_files/enterprise1_emission1.pdf', None, None),
            (user_ids['enterprise1'], '工厂B', 800.3, '吨CO2', '间接计算法', '2025-04-01', '2025-06-30', 'pending', now - timedelta(days=40), 'proof_files/enterprise1_emission2.pdf', None, None),
            (user_ids['enterprise1'], '工厂C', 1500.8, '吨CO2', '直接测量法', '2025-07-01', '2025-09-30', 'pending', now - timedelta(days=30), 'proof_files/enterprise1_emission3.pdf', None, None),
            
            # 企业2的排放数据
            (user_ids['enterprise2'], '生产线A', 950.2, '吨CO2', '直接测量法', '2025-01-01', '2025-03-31', 'pending', now - timedelta(days=45), 'proof_files/enterprise2_emission1.pdf', None, None),
            (user_ids['enterprise2'], '生产线B', 1100.7, '吨CO2', '间接计算法', '2025-04-01', '2025-06-30', 'pending', now - timedelta(days=35), 'proof_files/enterprise2_emission2.pdf', None, None),
            
            # 企业3的排放数据
            (user_ids['enterprise3'], '研发中心', 600.1, '吨CO2', '直接测量法', '2025-01-01', '2025-03-31', 'pending', now - timedelta(days=48), 'proof_files/enterprise3_emission1.pdf', None, None),
            (user_ids['enterprise3'], '数据中心', 750.4, '吨CO2', '间接计算法', '2025-04-01', '2025-06-30', 'pending', now - timedelta(days=38), 'proof_files/enterprise3_emission2.pdf', None, None)
        ]
        
        emission_ids = []
        with connection.cursor() as cursor:
            for enterprise_id, source, amount, unit, method, period_start, period_end, status, submission_time, proof_file, blockchain_hash, blockchain_block in emission_data:
                cursor.execute(
                    """INSERT INTO emission_data 
                    (enterprise_id, emission_source, emission_amount, emission_unit, calculation_method, 
                     emission_period_start, emission_period_end, status, submission_time, proof_file_path, 
                     blockchain_hash, blockchain_block) 
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)""",
                    (enterprise_id, source, amount, unit, method, period_start, period_end, status, 
                     submission_time, proof_file, blockchain_hash, blockchain_block)
                )
                cursor.execute("SELECT LAST_INSERT_ID()")
                emission_ids.append(cursor.fetchone()[0])
        
        connection.commit()
        logger.info("成功创建排放数据")
        return emission_ids
    except Exception as e:
        logger.error(f"创建排放数据失败: {str(e)}")
        connection.rollback()
        return []

def create_verifications(connection, user_ids, emission_ids):
    """创建核查记录"""
    try:
        # 清空现有核查记录
        with connection.cursor() as cursor:
            cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
            cursor.execute("TRUNCATE TABLE verification")
            cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
        
        # 创建核查记录
        now = datetime.now()
        verifications = [
            # 核查机构1的核查记录
            (emission_ids[0], user_ids['verifier1'], 'approved', '数据符合标准，排放量在允许范围内', now - timedelta(days=20), None, None),
            (emission_ids[2], user_ids['verifier1'], 'rejected', '数据不完整，需要补充更多证明材料', now - timedelta(days=10), None, None),
            (emission_ids[4], user_ids['verifier1'], 'approved', '数据准确，计算方法正确', now - timedelta(days=15), None, None),
            
            # 核查机构2的核查记录
            (emission_ids[1], user_ids['verifier2'], 'approved', '排放数据准确，符合行业标准', now - timedelta(days=18), None, None),
            (emission_ids[3], user_ids['verifier2'], 'pending', '正在核查中，需要额外信息', now - timedelta(days=25), None, None),
            (emission_ids[5], user_ids['verifier2'], 'approved', '数据完整，排放量符合预期', now - timedelta(days=22), None, None)
        ]
        
        with connection.cursor() as cursor:
            for emission_data_id, verifier_id, conclusion, comments, verification_time, blockchain_hash, blockchain_block in verifications:
                cursor.execute(
                    """INSERT INTO verification 
                    (emission_data_id, verifier_id, conclusion, comments, verification_time, blockchain_hash, blockchain_block) 
                    VALUES (%s, %s, %s, %s, %s, %s, %s)""",
                    (emission_data_id, verifier_id, conclusion, comments, verification_time, blockchain_hash, blockchain_block)
                )
        
        connection.commit()
        logger.info("成功创建核查记录")
        return True
    except Exception as e:
        logger.error(f"创建核查记录失败: {str(e)}")
        connection.rollback()
        return False

def create_transactions(connection, user_ids):
    """创建交易记录"""
    try:
        # 清空现有交易记录
        with connection.cursor() as cursor:
            cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
            cursor.execute("TRUNCATE TABLE transaction")
            cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
        
        # 创建交易记录
        now = datetime.now()
        transactions = [
            # 企业间的交易
            (user_ids['enterprise1'], user_ids['enterprise2'], 500.0, 20.5, 500.0 * 20.5, now - timedelta(days=30), 'pending', None, None),
            (user_ids['enterprise2'], user_ids['enterprise3'], 300.0, 22.0, 300.0 * 22.0, now - timedelta(days=25), 'completed', None, None),
            (user_ids['enterprise3'], user_ids['enterprise1'], 200.0, 21.0, 200.0 * 21.0, now - timedelta(days=20), 'cancelled', None, None),
            (user_ids['enterprise1'], user_ids['enterprise3'], 400.0, 19.5, 400.0 * 19.5, now - timedelta(days=15), 'pending', None, None)
        ]
        
        with connection.cursor() as cursor:
            for seller_id, buyer_id, amount, price, total_price, transaction_time, status, blockchain_hash, blockchain_block in transactions:
                cursor.execute(
                    """INSERT INTO transaction 
                    (seller_id, buyer_id, amount, price, total_price, transaction_time, status, blockchain_hash, blockchain_block) 
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)""",
                    (seller_id, buyer_id, amount, price, total_price, transaction_time, status, blockchain_hash, blockchain_block)
                )
        
        connection.commit()
        logger.info("成功创建交易记录")
        return True
    except Exception as e:
        logger.error(f"创建交易记录失败: {str(e)}")
        connection.rollback()
        return False

def create_penalties(connection, user_ids):
    """创建惩罚记录"""
    try:
        # 清空现有惩罚记录
        with connection.cursor() as cursor:
            cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
            cursor.execute("TRUNCATE TABLE penalty")
            cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
        
        # 创建惩罚记录
        now = datetime.now()
        penalties = [
            (user_ids['enterprise1'], 5000.0, '排放数据造假', now - timedelta(days=10), 'pending', None, None),
            (user_ids['enterprise2'], 3000.0, '未按时提交排放数据', now - timedelta(days=8), 'completed', None, None),
            (user_ids['enterprise3'], 2000.0, '超额排放', now - timedelta(days=5), 'pending', None, None)
        ]
        
        with connection.cursor() as cursor:
            for enterprise_id, amount, reason, penalty_time, status, blockchain_hash, blockchain_block in penalties:
                cursor.execute(
                    """INSERT INTO penalty 
                    (enterprise_id, amount, reason, penalty_time, status, blockchain_hash, blockchain_block) 
                    VALUES (%s, %s, %s, %s, %s, %s, %s)""",
                    (enterprise_id, amount, reason, penalty_time, status, blockchain_hash, blockchain_block)
                )
        
        connection.commit()
        logger.info("成功创建惩罚记录")
        return True
    except Exception as e:
        logger.error(f"创建惩罚记录失败: {str(e)}")
        connection.rollback()
        return False

def create_activities(connection, user_ids):
    """创建活动记录"""
    try:
        # 清空现有活动记录
        with connection.cursor() as cursor:
            cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
            cursor.execute("TRUNCATE TABLE activity")
            cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
        
        # 创建活动记录
        now = datetime.now()
        activities = [
            (user_ids['admin'], 'login', '管理员登录系统', now - timedelta(days=5)),
            (user_ids['enterprise1'], 'data_submission', '企业提交排放数据', now - timedelta(days=4)),
            (user_ids['verifier1'], 'verification', '核查机构提交核查结果', now - timedelta(days=3)),
            (user_ids['enterprise2'], 'transaction', '企业发起碳交易', now - timedelta(days=2)),
            (user_ids['admin'], 'penalty', '管理员创建惩罚记录', now - timedelta(days=1)),
            (user_ids['enterprise3'], 'login', '企业登录系统', now - timedelta(hours=12)),
            (user_ids['verifier2'], 'verification', '核查机构提交核查结果', now - timedelta(hours=6))
        ]
        
        with connection.cursor() as cursor:
            for user_id, activity_type, description, timestamp in activities:
                cursor.execute(
                    """INSERT INTO activity 
                    (user_id, activity_type, description, timestamp) 
                    VALUES (%s, %s, %s, %s)""",
                    (user_id, activity_type, description, timestamp)
                )
        
        connection.commit()
        logger.info("成功创建活动记录")
        return True
    except Exception as e:
        logger.error(f"创建活动记录失败: {str(e)}")
        connection.rollback()
        return False

def create_reports(connection, user_ids):
    """创建报告记录"""
    try:
        # 清空现有报告记录
        with connection.cursor() as cursor:
            cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
            cursor.execute("TRUNCATE TABLE report")
            cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
        
        # 创建报告记录
        now = datetime.now()
        reports = [
            (user_ids['enterprise1'], '2025年第一季度碳排放报告', 'quarterly', '本季度碳排放总量为2500吨CO2，较上季度减少5%...', now - timedelta(days=40), '2025-01-01', '2025-03-31', 'reports/enterprise1_q1_2025.pdf'),
            (user_ids['enterprise1'], '2025年上半年碳排放报告', 'semi_annual', '上半年碳排放总量为5000吨CO2，较去年同期减少8%...', now - timedelta(days=20), '2025-01-01', '2025-06-30', 'reports/enterprise1_h1_2025.pdf'),
            (user_ids['enterprise2'], '2025年第一季度碳排放报告', 'quarterly', '本季度碳排放总量为1800吨CO2，较上季度减少3%...', now - timedelta(days=38), '2025-01-01', '2025-03-31', 'reports/enterprise2_q1_2025.pdf'),
            (user_ids['enterprise3'], '2025年第一季度碳排放报告', 'quarterly', '本季度碳排放总量为1200吨CO2，较上季度减少7%...', now - timedelta(days=35), '2025-01-01', '2025-03-31', 'reports/enterprise3_q1_2025.pdf')
        ]
        
        with connection.cursor() as cursor:
            for enterprise_id, title, report_type, content, created_at, period_start, period_end, file_path in reports:
                cursor.execute(
                    """INSERT INTO report 
                    (enterprise_id, title, report_type, content, created_at, period_start, period_end, file_path) 
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)""",
                    (enterprise_id, title, report_type, content, created_at, period_start, period_end, file_path)
                )
        
        connection.commit()
        logger.info("成功创建报告记录")
        return True
    except Exception as e:
        logger.error(f"创建报告记录失败: {str(e)}")
        connection.rollback()
        return False

def show_test_data_summary(connection):
    """显示测试数据摘要"""
    try:
        tables = ['user', 'carbon_quota', 'emission_data', 'verification', 'transaction', 'penalty', 'activity', 'report']
        
        print("\n测试数据摘要:")
        print("=" * 40)
        
        with connection.cursor() as cursor:
            for table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"{table.ljust(20)}: {count} 条记录")
        
        print("=" * 40)
        return True
    except Exception as e:
        logger.error(f"获取数据摘要失败: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("开始初始化测试数据...")
    
    # 连接数据库
    connection = connect_to_database()
    
    # 创建测试用户
    user_ids = create_test_users(connection)
    if not user_ids:
        logger.error("创建测试用户失败，退出程序")
        connection.close()
        sys.exit(1)
    
    # 创建碳配额记录
    if not create_carbon_quotas(connection, user_ids):
        logger.error("创建碳配额记录失败，退出程序")
        connection.close()
        sys.exit(1)
    
    # 创建排放数据
    emission_ids = create_emission_data(connection, user_ids)
    if not emission_ids:
        logger.error("创建排放数据失败，退出程序")
        connection.close()
        sys.exit(1)
    
    # 创建核查记录
    if not create_verifications(connection, user_ids, emission_ids):
        logger.error("创建核查记录失败，退出程序")
        connection.close()
        sys.exit(1)
    
    # 创建交易记录
    if not create_transactions(connection, user_ids):
        logger.error("创建交易记录失败，退出程序")
        connection.close()
        sys.exit(1)
    
    # 创建惩罚记录
    if not create_penalties(connection, user_ids):
        logger.error("创建惩罚记录失败，退出程序")
        connection.close()
        sys.exit(1)
    
    # 创建活动记录
    if not create_activities(connection, user_ids):
        logger.error("创建活动记录失败，退出程序")
        connection.close()
        sys.exit(1)
    
    # 创建报告记录
    if not create_reports(connection, user_ids):
        logger.error("创建报告记录失败，退出程序")
        connection.close()
        sys.exit(1)
    
    # 显示测试数据摘要
    show_test_data_summary(connection)
    
    # 关闭连接
    connection.close()
    
    logger.info("测试数据初始化完成")
    print("\n测试账户信息:")
    print("=" * 40)
    print("管理员账户: admin / password123")
    print("企业账户1: enterprise1 / password123")
    print("企业账户2: enterprise2 / password123")
    print("企业账户3: enterprise3 / password123")
    print("核查机构账户1: verifier1 / password123")
    print("核查机构账户2: verifier2 / password123")
    print("=" * 40)
    print("\n现在您可以使用这些测试账户登录系统，测试各项功能。")

if __name__ == "__main__":
    main()
