"""
预测分析模块路由
处理碳排放预测相关的API请求
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
import os
import pandas as pd
from datetime import datetime

from models import db, User, EmissionData, PredictionModel, Activity
from utils.prediction import EmissionPredictor

prediction_bp = Blueprint('prediction', __name__)

@prediction_bp.route('/train', methods=['POST'])
@jwt_required()
def train_model():
    """训练预测模型"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    if not user:
        return jsonify({'error': '用户不存在'}), 404
    
    # 验证权限
    if user.role != 'admin':
        return jsonify({'error': '只有管理员可以训练模型'}), 403
    
    # 获取请求参数
    data = request.json or {}
    model_name = data.get('name', '碳排放预测模型')
    description = data.get('description', '基于历史排放数据的预测模型')
    
    # 获取所有已验证的排放数据
    emissions = EmissionData.query.filter_by(status='verified').all()
    
    if len(emissions) < 30:
        return jsonify({'error': '数据量不足，无法训练模型，至少需要30条数据'}), 400
    
    # 转换为列表字典
    emission_data = [e.to_dict() for e in emissions]
    
    # 创建并训练模型
    predictor = EmissionPredictor()
    
    try:
        result = predictor.train(emission_data)
        
        # 保存模型
        model_dir = os.path.join(current_app.instance_path, 'models')
        os.makedirs(model_dir, exist_ok=True)
        model_path = os.path.join(model_dir, f'emission_predictor_{datetime.now().strftime("%Y%m%d_%H%M%S")}.joblib')
        predictor.save_model(model_path)
        
        # 保存模型记录
        model = PredictionModel(
            name=model_name,
            description=description,
            model_path=model_path,
            last_trained=datetime.now()
        )
        model.set_metrics(result)
        
        db.session.add(model)
        
        # 记录活动
        activity = Activity(
            user_id=current_user_id,
            activity_type='model_train',
            description=f"训练了预测模型 '{model_name}'，R²: {result['r2']:.4f}"
        )
        db.session.add(activity)
        
        db.session.commit()
        
        return jsonify({
            'message': '模型训练成功',
            'model_id': model.id,
            'metrics': result
        }), 200
    except Exception as e:
        return jsonify({'error': f'模型训练失败: {str(e)}'}), 500

@prediction_bp.route('/models', methods=['GET'])
@jwt_required()
def get_models():
    """获取预测模型列表"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    if not user:
        return jsonify({'error': '用户不存在'}), 404
    
    # 获取所有模型
    models = PredictionModel.query.order_by(PredictionModel.last_trained.desc()).all()
    
    return jsonify([m.to_dict() for m in models]), 200

@prediction_bp.route('/predict', methods=['POST'])
@jwt_required()
def predict_emissions():
    """预测碳排放"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    if not user:
        return jsonify({'error': '用户不存在'}), 404
    
    data = request.json
    
    if not data:
        return jsonify({'error': '缺少预测数据'}), 400
    
    # 获取最新模型
    model = PredictionModel.query.order_by(PredictionModel.last_trained.desc()).first()
    
    if not model:
        return jsonify({'error': '预测模型尚未训练'}), 400
    
    # 加载模型
    predictor = EmissionPredictor(model.model_path)
    
    try:
        predictions = predictor.predict(data)
        
        # 组合结果
        result = []
        for i, item in enumerate(data):
            result.append({
                'input': item,
                'predicted_emission': float(predictions[i])
            })
        
        return jsonify({
            'predictions': result,
            'model': {
                'id': model.id,
                'name': model.name,
                'last_trained': model.last_trained.isoformat() if model.last_trained else None
            }
        }), 200
    except Exception as e:
        return jsonify({'error': f'预测失败: {str(e)}'}), 400

@prediction_bp.route('/trend', methods=['GET'])
@jwt_required()
def get_emission_trend():
    """获取排放趋势预测"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    if not user:
        return jsonify({'error': '用户不存在'}), 404
    
    # 获取查询参数
    enterprise_id = request.args.get('enterprise_id', type=int)
    months = request.args.get('months', 12, type=int)
    
    # 构建查询
    query = EmissionData.query.filter_by(status='verified')
    
    if enterprise_id:
        query = query.filter_by(enterprise_id=enterprise_id)
    elif user.role == 'enterprise':
        query = query.filter_by(enterprise_id=current_user_id)
    
    # 获取历史数据
    emissions = query.order_by(EmissionData.emission_period_start).all()
    
    if not emissions:
        return jsonify({'error': '没有足够的历史数据'}), 400
    
    # 转换为DataFrame
    df = pd.DataFrame([e.to_dict() for e in emissions])
    df['emission_period_start'] = pd.to_datetime(df['emission_period_start'])
    df = df.sort_values('emission_period_start')
    
    # 按月聚合
    monthly = df.groupby(pd.Grouper(key='emission_period_start', freq='M')).agg({
        'emission_amount': 'sum'
    }).reset_index()
    monthly['date'] = monthly['emission_period_start'].dt.strftime('%Y-%m')
    
    # 获取最近几个月的数据
    recent = monthly.tail(months)
    
    # 获取最新模型
    model = PredictionModel.query.order_by(PredictionModel.last_trained.desc()).first()
    future_predictions = []
    
    if model:
        try:
            # 加载模型
            predictor = EmissionPredictor(model.model_path)
            
            # 获取最后一条数据作为预测模板
            last_emission = emissions[-1].to_dict()
            
            # 预测未来6个月
            future_predictions = predictor.predict_future(last_emission, periods=6)
        except Exception as e:
            # 预测失败时不返回未来预测
            pass
    
    return jsonify({
        'historical': [
            {
                'date': row['date'],
                'emission_amount': float(row['emission_amount']),
                'is_prediction': False
            } for _, row in recent.iterrows()
        ],
        'future': future_predictions
    }), 200
