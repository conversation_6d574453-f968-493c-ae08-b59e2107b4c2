{% extends 'base.html' %}

{% block title %}碳排放核查系统 - 企业演示{% endblock %}

{% block head %}
<style>
    body {
        font-family: 'Arial', sans-serif;
        margin: 0;
        padding: 0;
        background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
        color: #333;
        min-height: 100vh;
    }
    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    .demo-header {
        background: linear-gradient(to right, #1B5E20, #388E3C);
        color: white;
        padding: 20px 0;
        box-shadow: 0 4px 10px rgba(0,0,0,0.2);
    }
    .header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }
    .logo {
        font-size: 24px;
        font-weight: bold;
    }
    .user-info {
        display: flex;
        align-items: center;
    }
    .user-name {
        margin-right: 15px;
    }
    .logout-btn {
        background-color: #e74c3c;
        color: white;
        border: none;
        padding: 8px 15px;
        border-radius: 4px;
        cursor: pointer;
        text-decoration: none;
    }
    .demo-nav {
        background: linear-gradient(to right, #2E7D32, #43A047);
        padding: 10px 0;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }
    .nav-content {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
        display: flex;
    }
    .nav-item {
        color: white;
        text-decoration: none;
        padding: 10px 20px;
        margin-right: 5px;
        border-radius: 30px;
        transition: all 0.3s ease;
    }
    .nav-item:hover, .nav-item.active {
        background: linear-gradient(to right, #1B5E20, #2E7D32);
        box-shadow: 0 4px 10px rgba(0,0,0,0.15);
        transform: translateY(-2px);
    }
    .card {
        background: linear-gradient(to bottom, #ffffff, #f9f9f9);
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        padding: 25px;
        margin-bottom: 25px;
        border: 1px solid rgba(0,0,0,0.05);
        transition: all 0.3s ease;
    }
    .card:hover {
        box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        transform: translateY(-5px);
    }
    .card-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 15px;
        color: #2c3e50;
    }
    .dashboard {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
    }
    .stat-card {
        text-align: center;
        padding: 30px 20px;
    }
    .stat-value {
        font-size: 36px;
        font-weight: bold;
        background: linear-gradient(to right, #4CAF50, #2E7D32);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin: 15px 0;
        text-shadow: 0 2px 5px rgba(0,0,0,0.05);
    }
    .stat-label {
        color: #7f8c8d;
    }
    table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    th, td {
        padding: 15px 20px;
        text-align: left;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }
    th {
        background: linear-gradient(to right, #43A047, #66BB6A);
        color: white;
        font-weight: bold;
        text-transform: uppercase;
        font-size: 14px;
        letter-spacing: 0.5px;
    }
    tr:last-child td {
        border-bottom: none;
    }
    tr:nth-child(even) {
        background-color: rgba(0,0,0,0.02);
    }
    tr:hover {
        background-color: rgba(76, 175, 80, 0.05);
    }
    .status {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        display: inline-block;
    }
    .status-pending {
        background: linear-gradient(to right, #f39c12, #e67e22);
        color: white;
    }
    .status-verified {
        background: linear-gradient(to right, #2ecc71, #27ae60);
        color: white;
    }
    .status-rejected {
        background: linear-gradient(to right, #e74c3c, #c0392b);
        color: white;
    }
    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 30px;
        cursor: pointer;
        font-weight: bold;
        transition: all 0.3s ease;
        box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        text-decoration: none;
        display: inline-block;
    }
    .btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 7px 15px rgba(0,0,0,0.2);
    }
    .btn-primary {
        background: linear-gradient(to right, #4CAF50, #2E7D32);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="demo-header">
    <div class="header-content">
        <div class="logo">碳排放管理系统</div>
        <div class="user-info">
            <span class="user-name">北京碳排放科技有限公司</span>
            <a href="/login" class="logout-btn">退出登录</a>
        </div>
    </div>
</div>

<div class="demo-nav">
    <div class="nav-content">
        <a href="/demo/enterprise" class="nav-item active">仪表板</a>
        <a href="/demo/enterprise/emissions" class="nav-item">排放数据</a>
        <a href="/demo/enterprise/verifications" class="nav-item">核查记录</a>
        <a href="/demo/enterprise/transactions" class="nav-item">碳交易</a>
        <a href="/demo/enterprise/calculator" class="nav-item">碳计算器</a>
        <a href="/demo/enterprise/predictions" class="nav-item">预测分析</a>
        <a href="/demo/enterprise/reports" class="nav-item">报告生成</a>
    </div>
</div>

<div class="container">
    <h1>企业仪表板</h1>

    <div class="dashboard">
        <div class="card stat-card">
            <div class="stat-label">总排放量</div>
            <div class="stat-value">2,450</div>
            <div class="stat-label">吨CO2e</div>
        </div>
        <div class="card stat-card">
            <div class="stat-label">当前配额</div>
            <div class="stat-value">3,200</div>
            <div class="stat-label">吨CO2e</div>
        </div>
        <div class="card stat-card">
            <div class="stat-label">核查通过率</div>
            <div class="stat-value">85%</div>
            <div class="stat-label">本年度</div>
        </div>
    </div>

    <div class="card">
        <div class="card-title">
            <span>最近排放数据</span>
            <a href="/demo/enterprise/emissions" class="btn btn-primary" style="float: right;">提交新数据</a>
        </div>
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>排放源</th>
                    <th>排放量</th>
                    <th>计算方法</th>
                    <th>排放期间</th>
                    <th>状态</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>1001</td>
                    <td>燃煤锅炉</td>
                    <td>450 吨CO2e</td>
                    <td>排放因子法</td>
                    <td>2025-01-01 至 2025-01-31</td>
                    <td><span class="status status-verified">已核查</span></td>
                    <td><a href="/demo/enterprise/emission_detail" class="btn btn-primary">查看</a></td>
                </tr>
                <tr>
                    <td>1002</td>
                    <td>天然气锅炉</td>
                    <td>320 吨CO2e</td>
                    <td>排放因子法</td>
                    <td>2025-02-01 至 2025-02-28</td>
                    <td><span class="status status-pending">待核查</span></td>
                    <td><a href="/demo/enterprise/emission_detail" class="btn btn-primary">查看</a></td>
                </tr>
                <tr>
                    <td>1003</td>
                    <td>工业生产过程</td>
                    <td>780 吨CO2e</td>
                    <td>物料平衡法</td>
                    <td>2025-03-01 至 2025-03-31</td>
                    <td><span class="status status-rejected">已拒绝</span></td>
                    <td><a href="/demo/enterprise/emission_detail" class="btn btn-primary">查看</a></td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="card">
        <div class="card-title">最近交易记录</div>
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>交易方</th>
                    <th>数量</th>
                    <th>价格</th>
                    <th>总价</th>
                    <th>交易时间</th>
                    <th>状态</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>2001</td>
                    <td>上海绿色能源有限公司</td>
                    <td>200 吨</td>
                    <td>45 元/吨</td>
                    <td>9,000 元</td>
                    <td>2025-04-15</td>
                    <td><span class="status status-verified">已完成</span></td>
                    <td><a href="/demo/enterprise/transaction_detail" class="btn btn-primary">查看</a></td>
                </tr>
                <tr>
                    <td>2002</td>
                    <td>广州环保科技有限公司</td>
                    <td>150 吨</td>
                    <td>50 元/吨</td>
                    <td>7,500 元</td>
                    <td>2025-05-20</td>
                    <td><span class="status status-pending">待确认</span></td>
                    <td><a href="/demo/enterprise/transaction_detail" class="btn btn-primary">查看</a></td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 导航切换
        const currentPath = window.location.pathname;
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            if (item.getAttribute('href') === currentPath) {
                navItems.forEach(i => i.classList.remove('active'));
                item.classList.add('active');
            }
        });
    });
</script>
{% endblock %}
