<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放核查系统 - 企业区块链配置（演示）</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <style>
        .gradient-custom {
            background: linear-gradient(135deg, #43a047 0%, #1de9b6 100%);
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            margin-bottom: 20px;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }
        .card-header {
            border-radius: 15px 15px 0 0 !important;
            background: linear-gradient(135deg, #43a047 0%, #1de9b6 100%) !important;
            color: white;
            font-weight: 600;
            padding: 1rem 1.5rem;
            border: none;
        }
        .btn-success {
            background: linear-gradient(135deg, #43a047 0%, #1de9b6 100%);
            border: none;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #388e3c 0%, #00bfa5 100%);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container">
            <a class="navbar-brand" href="/demo"><i class="fas fa-leaf me-2"></i>碳排放核查系统（演示）</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard"><i class="fas fa-tachometer-alt me-1"></i>仪表板</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/emissions"><i class="fas fa-cloud me-1"></i>排放数据</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/verifications"><i class="fas fa-clipboard-check me-1"></i>核查记录</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/transactions"><i class="fas fa-exchange-alt me-1"></i>碳交易</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/calculator"><i class="fas fa-calculator me-1"></i>碳计算器</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/predictions"><i class="fas fa-chart-line me-1"></i>预测分析</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/reports"><i class="fas fa-file-alt me-1"></i>报告生成</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/enterprise_blockchain_config"><i class="fas fa-link me-1"></i>区块链配置</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>北京碳排放科技有限公司
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-id-card me-1"></i>企业资料</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-1"></i>账户设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/demo"><i class="fas fa-sign-out-alt me-1"></i>返回演示首页</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 演示提示 -->
    <div class="container mt-3">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>这是<strong>演示页面</strong>，展示了企业用户区块链配置的界面和功能。在实际系统中，企业可以配置区块链连接信息，查看区块链状态，并进行区块链操作。
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-link me-2"></i>区块链配置</h1>
            <div>
                <span class="badge bg-primary">当前时间: <span id="current-time"></span></span>
            </div>
        </div>

        <!-- 区块链状态 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header gradient-custom">
                        <h5 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i>区块链状态</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                连接状态
                                <span class="badge bg-success rounded-pill"><i class="fas fa-check"></i> 已连接</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                网络
                                <span class="badge bg-primary rounded-pill">本地测试网</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                当前区块
                                <span class="badge bg-info rounded-pill">12345</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                智能合约地址
                                <span class="text-muted">0x5FbDB2315678afecb367f032d93F642f64180aa3</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                企业区块链地址
                                <span class="text-muted">0x70997970C51812dc3A010C7d01b50e0d17dc79C8</span>
                            </li>
                        </ul>
                    </div>
                    <div class="card-footer bg-light">
                        <button type="button" class="btn btn-success" id="refreshStatusBtn">
                            <i class="fas fa-sync-alt me-1"></i>刷新状态
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header gradient-custom">
                        <h5 class="card-title mb-0"><i class="fas fa-cog me-2"></i>区块链配置</h5>
                    </div>
                    <div class="card-body">
                        <form id="blockchainConfigForm">
                            <div class="mb-3">
                                <label for="ethereumNodeUrl" class="form-label">以太坊节点URL</label>
                                <input type="text" class="form-control" id="ethereumNodeUrl" name="ethereum_node_url" value="http://127.0.0.1:8545" required>
                                <div class="form-text">例如: http://127.0.0.1:8545 (本地Ganache)</div>
                            </div>
                            <div class="mb-3">
                                <label for="privateKey" class="form-label">企业私钥</label>
                                <input type="password" class="form-control" id="privateKey" name="private_key" value="0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80">
                                <div class="form-text">用于签署交易的私钥 (请勿在生产环境中明文存储)</div>
                            </div>
                            <div class="mb-3">
                                <label for="contractAddress" class="form-label">智能合约地址</label>
                                <input type="text" class="form-control" id="contractAddress" name="contract_address" value="0x5FbDB2315678afecb367f032d93F642f64180aa3">
                                <div class="form-text">已部署的碳排放智能合约地址</div>
                            </div>
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="simulationMode" name="simulation_mode" checked>
                                <label class="form-check-label" for="simulationMode">启用模拟模式 (无需真实区块链)</label>
                            </div>
                        </form>
                    </div>
                    <div class="card-footer bg-light">
                        <button type="button" class="btn btn-success" id="saveConfigBtn">
                            <i class="fas fa-save me-1"></i>保存配置
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 区块链交易记录 -->
        <div class="card mb-4">
            <div class="card-header gradient-custom">
                <h5 class="card-title mb-0"><i class="fas fa-history me-2"></i>最近区块链交易记录</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>交易哈希</th>
                                <th>类型</th>
                                <th>区块号</th>
                                <th>时间</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef</code></td>
                                <td>提交排放数据</td>
                                <td>12345</td>
                                <td>2025-04-01 10:30:45</td>
                                <td><span class="badge bg-success">成功</span></td>
                            </tr>
                            <tr>
                                <td><code>0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890</code></td>
                                <td>购买碳配额</td>
                                <td>12350</td>
                                <td>2025-04-02 14:20:15</td>
                                <td><span class="badge bg-success">成功</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 区块链操作 -->
        <div class="card mb-4">
            <div class="card-header gradient-custom">
                <h5 class="card-title mb-0"><i class="fas fa-tools me-2"></i>区块链操作</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="d-grid">
                            <button type="button" class="btn btn-success" id="testConnectionBtn">
                                <i class="fas fa-plug me-2"></i>测试连接
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="d-grid">
                            <button type="button" class="btn btn-success" id="registerAddressBtn">
                                <i class="fas fa-user-plus me-2"></i>注册企业地址
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="d-grid">
                            <button type="button" class="btn btn-success" id="viewBalanceBtn">
                                <i class="fas fa-wallet me-2"></i>查看账户余额
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">© 2025 碳排放核查系统 | 基于区块链技术</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 显示当前时间
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleString('zh-CN');
        }
        updateTime();
        setInterval(updateTime, 1000);

        // 刷新状态按钮点击事件
        document.getElementById('refreshStatusBtn').addEventListener('click', function() {
            alert('区块链状态已刷新（演示）');
        });

        // 保存配置按钮点击事件
        document.getElementById('saveConfigBtn').addEventListener('click', function() {
            alert('配置已保存（演示）');
        });

        // 测试连接按钮点击事件
        document.getElementById('testConnectionBtn').addEventListener('click', function() {
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>测试中...';

            setTimeout(() => {
                alert('连接成功!\n网络ID: 1337\n当前区块: 12345');
                this.disabled = false;
                this.innerHTML = '<i class="fas fa-plug me-2"></i>测试连接';
            }, 1500);
        });

        // 注册企业地址按钮点击事件
        document.getElementById('registerAddressBtn').addEventListener('click', function() {
            alert('企业地址已成功注册到区块链（演示）');
        });

        // 查看账户余额按钮点击事件
        document.getElementById('viewBalanceBtn').addEventListener('click', function() {
            alert('当前账户余额: 100 ETH（演示）');
        });
    </script>
</body>
</html>
