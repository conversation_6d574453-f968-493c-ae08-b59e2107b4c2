"""
创建测试数据
"""

import os
import sys
import random
from datetime import datetime, timedelta
import pymysql
from werkzeug.security import generate_password_hash

# 数据库连接信息
DB_HOST = '***********'
DB_PORT = 3306
DB_USER = 'wuhong'
DB_PASSWORD = 'D7mH8rZ7a7Z2kJa8'
DB_NAME = 'ces'

def get_db_connection():
    """获取数据库连接"""
    try:
        conn = pymysql.connect(
            host=DB_HOST,
            port=DB_PORT,
            user=DB_USER,
            password=DB_PASSWORD,
            database=DB_NAME,
            charset='utf8mb4'
        )
        return conn
    except Exception as e:
        print(f"数据库连接失败: {str(e)}")
        sys.exit(1)

def create_emission_data():
    """创建排放数据"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 清空现有数据
        cursor.execute("DELETE FROM emission_data")
        
        # 企业ID列表
        enterprise_ids = [2, 3, 4]  # enterprise1, enterprise2, enterprise3
        
        # 排放源类型
        emission_sources = ['电力消耗', '燃料燃烧', '工业过程', '废弃物处理', '运输']
        
        # 状态
        statuses = ['待提交', '已提交', '待核查', '已核查', '已拒绝']
        
        # 生成排放数据
        emission_data = []
        for enterprise_id in enterprise_ids:
            # 每个企业生成5-10条排放数据
            for _ in range(random.randint(5, 10)):
                source = random.choice(emission_sources)
                amount = random.randint(500, 5000)
                year = 2025
                month = random.randint(1, 12)
                day = random.randint(1, 28)
                submission_date = datetime(year, month, day)
                status = random.choice(statuses)
                
                emission_data.append((
                    enterprise_id,
                    source,
                    amount,
                    'tCO2e',
                    submission_date,
                    status,
                    f'这是{source}的排放数据，排放量为{amount} tCO2e'
                ))
        
        # 插入数据
        cursor.executemany(
            """
            INSERT INTO emission_data 
            (enterprise_id, source, amount, unit, submission_date, status, description)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            """,
            emission_data
        )
        
        conn.commit()
        print(f"成功创建 {len(emission_data)} 条排放数据")
    except Exception as e:
        conn.rollback()
        print(f"创建排放数据失败: {str(e)}")
    finally:
        cursor.close()
        conn.close()

def create_verification_data():
    """创建核查记录"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 清空现有数据
        cursor.execute("DELETE FROM verification")
        
        # 获取排放数据
        cursor.execute("SELECT id, enterprise_id, source, amount, unit FROM emission_data WHERE status IN ('已提交', '待核查', '已核查')")
        emission_data = cursor.fetchall()
        
        # 核查机构ID列表
        verifier_ids = [5, 6]  # verifier1, verifier2
        
        # 状态
        statuses = ['待核查', '已通过', '已拒绝']
        
        # 生成核查记录
        verification_data = []
        for emission in emission_data:
            emission_id = emission[0]
            enterprise_id = emission[1]
            source = emission[2]
            amount = emission[3]
            unit = emission[4]
            
            verifier_id = random.choice(verifier_ids)
            status = random.choice(statuses)
            
            year = 2025
            month = random.randint(1, 12)
            day = random.randint(1, 28)
            verification_date = datetime(year, month, day)
            
            comments = ""
            if status == '已通过':
                comments = f"核查通过，{source}的排放数据准确，计算方法正确"
            elif status == '已拒绝':
                comments = f"核查未通过，{source}的排放数据不完整，需要补充证明材料"
            
            verification_data.append((
                emission_id,
                enterprise_id,
                verifier_id,
                verification_date,
                status,
                comments
            ))
        
        # 插入数据
        cursor.executemany(
            """
            INSERT INTO verification 
            (emission_id, enterprise_id, verifier_id, verification_date, status, comments)
            VALUES (%s, %s, %s, %s, %s, %s)
            """,
            verification_data
        )
        
        conn.commit()
        print(f"成功创建 {len(verification_data)} 条核查记录")
    except Exception as e:
        conn.rollback()
        print(f"创建核查记录失败: {str(e)}")
    finally:
        cursor.close()
        conn.close()

def create_transaction_data():
    """创建交易记录"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 清空现有数据
        cursor.execute("DELETE FROM transaction")
        
        # 企业ID列表
        enterprise_ids = [2, 3, 4]  # enterprise1, enterprise2, enterprise3
        
        # 交易类型
        transaction_types = ['购买', '出售']
        
        # 状态
        statuses = ['待确认', '已确认', '已完成', '已取消']
        
        # 生成交易记录
        transaction_data = []
        for _ in range(20):
            # 随机选择买方和卖方
            buyer_id = random.choice(enterprise_ids)
            seller_id = random.choice([id for id in enterprise_ids if id != buyer_id])
            
            amount = random.randint(100, 1000)
            price = random.randint(30, 100)
            total = amount * price
            
            year = 2025
            month = random.randint(1, 12)
            day = random.randint(1, 28)
            transaction_date = datetime(year, month, day)
            
            status = random.choice(statuses)
            
            transaction_data.append((
                buyer_id,
                seller_id,
                amount,
                'tCO2e',
                price,
                total,
                transaction_date,
                status,
                f'这是一笔{amount} tCO2e的碳配额交易，单价{price}元/tCO2e，总价{total}元'
            ))
        
        # 插入数据
        cursor.executemany(
            """
            INSERT INTO transaction 
            (buyer_id, seller_id, amount, unit, price, total, transaction_date, status, description)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """,
            transaction_data
        )
        
        conn.commit()
        print(f"成功创建 {len(transaction_data)} 条交易记录")
    except Exception as e:
        conn.rollback()
        print(f"创建交易记录失败: {str(e)}")
    finally:
        cursor.close()
        conn.close()

def create_carbon_quota_data():
    """创建碳配额数据"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 清空现有数据
        cursor.execute("DELETE FROM carbon_quota")
        
        # 企业ID列表
        enterprise_ids = [2, 3, 4]  # enterprise1, enterprise2, enterprise3
        
        # 生成碳配额数据
        quota_data = []
        for enterprise_id in enterprise_ids:
            total = random.randint(5000, 10000)
            used = random.randint(1000, total - 1000)
            remaining = total - used
            
            quota_data.append((
                enterprise_id,
                total,
                used,
                remaining,
                2025,
                datetime.now(),
                f'2025年度碳配额，总量{total} tCO2e，已使用{used} tCO2e，剩余{remaining} tCO2e'
            ))
        
        # 插入数据
        cursor.executemany(
            """
            INSERT INTO carbon_quota 
            (enterprise_id, total, used, remaining, year, allocation_date, description)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            """,
            quota_data
        )
        
        conn.commit()
        print(f"成功创建 {len(quota_data)} 条碳配额数据")
    except Exception as e:
        conn.rollback()
        print(f"创建碳配额数据失败: {str(e)}")
    finally:
        cursor.close()
        conn.close()

def create_report_data():
    """创建报告数据"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 清空现有数据
        cursor.execute("DELETE FROM report")
        
        # 企业ID列表
        enterprise_ids = [2, 3, 4]  # enterprise1, enterprise2, enterprise3
        
        # 核查机构ID列表
        verifier_ids = [5, 6]  # verifier1, verifier2
        
        # 报告类型
        report_types = ['排放报告', '核查报告', '交易报告', '配额报告']
        
        # 生成报告数据
        report_data = []
        
        # 企业报告
        for enterprise_id in enterprise_ids:
            for report_type in report_types:
                year = 2025
                month = random.randint(1, 12)
                day = random.randint(1, 28)
                generation_date = datetime(year, month, day)
                
                report_data.append((
                    enterprise_id,
                    None,  # verifier_id
                    report_type,
                    f'{report_type}_{enterprise_id}_{generation_date.strftime("%Y%m%d")}.pdf',
                    generation_date,
                    f'这是企业ID为{enterprise_id}的{report_type}，生成日期为{generation_date.strftime("%Y-%m-%d")}'
                ))
        
        # 核查机构报告
        for verifier_id in verifier_ids:
            for enterprise_id in enterprise_ids:
                report_type = '核查报告'
                year = 2025
                month = random.randint(1, 12)
                day = random.randint(1, 28)
                generation_date = datetime(year, month, day)
                
                report_data.append((
                    enterprise_id,
                    verifier_id,
                    report_type,
                    f'{report_type}_{enterprise_id}_{verifier_id}_{generation_date.strftime("%Y%m%d")}.pdf',
                    generation_date,
                    f'这是核查机构ID为{verifier_id}对企业ID为{enterprise_id}的{report_type}，生成日期为{generation_date.strftime("%Y-%m-%d")}'
                ))
        
        # 插入数据
        cursor.executemany(
            """
            INSERT INTO report 
            (enterprise_id, verifier_id, report_type, file_path, generation_date, description)
            VALUES (%s, %s, %s, %s, %s, %s)
            """,
            report_data
        )
        
        conn.commit()
        print(f"成功创建 {len(report_data)} 条报告数据")
    except Exception as e:
        conn.rollback()
        print(f"创建报告数据失败: {str(e)}")
    finally:
        cursor.close()
        conn.close()

def create_penalty_data():
    """创建惩罚记录"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 清空现有数据
        cursor.execute("DELETE FROM penalty")
        
        # 企业ID列表
        enterprise_ids = [2, 3, 4]  # enterprise1, enterprise2, enterprise3
        
        # 惩罚类型
        penalty_types = ['超额排放', '数据造假', '未按时提交', '拒绝核查']
        
        # 状态
        statuses = ['待处理', '已处理', '已撤销']
        
        # 生成惩罚记录
        penalty_data = []
        for enterprise_id in enterprise_ids:
            # 每个企业生成1-3条惩罚记录
            for _ in range(random.randint(1, 3)):
                penalty_type = random.choice(penalty_types)
                amount = random.randint(5000, 50000)
                
                year = 2025
                month = random.randint(1, 12)
                day = random.randint(1, 28)
                issue_date = datetime(year, month, day)
                
                status = random.choice(statuses)
                
                penalty_data.append((
                    enterprise_id,
                    penalty_type,
                    amount,
                    issue_date,
                    status,
                    f'这是对企业ID为{enterprise_id}的{penalty_type}惩罚，金额为{amount}元'
                ))
        
        # 插入数据
        cursor.executemany(
            """
            INSERT INTO penalty 
            (enterprise_id, penalty_type, amount, issue_date, status, description)
            VALUES (%s, %s, %s, %s, %s, %s)
            """,
            penalty_data
        )
        
        conn.commit()
        print(f"成功创建 {len(penalty_data)} 条惩罚记录")
    except Exception as e:
        conn.rollback()
        print(f"创建惩罚记录失败: {str(e)}")
    finally:
        cursor.close()
        conn.close()

def create_activity_data():
    """创建活动记录"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 清空现有数据
        cursor.execute("DELETE FROM activity")
        
        # 用户ID列表
        user_ids = [1, 2, 3, 4, 5, 6]  # admin, enterprise1, enterprise2, enterprise3, verifier1, verifier2
        
        # 活动类型
        activity_types = ['登录', '提交排放数据', '核查排放数据', '交易配额', '生成报告', '查看报告', '配置系统']
        
        # 生成活动记录
        activity_data = []
        for _ in range(100):
            user_id = random.choice(user_ids)
            activity_type = random.choice(activity_types)
            
            # 根据用户角色选择合适的活动类型
            if user_id == 1:  # admin
                activity_type = random.choice(['登录', '配置系统', '查看报告'])
            elif user_id in [2, 3, 4]:  # enterprise
                activity_type = random.choice(['登录', '提交排放数据', '交易配额', '生成报告', '查看报告'])
            elif user_id in [5, 6]:  # verifier
                activity_type = random.choice(['登录', '核查排放数据', '生成报告', '查看报告'])
            
            year = 2025
            month = random.randint(1, 12)
            day = random.randint(1, 28)
            hour = random.randint(0, 23)
            minute = random.randint(0, 59)
            second = random.randint(0, 59)
            activity_time = datetime(year, month, day, hour, minute, second)
            
            activity_data.append((
                user_id,
                activity_type,
                activity_time,
                f'用户ID为{user_id}进行了{activity_type}活动'
            ))
        
        # 插入数据
        cursor.executemany(
            """
            INSERT INTO activity 
            (user_id, activity_type, activity_time, description)
            VALUES (%s, %s, %s, %s)
            """,
            activity_data
        )
        
        conn.commit()
        print(f"成功创建 {len(activity_data)} 条活动记录")
    except Exception as e:
        conn.rollback()
        print(f"创建活动记录失败: {str(e)}")
    finally:
        cursor.close()
        conn.close()

def main():
    """主函数"""
    print("开始创建测试数据...")
    
    # 创建排放数据
    create_emission_data()
    
    # 创建核查记录
    create_verification_data()
    
    # 创建交易记录
    create_transaction_data()
    
    # 创建碳配额数据
    create_carbon_quota_data()
    
    # 创建报告数据
    create_report_data()
    
    # 创建惩罚记录
    create_penalty_data()
    
    # 创建活动记录
    create_activity_data()
    
    print("测试数据创建完成！")

if __name__ == "__main__":
    main()
