{% extends "base.html" %}

{% block title %}排放数据 - 碳排放核查系统{% endblock %}

{% block content %}
<div class="row">
    <!-- 数据提交表单 -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">提交排放数据</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('emission_data') }}" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="emission_source" class="form-label">排放源</label>
                        <select class="form-select" id="emission_source" name="emission_source" required>
                            <option value="">请选择排放源</option>
                            <option value="fuel_combustion">燃料燃烧</option>
                            <option value="industrial_process">工业生产过程</option>
                            <option value="waste_disposal">废弃物处理</option>
                            <option value="other">其他</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="emission_amount" class="form-label">排放量（吨）</label>
                        <input type="number" class="form-control" id="emission_amount" name="emission_amount" step="0.01" required>
                    </div>
                    <div class="mb-3">
                        <label for="calculation_method" class="form-label">计算方法</label>
                        <select class="form-select" id="calculation_method" name="calculation_method" required>
                            <option value="">请选择计算方法</option>
                            <option value="emission_factor">排放因子法</option>
                            <option value="material_balance">物，美女ous_monitoring">连续监测法</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="proof_file" class="form-label">证明文件</label>
                        <input type="file" class="form-control" id="proof_file" name="proof_file" accept=".pdf,.doc,.docx,.xls,.xlsx">
                        <div class="form-text">支持PDF、Word、Excel格式，最大16MB</div>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">提交数据</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 数据列表 -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">排放数据记录</h5>
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-secondary btn-sm" data-filter="all">全部</button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" data-filter="pending">待核查</button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" data-filter="verified">已核查</button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>排放源</th>
                                <th>排放量</th>
                                <th>计算方法</th>
                                <th>提交时间</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for data in data %}
                            <tr data-status="{{ data.status }}">
                                <td>{{ data.emission_source }}</td>
                                <td>{{ data.emission_amount }} 吨</td>
                                <td>{{ data.calculation_method }}</td>
                                <td>{{ data.submission_time }}</td>
                                <td>
                                    <span class="badge bg-{{ data.status_color }}">
                                        {{ data.status }}
                                    </span>
                                </td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#detailModal{{ data.id }}">
                                        详情
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 详情模态框 -->
{% for data in data %}
<div class="modal fade" id="detailModal{{ data.id }}" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">排放数据详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <dl class="row">
                    <dt class="col-sm-4">排放源</dt>
                    <dd class="col-sm-8">{{ data.emission_source }}</dd>
                    
                    <dt class="col-sm-4">排放量</dt>
                    <dd class="col-sm-8">{{ data.emission_amount }} 吨</dd>
                    
                    <dt class="col-sm-4">计算方法</dt>
                    <dd class="col-sm-8">{{ data.calculation_method }}</dd>
                    
                    <dt class="col-sm-4">提交时间</dt>
                    <dd class="col-sm-8">{{ data.submission_time }}</dd>
                    
                    <dt class="col-sm-4">状态</dt>
                    <dd class="col-sm-8">
                        <span class="badge bg-{{ data.status_color }}">
                            {{ data.status }}
                        </span>
                    </dd>
                    
                    {% if data.proof_file_path %}
                    <dt class="col-sm-4">证明文件</dt>
                    <dd class="col-sm-8">
                        <a href="{{ url_for('static', filename=data.proof_file_path) }}" target="_blank" class="btn btn-sm btn-outline-primary">
                            查看文件
                        </a>
                    </dd>
                    {% endif %}
                    
                    {% if data.blockchain_hash %}
                    <dt class="col-sm-4">区块链哈希</dt>
                    <dd class="col-sm-8">
                        <small class="text-muted">{{ data.blockchain_hash }}</small>
                    </dd>
                    {% endif %}
                </dl>
            </div>
        </div>
    </div>
</div>
{% endfor %}
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 数据过滤
    document.querySelectorAll('[data-filter]').forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.dataset.filter;
            const rows = document.querySelectorAll('tbody tr');
            
            rows.forEach(row => {
                if (filter === 'all' || row.dataset.status === filter) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
            
            // 更新按钮状态
            document.querySelectorAll('[data-filter]').forEach(btn => {
                btn.classList.remove('active');
            });
            this.classList.add('active');
        });
    });
    
    // 表单验证
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const amount = document.getElementById('emission_amount').value;
        if (amount <= 0) {
            e.preventDefault();
            alert('排放量必须大于0');
            return;
        }
        
        const file = document.getElementById('proof_file').files[0];
        if (file && file.size > 16 * 1024 * 1024) {
            e.preventDefault();
            alert('文件大小不能超过16MB');
            return;
        }
    });
});
</script>
{% endblock %} 