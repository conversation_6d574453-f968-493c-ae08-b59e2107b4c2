.carbon-calculator {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.carbon-calculator h2 {
  text-align: center;
  margin-bottom: 30px;
  color: #2c3e50;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.calculator-container {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
}

.calculator-form {
  flex: 1;
  min-width: 300px;
}

.calculator-section {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.calculator-section h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #2c3e50;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 10px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #495057;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 16px;
}

.transportation-item,
.waste-item,
.industrial-item {
  background-color: #ffffff;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
  border: 1px solid #e9ecef;
  position: relative;
}

.remove-btn {
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  cursor: pointer;
  position: absolute;
  top: 10px;
  right: 10px;
}

.add-btn {
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 15px;
  cursor: pointer;
  margin-top: 10px;
  width: 100%;
}

.calculate-btn {
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 12px 20px;
  font-size: 18px;
  cursor: pointer;
  width: 100%;
  margin-top: 20px;
}

.calculate-btn:hover {
  background-color: #0069d9;
}

.calculate-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.calculation-result {
  flex: 1;
  min-width: 300px;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  align-self: flex-start;
}

.calculation-result h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #2c3e50;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 10px;
}

.result-total {
  background-color: #e9ecef;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-total strong {
  font-size: 24px;
  color: #28a745;
}

.result-breakdown h4 {
  margin-bottom: 15px;
  color: #495057;
}

.result-breakdown ul {
  list-style: none;
  padding: 0;
}

.result-breakdown li {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid #e9ecef;
}

.result-actions {
  margin-top: 20px;
  display: flex;
  gap: 10px;
}

.result-actions button {
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 15px;
  cursor: pointer;
  flex: 1;
}

.result-actions button:hover {
  background-color: #5a6268;
}

.calculation-history {
  width: 100%;
  margin-top: 30px;
}

.calculation-history h3 {
  margin-bottom: 20px;
  color: #2c3e50;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 10px;
}

.calculation-history table {
  width: 100%;
  border-collapse: collapse;
}

.calculation-history th,
.calculation-history td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.calculation-history th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #495057;
}

.calculation-history tr:hover {
  background-color: #f8f9fa;
}

.calculation-history button {
  background-color: #17a2b8;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  cursor: pointer;
}

.calculation-history button:hover {
  background-color: #138496;
}

@media (max-width: 768px) {
  .calculator-container {
    flex-direction: column;
  }
  
  .calculation-result {
    margin-top: 30px;
  }
}
