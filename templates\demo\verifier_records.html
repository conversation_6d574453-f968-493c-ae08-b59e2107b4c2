<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放管理系统 - 核查记录</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/unified-nav.css') }}">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background: linear-gradient(to right, #1B5E20, #388E3C);
            color: white;
            padding: 20px 0;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-name {
            margin-right: 15px;
        }
        .logout-btn {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 30px;
            cursor: pointer;
            text-decoration: none;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .logout-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        nav {
            background: linear-gradient(to right, #2E7D32, #43A047);
            padding: 10px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
        }
        .nav-item {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            margin-right: 5px;
            border-radius: 30px;
            transition: all 0.3s ease;
        }
        .nav-item:hover, .nav-item.active {
            background: linear-gradient(to right, #1B5E20, #2E7D32);
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        .card {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            transform: translateY(-5px);
        }
        .card-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        th, td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        th {
            background: linear-gradient(to right, #43A047, #66BB6A);
            color: white;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 14px;
            letter-spacing: 0.5px;
        }
        tr:last-child td {
            border-bottom: none;
        }
        tr:nth-child(even) {
            background-color: rgba(0,0,0,0.02);
        }
        tr:hover {
            background-color: rgba(76, 175, 80, 0.05);
        }
        .status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            color: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            display: inline-block;
        }
        .status-pending {
            background: linear-gradient(to right, #f39c12, #e67e22);
        }
        .status-verified {
            background: linear-gradient(to right, #2ecc71, #27ae60);
        }
        .status-rejected {
            background: linear-gradient(to right, #e74c3c, #c0392b);
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        .btn-primary {
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            color: white;
        }
        .btn-success {
            background: linear-gradient(to right, #2ecc71, #27ae60);
            color: white;
        }
        .btn-danger {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
        }
        .filters {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }
        .filter-group {
            display: flex;
            align-items: center;
        }
        .filter-group label {
            margin-right: 5px;
            font-weight: bold;
        }
        .filter-group select {
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 30px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .filter-group select:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 2px 10px rgba(76, 175, 80, 0.2);
        }
        .blockchain-info {
            font-size: 12px;
            color: #7f8c8d;
            margin-top: 10px;
            padding: 8px 12px;
            background: linear-gradient(to right, #f1f8e9, #e8f5e9);
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .blockchain-hash {
            font-family: monospace;
            background: linear-gradient(to right, #f1f8e9, #dcedc8);
            padding: 3px 8px;
            border-radius: 15px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            display: inline-block;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo">碳排放管理系统</div>
            <div class="user-info">
                <span class="user-name">国家碳排放核查中心</span>
                <a href="/login" class="logout-btn">退出登录</a>
            </div>
        </div>
    </header>

    <div class="demo-nav">
        <div class="nav-content">
            <a href="/verifier" class="nav-item"><i class="fas fa-tachometer-alt mr-2"></i>仪表板</a>
            <a href="/verifier_tasks" class="nav-item"><i class="fas fa-tasks mr-2"></i>待核查任务</a>
            <a href="/verifier_records" class="nav-item active"><i class="fas fa-clipboard-check mr-2"></i>核查记录</a>
            <a href="/verifier_enterprises" class="nav-item"><i class="fas fa-building mr-2"></i>企业管理</a>
            <a href="/verifier_reports" class="nav-item"><i class="fas fa-file-alt mr-2"></i>报告生成</a>
            <a href="/verifier_blockchain_config" class="nav-item"><i class="fas fa-link mr-2"></i>区块链配置</a>
        </div>
    </div>

    <div class="container">
        <h1>核查记录</h1>

        <div class="filters">
            <div class="filter-group">
                <label for="enterprise-filter">企业:</label>
                <select id="enterprise-filter">
                    <option value="all">全部</option>
                    <option value="1">北京碳排放科技有限公司</option>
                    <option value="2">上海绿色能源有限公司</option>
                    <option value="3">广州环保科技有限公司</option>
                </select>
            </div>
            <div class="filter-group">
                <label for="conclusion-filter">核查结论:</label>
                <select id="conclusion-filter">
                    <option value="all">全部</option>
                    <option value="approved">通过</option>
                    <option value="rejected">拒绝</option>
                </select>
            </div>
            <div class="filter-group">
                <label for="date-filter">核查日期:</label>
                <select id="date-filter">
                    <option value="all">全部</option>
                    <option value="week">本周</option>
                    <option value="month" selected>本月</option>
                    <option value="year">本年</option>
                </select>
            </div>
        </div>

        <div class="card">
            <div class="card-title">核查记录列表</div>
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>企业</th>
                        <th>排放源</th>
                        <th>排放量</th>
                        <th>核查结论</th>
                        <th>核查时间</th>
                        <th>区块链记录</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1001</td>
                        <td>北京碳排放科技有限公司</td>
                        <td>燃煤锅炉</td>
                        <td>450 吨CO2e</td>
                        <td><span class="status status-verified">通过</span></td>
                        <td>2023-05-08</td>
                        <td>
                            <div class="blockchain-info">
                                哈希: <span class="blockchain-hash">0xa1b2...</span>
                                <br>
                                区块: #15790123
                            </div>
                        </td>
                        <td><a href="/verification_detail?id=1001" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>1003</td>
                        <td>广州环保科技有限公司</td>
                        <td>工业生产过程</td>
                        <td>780 吨CO2e</td>
                        <td><span class="status status-rejected">拒绝</span></td>
                        <td>2023-05-09</td>
                        <td>
                            <div class="blockchain-info">
                                哈希: <span class="blockchain-hash">0xc3d4...</span>
                                <br>
                                区块: #15825678
                            </div>
                        </td>
                        <td><a href="/verification_detail?id=1003" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>1004</td>
                        <td>上海绿色能源有限公司</td>
                        <td>交通运输</td>
                        <td>250 吨CO2e</td>
                        <td><span class="status status-verified">通过</span></td>
                        <td>2023-05-10</td>
                        <td>
                            <div class="blockchain-info">
                                哈希: <span class="blockchain-hash">0xe5f6...</span>
                                <br>
                                区块: #15850456
                            </div>
                        </td>
                        <td><a href="/verification_detail?id=1004" class="btn btn-primary">查看</a></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="card">
            <div class="card-title">核查统计</div>
            <table>
                <thead>
                    <tr>
                        <th>企业</th>
                        <th>核查总数</th>
                        <th>通过数量</th>
                        <th>拒绝数量</th>
                        <th>通过率</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>北京碳排放科技有限公司</td>
                        <td>8</td>
                        <td>7</td>
                        <td>1</td>
                        <td>87.5%</td>
                    </tr>
                    <tr>
                        <td>上海绿色能源有限公司</td>
                        <td>5</td>
                        <td>4</td>
                        <td>1</td>
                        <td>80%</td>
                    </tr>
                    <tr>
                        <td>广州环保科技有限公司</td>
                        <td>6</td>
                        <td>4</td>
                        <td>2</td>
                        <td>66.7%</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 导航切换 - 不需要额外的事件监听器，因为我们使用的是链接
            // 根据当前URL设置活动导航项
            const currentPath = window.location.pathname;
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                if (item.getAttribute('href') === currentPath) {
                    navItems.forEach(i => i.classList.remove('active'));
                    item.classList.add('active');
                }
            });

            // 筛选功能
            const enterpriseFilter = document.getElementById('enterprise-filter');
            const conclusionFilter = document.getElementById('conclusion-filter');
            const dateFilter = document.getElementById('date-filter');

            // 这里只是演示，实际应用中应该根据筛选条件过滤数据
            enterpriseFilter.addEventListener('change', function() {
                console.log('企业筛选:', this.value);
            });

            conclusionFilter.addEventListener('change', function() {
                console.log('核查结论筛选:', this.value);
            });

            dateFilter.addEventListener('change', function() {
                console.log('核查日期筛选:', this.value);
            });

            // 添加核查记录统计图表
            const chartContainer = document.createElement('div');
            chartContainer.style.height = '300px';
            chartContainer.style.marginBottom = '30px';
            chartContainer.innerHTML = '<canvas id="verificationStatsChart"></canvas>';

            // 将图表插入到核查统计卡片中
            const statsCard = document.querySelector('.card:last-child');
            statsCard.appendChild(chartContainer);

            // 创建图表
            const ctx = document.getElementById('verificationStatsChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [
                        {
                            label: '已通过',
                            data: [5, 7, 8, 10, 12, 15],
                            borderColor: 'rgba(46, 204, 113, 1)',
                            backgroundColor: 'rgba(46, 204, 113, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        },
                        {
                            label: '已拒绝',
                            data: [2, 1, 3, 2, 1, 2],
                            borderColor: 'rgba(231, 76, 60, 1)',
                            backgroundColor: 'rgba(231, 76, 60, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        },
                        {
                            label: '待核查',
                            data: [3, 4, 2, 5, 3, 6],
                            borderColor: 'rgba(243, 156, 18, 1)',
                            backgroundColor: 'rgba(243, 156, 18, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '核查记录数量'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: '2023年'
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: '核查记录统计趋势',
                            font: {
                                size: 16
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.dataset.label || '';
                                    const value = context.raw || 0;
                                    return `${label}: ${value} 条`;
                                }
                            }
                        }
                    }
                }
            });

            // 添加企业分布饼图
            const pieContainer = document.createElement('div');
            pieContainer.style.height = '300px';
            pieContainer.style.marginBottom = '30px';
            pieContainer.innerHTML = '<canvas id="enterpriseDistributionChart"></canvas>';

            // 将饼图插入到折线图下方
            chartContainer.parentNode.insertBefore(pieContainer, chartContainer.nextSibling);

            // 创建饼图
            const pieCtx = document.getElementById('enterpriseDistributionChart').getContext('2d');
            new Chart(pieCtx, {
                type: 'pie',
                data: {
                    labels: ['北京碳排放科技有限公司', '上海绿色能源有限公司', '广州环保科技有限公司', '深圳新能源有限公司', '其他企业'],
                    datasets: [{
                        data: [12, 8, 6, 5, 9],
                        backgroundColor: [
                            'rgba(46, 204, 113, 0.8)',
                            'rgba(52, 152, 219, 0.8)',
                            'rgba(155, 89, 182, 0.8)',
                            'rgba(241, 196, 15, 0.8)',
                            'rgba(230, 126, 34, 0.8)'
                        ],
                        borderColor: [
                            'rgba(46, 204, 113, 1)',
                            'rgba(52, 152, 219, 1)',
                            'rgba(155, 89, 182, 1)',
                            'rgba(241, 196, 15, 1)',
                            'rgba(230, 126, 34, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                        },
                        title: {
                            display: true,
                            text: '核查记录企业分布',
                            font: {
                                size: 16
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.chart.data.datasets[0].data.reduce(
                                        (sum, value) => sum + value, 0
                                    );
                                    const percentage = Math.round((value * 100) / total);
                                    return `${label}: ${value} 条 (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>
