<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放管理系统 - 交易管理</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background: linear-gradient(to right, #1B5E20, #388E3C);
            color: white;
            padding: 20px 0;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-name {
            margin-right: 15px;
        }
        .logout-btn {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 30px;
            cursor: pointer;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .logout-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        nav {
            background: linear-gradient(to right, #2E7D32, #43A047);
            padding: 10px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
        }
        .nav-item {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            margin-right: 5px;
            border-radius: 30px;
            transition: all 0.3s ease;
        }
        .nav-item:hover, .nav-item.active {
            background: linear-gradient(to right, #1B5E20, #2E7D32);
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        .card {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            transform: translateY(-5px);
        }
        .card-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .dashboard {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
        }
        .stat-card {
            text-align: center;
            padding: 30px 20px;
        }
        .stat-value {
            font-size: 36px;
            font-weight: bold;
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin: 15px 0;
            text-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .stat-label {
            color: #7f8c8d;
        }
        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        th, td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        th {
            background: linear-gradient(to right, #43A047, #66BB6A);
            color: white;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 14px;
            letter-spacing: 0.5px;
        }
        tr:last-child td {
            border-bottom: none;
        }
        tr:nth-child(even) {
            background-color: rgba(0,0,0,0.02);
        }
        tr:hover {
            background-color: rgba(76, 175, 80, 0.05);
        }
        .status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            display: inline-block;
        }
        .status-completed {
            background: linear-gradient(to right, #2ecc71, #27ae60);
            color: white;
        }
        .status-pending {
            background: linear-gradient(to right, #f39c12, #e67e22);
            color: white;
        }
        .status-cancelled {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        .btn-primary {
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            color: white;
        }
        .chart-container {
            height: 300px;
            margin-top: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo">碳排放管理系统</div>
            <div class="user-info">
                <span class="user-name">管理员</span>
                <a href="/login" class="logout-btn">退出登录</a>
            </div>
        </div>
    </header>

    <nav>
        <div class="nav-content">
            <a href="/admin" class="nav-item">系统概览</a>
            <a href="/admin_users" class="nav-item">用户管理</a>
            <a href="/admin_quotas" class="nav-item">配额管理</a>
            <a href="/admin_verifications" class="nav-item">核查管理</a>
            <a href="/admin_transactions" class="nav-item active">交易管理</a>
            <a href="/admin_settings" class="nav-item">系统配置</a>
            <a href="/admin_logs" class="nav-item">日志查看</a>
        </div>
    </nav>

    <div class="container">
        <h1>交易管理</h1>

        <div class="dashboard">
            <div class="card stat-card">
                <div class="stat-label">交易总量</div>
                <div class="stat-value">12,500</div>
                <div class="stat-label">吨CO2e</div>
            </div>
            <div class="card stat-card">
                <div class="stat-label">交易总额</div>
                <div class="stat-value">1,250,000</div>
                <div class="stat-label">元</div>
            </div>
            <div class="card stat-card">
                <div class="stat-label">平均价格</div>
                <div class="stat-value">100</div>
                <div class="stat-label">元/吨</div>
            </div>
        </div>

        <div class="card">
            <div class="card-title">交易趋势</div>
            <div class="chart-container">
                <canvas id="transactionChart"></canvas>
            </div>
        </div>

        <div class="card">
            <div class="card-title">最近交易记录</div>
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>卖方</th>
                        <th>买方</th>
                        <th>数量</th>
                        <th>价格</th>
                        <th>总价</th>
                        <th>交易时间</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>2001</td>
                        <td>北京碳排放科技有限公司</td>
                        <td>上海绿色能源有限公司</td>
                        <td>500 吨</td>
                        <td>95 元/吨</td>
                        <td>47,500 元</td>
                        <td>2023-04-15</td>
                        <td><span class="status status-completed">已完成</span></td>
                        <td><a href="/transaction_detail?id=2001" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>2002</td>
                        <td>广州环保科技有限公司</td>
                        <td>深圳新能源有限公司</td>
                        <td>300 吨</td>
                        <td>100 元/吨</td>
                        <td>30,000 元</td>
                        <td>2023-04-20</td>
                        <td><span class="status status-pending">待确认</span></td>
                        <td><a href="/transaction_detail?id=2002" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>2003</td>
                        <td>重庆工业集团</td>
                        <td>北京碳排放科技有限公司</td>
                        <td>800 吨</td>
                        <td>90 元/吨</td>
                        <td>72,000 元</td>
                        <td>2023-04-25</td>
                        <td><span class="status status-completed">已完成</span></td>
                        <td><a href="/transaction_detail?id=2003" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>2004</td>
                        <td>上海绿色能源有限公司</td>
                        <td>广州环保科技有限公司</td>
                        <td>200 吨</td>
                        <td>105 元/吨</td>
                        <td>21,000 元</td>
                        <td>2023-05-01</td>
                        <td><span class="status status-cancelled">已取消</span></td>
                        <td><a href="/transaction_detail?id=2004" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>2005</td>
                        <td>深圳新能源有限公司</td>
                        <td>重庆工业集团</td>
                        <td>450 吨</td>
                        <td>98 元/吨</td>
                        <td>44,100 元</td>
                        <td>2023-05-05</td>
                        <td><span class="status status-pending">待确认</span></td>
                        <td><a href="/transaction_detail?id=2005" class="btn btn-primary">查看</a></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 导航切换
            const currentPath = window.location.pathname;
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                if (item.getAttribute('href') === currentPath) {
                    navItems.forEach(i => i.classList.remove('active'));
                    item.classList.add('active');
                }
            });

            // 交易趋势图表
            const ctx = document.getElementById('transactionChart').getContext('2d');
            const transactionChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [
                        {
                            label: '交易量 (吨)',
                            data: [1200, 1500, 2000, 2200, 2800, 3000],
                            borderColor: 'rgba(46, 204, 113, 1)',
                            backgroundColor: 'rgba(46, 204, 113, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        },
                        {
                            label: '平均价格 (元/吨)',
                            data: [85, 90, 95, 98, 100, 105],
                            borderColor: 'rgba(52, 152, 219, 1)',
                            backgroundColor: 'rgba(52, 152, 219, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4,
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: false,
                            title: {
                                display: true,
                                text: '交易量 (吨)'
                            }
                        },
                        y1: {
                            position: 'right',
                            beginAtZero: false,
                            title: {
                                display: true,
                                text: '价格 (元/吨)'
                            },
                            grid: {
                                drawOnChartArea: false
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: '2023年交易趋势'
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>
