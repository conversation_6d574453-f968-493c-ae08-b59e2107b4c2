import React, { useState, useEffect } from 'react';
import axios from 'axios';
import '../styles/NotificationManagement.css';

function NotificationManagement() {
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    type: '',
    priority: 'normal',
    targetUsers: []
  });

  useEffect(() => {
    fetchNotifications();
  }, []);

  const fetchNotifications = async () => {
    try {
      const response = await axios.get('/api/notifications', {
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      setNotifications(response.data);
      setLoading(false);
    } catch (err) {
      setError('获取通知列表失败');
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await axios.post('/api/notifications', formData, {
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      fetchNotifications();
      setFormData({
        title: '',
        content: '',
        type: '',
        priority: 'normal',
        targetUsers: []
      });
    } catch (err) {
      setError('创建通知失败');
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleTargetUsersChange = (e) => {
    const options = e.target.options;
    const selectedUsers = [];
    for (let i = 0; i < options.length; i++) {
      if (options[i].selected) {
        selectedUsers.push(options[i].value);
      }
    }
    setFormData(prev => ({
      ...prev,
      targetUsers: selectedUsers
    }));
  };

  const handleStatusChange = async (notificationId, newStatus) => {
    try {
      await axios.patch(`/api/notifications/${notificationId}`, 
        { status: newStatus },
        { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } }
      );
      fetchNotifications();
    } catch (err) {
      setError('更新通知状态失败');
    }
  };

  const handleDelete = async (notificationId) => {
    if (window.confirm('确定要删除这条通知吗？')) {
      try {
        await axios.delete(`/api/notifications/${notificationId}`, {
          headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
        });
        fetchNotifications();
      } catch (err) {
        setError('删除通知失败');
      }
    }
  };

  if (loading) return <div className="loading">加载中...</div>;

  return (
    <div className="notification-management">
      <h2>通知管理</h2>
      {error && <div className="alert alert-danger">{error}</div>}
      
      <div className="notification-form">
        <h3>创建新通知</h3>
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="title">通知标题</label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              required
              className="form-control"
            />
          </div>
          <div className="form-group">
            <label htmlFor="type">通知类型</label>
            <select
              id="type"
              name="type"
              value={formData.type}
              onChange={handleChange}
              required
              className="form-control"
            >
              <option value="">请选择类型</option>
              <option value="system">系统通知</option>
              <option value="alert">预警通知</option>
              <option value="report">报告通知</option>
              <option value="other">其他通知</option>
            </select>
          </div>
          <div className="form-group">
            <label htmlFor="priority">优先级</label>
            <select
              id="priority"
              name="priority"
              value={formData.priority}
              onChange={handleChange}
              required
              className="form-control"
            >
              <option value="low">低</option>
              <option value="normal">中</option>
              <option value="high">高</option>
              <option value="urgent">紧急</option>
            </select>
          </div>
          <div className="form-group">
            <label htmlFor="content">通知内容</label>
            <textarea
              id="content"
              name="content"
              value={formData.content}
              onChange={handleChange}
              required
              className="form-control"
              rows="4"
            />
          </div>
          <div className="form-group">
            <label htmlFor="targetUsers">目标用户</label>
            <select
              id="targetUsers"
              name="targetUsers"
              multiple
              value={formData.targetUsers}
              onChange={handleTargetUsersChange}
              className="form-control"
            >
              <option value="all">所有用户</option>
              <option value="admin">管理员</option>
              <option value="operator">操作员</option>
              <option value="viewer">查看者</option>
            </select>
            <small className="form-text">按住Ctrl键可以选择多个用户</small>
          </div>
          <button type="submit" className="btn btn-primary">
            发送通知
          </button>
        </form>
      </div>

      <div className="notification-list">
        <h3>通知列表</h3>
        <div className="table-responsive">
          <table className="table">
            <thead>
              <tr>
                <th>标题</th>
                <th>类型</th>
                <th>优先级</th>
                <th>目标用户</th>
                <th>状态</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              {notifications.map(notification => (
                <tr key={notification.id}>
                  <td>{notification.title}</td>
                  <td>
                    <span className={`type ${notification.type}`}>
                      {notification.type === 'system' ? '系统通知' :
                       notification.type === 'alert' ? '预警通知' :
                       notification.type === 'report' ? '报告通知' : '其他通知'}
                    </span>
                  </td>
                  <td>
                    <span className={`priority ${notification.priority}`}>
                      {notification.priority === 'low' ? '低' :
                       notification.priority === 'normal' ? '中' :
                       notification.priority === 'high' ? '高' : '紧急'}
                    </span>
                  </td>
                  <td>
                    {notification.targetUsers.map(user => (
                      <span key={user} className="target-user">
                        {user === 'all' ? '所有用户' :
                         user === 'admin' ? '管理员' :
                         user === 'operator' ? '操作员' : '查看者'}
                      </span>
                    ))}
                  </td>
                  <td>
                    <span className={`status ${notification.status}`}>
                      {notification.status === 'draft' ? '草稿' :
                       notification.status === 'sent' ? '已发送' :
                       notification.status === 'read' ? '已读' : '已过期'}
                    </span>
                  </td>
                  <td>
                    <div className="action-buttons">
                      <select
                        value={notification.status}
                        onChange={(e) => handleStatusChange(notification.id, e.target.value)}
                        className="status-select"
                      >
                        <option value="draft">草稿</option>
                        <option value="sent">已发送</option>
                        <option value="read">已读</option>
                        <option value="expired">已过期</option>
                      </select>
                      <button
                        onClick={() => handleDelete(notification.id)}
                        className="btn btn-danger"
                      >
                        删除
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default NotificationManagement; 