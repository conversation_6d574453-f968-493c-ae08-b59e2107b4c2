import React, { useState, useEffect } from 'react';
import api, { endpoints } from '../../config/api';
import '../../styles/admin/Dashboard.css';

const AdminDashboard = () => {
  const [stats, setStats] = useState({
    totalCompanies: 0,
    totalReports: 0,
    totalVerifications: 0,
    totalEmissions: 0
  });
  const [recentReports, setRecentReports] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const [statsResponse, reportsResponse] = await Promise.all([
        api.get(endpoints.admin.stats),
        api.get(endpoints.admin.recentReports)
      ]);
      setStats(statsResponse.data);
      setRecentReports(reportsResponse.data);
    } catch (err) {
      setError('获取数据失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <div className="loading">加载中...</div>;
  }

  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }

  return (
    <div className="admin-dashboard">
      <h1 className="page-title">管理员仪表板</h1>
      
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon companies-icon">
            <i className="fas fa-building"></i>
          </div>
          <div className="stat-content">
            <h3 className="stat-title">企业总数</h3>
            <p className="stat-value">{stats.totalCompanies}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon reports-icon">
            <i className="fas fa-file-alt"></i>
          </div>
          <div className="stat-content">
            <h3 className="stat-title">报告总数</h3>
            <p className="stat-value">{stats.totalReports}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon verifications-icon">
            <i className="fas fa-check-circle"></i>
          </div>
          <div className="stat-content">
            <h3 className="stat-title">验证总数</h3>
            <p className="stat-value">{stats.totalVerifications}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon emissions-icon">
            <i className="fas fa-cloud"></i>
          </div>
          <div className="stat-content">
            <h3 className="stat-title">排放总量</h3>
            <p className="stat-value">{stats.totalEmissions} 吨</p>
          </div>
        </div>
      </div>

      <div className="dashboard-grid">
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">最近报告</h2>
          </div>
          <div className="table-container">
            <table className="table">
              <thead>
                <tr>
                  <th>企业名称</th>
                  <th>报告类型</th>
                  <th>提交时间</th>
                  <th>状态</th>
                  <th>排放量</th>
                </tr>
              </thead>
              <tbody>
                {recentReports.map(report => (
                  <tr key={report.id}>
                    <td>{report.companyName}</td>
                    <td>{report.reportType}</td>
                    <td>{new Date(report.submittedAt).toLocaleDateString()}</td>
                    <td>
                      <span className={`status-badge ${report.status}`}>
                        {report.status === 'pending' ? '待验证' : '已验证'}
                      </span>
                    </td>
                    <td>{report.emissions} 吨</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        <div className="card">
          <div className="card-header">
            <h2 className="card-title">系统概览</h2>
          </div>
          <div className="system-overview">
            <div className="overview-item">
              <span className="overview-label">活跃用户</span>
              <span className="overview-value">{stats.activeUsers}</span>
            </div>
            <div className="overview-item">
              <span className="overview-label">今日报告</span>
              <span className="overview-value">{stats.todayReports}</span>
            </div>
            <div className="overview-item">
              <span className="overview-label">验证率</span>
              <span className="overview-value">{stats.verificationRate}%</span>
            </div>
            <div className="overview-item">
              <span className="overview-label">平均响应时间</span>
              <span className="overview-value">{stats.averageResponseTime}小时</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard; 