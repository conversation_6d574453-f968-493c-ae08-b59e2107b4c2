import React, { useState, useEffect } from 'react';
import axios from 'axios';
import '../styles/CarbonCalculator.css';

function CarbonCalculator() {
  const [formData, setFormData] = useState({
    electricity: '',
    electricity_region: 'default',
    coal: '',
    natural_gas: '',
    gasoline: '',
    diesel: '',
    lpg: '',
    fuel_oil: '',
    transportation: [
      { vehicle_type: 'car_gasoline', distance: '', fuel_efficiency: '' }
    ],
    waste: [
      { waste_type: 'landfill', weight: '' }
    ],
    industrial: []
  });
  
  const [emissionFactors, setEmissionFactors] = useState(null);
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [calculations, setCalculations] = useState([]);

  useEffect(() => {
    fetchEmissionFactors();
    fetchCalculations();
  }, []);

  const fetchEmissionFactors = async () => {
    try {
      const response = await axios.get('/api/calculator/emission-factors');
      setEmissionFactors(response.data);
    } catch (err) {
      setError('获取排放因子失败');
    }
  };

  const fetchCalculations = async () => {
    try {
      const response = await axios.get('/api/calculator/calculations');
      setCalculations(response.data);
    } catch (err) {
      setError('获取计算历史失败');
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleTransportationChange = (index, field, value) => {
    const updatedItems = [...formData.transportation];
    updatedItems[index][field] = value;
    setFormData(prev => ({
      ...prev,
      transportation: updatedItems
    }));
  };

  const addTransportationItem = () => {
    setFormData(prev => ({
      ...prev,
      transportation: [
        ...prev.transportation,
        { vehicle_type: 'car_gasoline', distance: '', fuel_efficiency: '' }
      ]
    }));
  };

  const removeTransportationItem = (index) => {
    const updatedItems = formData.transportation.filter((_, i) => i !== index);
    setFormData(prev => ({
      ...prev,
      transportation: updatedItems
    }));
  };

  const handleWasteChange = (index, field, value) => {
    const updatedItems = [...formData.waste];
    updatedItems[index][field] = value;
    setFormData(prev => ({
      ...prev,
      waste: updatedItems
    }));
  };

  const addWasteItem = () => {
    setFormData(prev => ({
      ...prev,
      waste: [
        ...prev.waste,
        { waste_type: 'landfill', weight: '' }
      ]
    }));
  };

  const removeWasteItem = (index) => {
    const updatedItems = formData.waste.filter((_, i) => i !== index);
    setFormData(prev => ({
      ...prev,
      waste: updatedItems
    }));
  };

  const handleIndustrialChange = (index, field, value) => {
    const updatedItems = [...formData.industrial];
    updatedItems[index][field] = value;
    setFormData(prev => ({
      ...prev,
      industrial: updatedItems
    }));
  };

  const addIndustrialItem = () => {
    setFormData(prev => ({
      ...prev,
      industrial: [
        ...prev.industrial,
        { process_type: 'cement', production: '' }
      ]
    }));
  };

  const removeIndustrialItem = (index) => {
    const updatedItems = formData.industrial.filter((_, i) => i !== index);
    setFormData(prev => ({
      ...prev,
      industrial: updatedItems
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    
    // 准备提交数据
    const calculationData = { ...formData };
    
    // 转换字符串数值为数字
    for (const key in calculationData) {
      if (typeof calculationData[key] === 'string' && calculationData[key] && !isNaN(calculationData[key])) {
        calculationData[key] = parseFloat(calculationData[key]);
      }
    }
    
    // 处理交通数据
    calculationData.transportation = calculationData.transportation.map(item => ({
      vehicle_type: item.vehicle_type,
      distance: parseFloat(item.distance) || 0,
      fuel_efficiency: item.fuel_efficiency ? parseFloat(item.fuel_efficiency) : undefined
    }));
    
    // 处理废弃物数据
    calculationData.waste = calculationData.waste.map(item => ({
      waste_type: item.waste_type,
      weight: parseFloat(item.weight) || 0
    }));
    
    // 处理工业过程数据
    calculationData.industrial = calculationData.industrial.map(item => ({
      process_type: item.process_type,
      production: parseFloat(item.production) || 0
    }));

    try {
      const response = await axios.post('/api/calculator/calculate', calculationData);
      setResult(response.data);
      fetchCalculations();  // 刷新计算历史
    } catch (err) {
      setError('计算失败: ' + (err.response?.data?.error || err.message));
    } finally {
      setLoading(false);
    }
  };

  const saveCalculation = async (calculationResult) => {
    // 这里可以实现保存计算结果的逻辑
    alert('计算结果已保存');
  };

  return (
    <div className="carbon-calculator">
      <h2>碳足迹计算器</h2>
      
      {error && <div className="error-message">{error}</div>}
      
      <div className="calculator-container">
        <div className="calculator-form">
          <form onSubmit={handleSubmit}>
            <div className="calculator-section">
              <h3>能源消耗</h3>
              
              <div className="form-group">
                <label>电力消耗 (kWh)</label>
                <input
                  type="number"
                  name="electricity"
                  value={formData.electricity}
                  onChange={handleChange}
                  min="0"
                />
              </div>
              
              <div className="form-group">
                <label>电网区域</label>
                <select
                  name="electricity_region"
                  value={formData.electricity_region}
                  onChange={handleChange}
                >
                  <option value="default">全国平均</option>
                  <option value="north">华北电网</option>
                  <option value="northeast">东北电网</option>
                  <option value="east">华东电网</option>
                  <option value="central">华中电网</option>
                  <option value="northwest">西北电网</option>
                  <option value="south">南方电网</option>
                </select>
              </div>
              
              <div className="form-group">
                <label>煤炭 (kg)</label>
                <input
                  type="number"
                  name="coal"
                  value={formData.coal}
                  onChange={handleChange}
                  min="0"
                />
              </div>
              
              <div className="form-group">
                <label>天然气 (m³)</label>
                <input
                  type="number"
                  name="natural_gas"
                  value={formData.natural_gas}
                  onChange={handleChange}
                  min="0"
                />
              </div>
              
              <div className="form-group">
                <label>汽油 (L)</label>
                <input
                  type="number"
                  name="gasoline"
                  value={formData.gasoline}
                  onChange={handleChange}
                  min="0"
                />
              </div>
              
              <div className="form-group">
                <label>柴油 (L)</label>
                <input
                  type="number"
                  name="diesel"
                  value={formData.diesel}
                  onChange={handleChange}
                  min="0"
                />
              </div>
            </div>
            
            <div className="calculator-section">
              <h3>交通</h3>
              
              {formData.transportation.map((item, index) => (
                <div key={index} className="transportation-item">
                  <div className="form-group">
                    <label>交通工具类型</label>
                    <select
                      value={item.vehicle_type}
                      onChange={(e) => handleTransportationChange(index, 'vehicle_type', e.target.value)}
                    >
                      <option value="car_gasoline">汽油车</option>
                      <option value="car_diesel">柴油车</option>
                      <option value="car_electric">电动车</option>
                      <option value="bus">公交车</option>
                      <option value="train">火车</option>
                      <option value="plane">飞机</option>
                      <option value="ship">船舶</option>
                    </select>
                  </div>
                  
                  <div className="form-group">
                    <label>距离 (km)</label>
                    <input
                      type="number"
                      value={item.distance}
                      onChange={(e) => handleTransportationChange(index, 'distance', e.target.value)}
                      min="0"
                    />
                  </div>
                  
                  {(item.vehicle_type === 'car_gasoline' || item.vehicle_type === 'car_diesel') && (
                    <div className="form-group">
                      <label>燃油效率 (L/100km)</label>
                      <input
                        type="number"
                        value={item.fuel_efficiency}
                        onChange={(e) => handleTransportationChange(index, 'fuel_efficiency', e.target.value)}
                        min="0"
                      />
                    </div>
                  )}
                  
                  <button 
                    type="button" 
                    className="remove-btn"
                    onClick={() => removeTransportationItem(index)}
                  >
                    删除
                  </button>
                </div>
              ))}
              
              <button 
                type="button" 
                className="add-btn"
                onClick={addTransportationItem}
              >
                添加交通项
              </button>
            </div>
            
            <div className="calculator-section">
              <h3>废弃物</h3>
              
              {formData.waste.map((item, index) => (
                <div key={index} className="waste-item">
                  <div className="form-group">
                    <label>处理方式</label>
                    <select
                      value={item.waste_type}
                      onChange={(e) => handleWasteChange(index, 'waste_type', e.target.value)}
                    >
                      <option value="landfill">填埋</option>
                      <option value="incineration">焚烧</option>
                      <option value="recycling">回收</option>
                      <option value="composting">堆肥</option>
                    </select>
                  </div>
                  
                  <div className="form-group">
                    <label>重量 (kg)</label>
                    <input
                      type="number"
                      value={item.weight}
                      onChange={(e) => handleWasteChange(index, 'weight', e.target.value)}
                      min="0"
                    />
                  </div>
                  
                  <button 
                    type="button" 
                    className="remove-btn"
                    onClick={() => removeWasteItem(index)}
                  >
                    删除
                  </button>
                </div>
              ))}
              
              <button 
                type="button" 
                className="add-btn"
                onClick={addWasteItem}
              >
                添加废弃物项
              </button>
            </div>
            
            <button type="submit" className="calculate-btn" disabled={loading}>
              {loading ? '计算中...' : '计算碳足迹'}
            </button>
          </form>
        </div>
        
        {result && (
          <div className="calculation-result">
            <h3>计算结果</h3>
            
            <div className="result-total">
              <span>总碳排放量:</span>
              <strong>{result.total_emission.toFixed(2)} {result.unit}</strong>
            </div>
            
            <div className="result-breakdown">
              <h4>排放明细:</h4>
              <ul>
                <li>
                  <span>电力:</span>
                  <span>{result.breakdown.electricity.toFixed(2)} {result.unit}</span>
                </li>
                <li>
                  <span>燃料:</span>
                  <span>{result.breakdown.fuel.toFixed(2)} {result.unit}</span>
                </li>
                <li>
                  <span>交通:</span>
                  <span>{result.breakdown.transportation.toFixed(2)} {result.unit}</span>
                </li>
                <li>
                  <span>废弃物:</span>
                  <span>{result.breakdown.waste.toFixed(2)} {result.unit}</span>
                </li>
                <li>
                  <span>工业过程:</span>
                  <span>{result.breakdown.industrial.toFixed(2)} {result.unit}</span>
                </li>
              </ul>
            </div>
            
            <div className="result-actions">
              <button onClick={() => window.print()}>打印结果</button>
              <button onClick={() => saveCalculation(result)}>保存结果</button>
            </div>
          </div>
        )}
      </div>
      
      <div className="calculation-history">
        <h3>计算历史</h3>
        
        {calculations.length > 0 ? (
          <table>
            <thead>
              <tr>
                <th>ID</th>
                <th>计算时间</th>
                <th>总排放量</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              {calculations.map(calc => (
                <tr key={calc.id}>
                  <td>{calc.id}</td>
                  <td>{new Date(calc.calculation_time).toLocaleString()}</td>
                  <td>{calc.result_total.toFixed(2)} kgCO2e</td>
                  <td>
                    <button onClick={() => window.location.href = `/api/calculator/calculations/${calc.id}`}>
                      查看详情
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <p>暂无计算历史</p>
        )}
      </div>
    </div>
  );
}

export default CarbonCalculator;
