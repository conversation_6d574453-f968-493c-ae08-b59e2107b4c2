<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放管理系统 - 登录</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }
        .login-container {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 20px;
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
            padding: 40px;
            width: 400px;
            max-width: 90%;
            border: 1px solid rgba(0,0,0,0.05);
        }
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-title {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .login-subtitle {
            color: #7f8c8d;
            font-size: 14px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #2c3e50;
        }
        input, select {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 30px;
            font-size: 14px;
            box-sizing: border-box;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        input:focus, select:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 2px 10px rgba(76, 175, 80, 0.2);
        }
        .login-btn {
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            color: white;
            border: none;
            padding: 14px;
            border-radius: 30px;
            cursor: pointer;
            font-weight: bold;
            width: 100%;
            font-size: 16px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }
        .login-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 20px rgba(0, 0, 0, 0.3);
            background: linear-gradient(to right, #2E7D32, #4CAF50);
        }
        .demo-accounts {
            margin-top: 30px;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }
        .demo-accounts h3 {
            font-size: 16px;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .account-list {
            list-style-type: none;
            padding: 0;
        }
        .account-list li {
            margin-bottom: 10px;
            padding: 15px;
            background: linear-gradient(to right, #f8f9fa, #f1f3f5);
            border-radius: 15px;
            cursor: pointer;
            box-shadow: 0 3px 10px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,0.03);
        }
        .account-list li:hover {
            background: linear-gradient(to right, #e9ecef, #dee2e6);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .account-role {
            font-weight: bold;
            color: #2c3e50;
        }
        .account-credentials {
            color: #7f8c8d;
            font-size: 14px;
        }
        .register-link {
            text-align: center;
            margin-top: 20px;
            font-size: 14px;
        }
        .register-link a {
            color: #4CAF50;
            text-decoration: none;
        }
        .register-link a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="login-title">碳排放管理系统</div>
            <div class="login-subtitle">请登录以继续</div>
        </div>

        <form id="login-form">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" placeholder="请输入用户名" required>
            </div>

            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" placeholder="请输入密码" required>
            </div>

            <div class="form-group">
                <label for="role">选择登录角色</label>
                <select id="role" name="role">
                    <option value="enterprise">企业用户</option>
                    <option value="verifier">核查机构</option>
                    <option value="admin">管理员</option>
                </select>
            </div>

            <button type="submit" class="login-btn">登录</button>
        </form>

        <div class="demo-accounts">
            <h3>演示账号</h3>
            <ul class="account-list">
                <li class="demo-account" data-username="admin" data-password="admin123" data-role="admin">
                    <div class="account-role">管理员</div>
                    <div class="account-credentials">用户名: admin / 密码: admin123</div>
                </li>
                <li class="demo-account" data-username="enterprise1" data-password="password123" data-role="enterprise">
                    <div class="account-role">企业用户</div>
                    <div class="account-credentials">用户名: enterprise1 / 密码: password123</div>
                </li>
                <li class="demo-account" data-username="verifier1" data-password="password123" data-role="verifier">
                    <div class="account-role">核查机构</div>
                    <div class="account-credentials">用户名: verifier1 / 密码: password123</div>
                </li>
            </ul>
        </div>

        <div class="register-link">
            没有账号？<a href="/register">立即注册</a>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 演示账号点击事件
            const demoAccounts = document.querySelectorAll('.demo-account');
            demoAccounts.forEach(account => {
                account.addEventListener('click', function() {
                    const username = this.getAttribute('data-username');
                    const password = this.getAttribute('data-password');
                    const role = this.getAttribute('data-role');

                    document.getElementById('username').value = username;
                    document.getElementById('password').value = password;
                    document.getElementById('role').value = role;
                });
            });

            // 登录表单提交事件
            const loginForm = document.getElementById('login-form');
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;

                // 获取选择的角色
                const role = document.getElementById('role').value;

                // 简单的演示登录逻辑
                if (username === 'admin' && password === 'admin123') {
                    window.location.href = '/admin';
                } else if (username === 'enterprise1' && password === 'password123') {
                    if (role === 'enterprise') {
                        window.location.href = '/dashboard';
                    } else {
                        alert('请选择正确的用户角色');
                    }
                } else if (username === 'verifier1' && password === 'password123') {
                    if (role === 'verifier') {
                        window.location.href = '/verifier';
                    } else {
                        alert('请选择正确的用户角色');
                    }
                } else {
                    alert('用户名或密码错误');
                }
            });
        });
    </script>
</body>
</html>
