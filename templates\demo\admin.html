{% extends 'base.html' %}

{% block title %}碳排放核查系统 - 管理员演示{% endblock %}

{% block head %}
<style>
    body {
        font-family: 'Arial', sans-serif;
        margin: 0;
        padding: 0;
        background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
        color: #333;
        min-height: 100vh;
    }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background: linear-gradient(to right, #1B5E20, #388E3C);
            color: white;
            padding: 20px 0;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-name {
            margin-right: 15px;
        }
        .logout-btn {
            background-color: #e74c3c;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        nav {
            background: linear-gradient(to right, #2E7D32, #43A047);
            padding: 10px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
        }
        .nav-item {
            color: white;
            text-decoration: none;
            padding: 15px 20px;
            margin-right: 5px;
            border-radius: 0;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 16px;
            letter-spacing: 0.5px;
            position: relative;
            display: flex;
            align-items: center;
        }
        .nav-item:hover, .nav-item.active {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }
        .nav-item.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 20%;
            right: 20%;
            height: 3px;
            background-color: white;
            border-radius: 3px 3px 0 0;
        }
        .card {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            transform: translateY(-5px);
        }
        .card-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .dashboard {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
        }
        .stat-card {
            text-align: center;
            padding: 30px 20px;
        }
        .stat-value {
            font-size: 36px;
            font-weight: bold;
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin: 15px 0;
            text-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .stat-label {
            color: #7f8c8d;
        }
        .chart-container {
            height: 300px;
            margin-top: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        th, td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        th {
            background: linear-gradient(to right, #43A047, #66BB6A);
            color: white;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 14px;
            letter-spacing: 0.5px;
        }
        tr:last-child td {
            border-bottom: none;
        }
        tr:nth-child(even) {
            background-color: rgba(0,0,0,0.02);
        }
        tr:hover {
            background-color: rgba(76, 175, 80, 0.05);
        }
        .status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            display: inline-block;
        }
        .status-active {
            background: linear-gradient(to right, #2ecc71, #27ae60);
            color: white;
        }
        .status-inactive {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        .btn-primary {
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            color: white;
        }
        .btn-success {
            background: linear-gradient(to right, #2ecc71, #27ae60);
            color: white;
        }
        .btn-danger {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 30px;
            font-size: 14px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 2px 10px rgba(76, 175, 80, 0.2);
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        .modal-content {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 20px;
            width: 500px;
            max-width: 90%;
            padding: 30px;
            box-shadow: 0 15px 50px rgba(0,0,0,0.2);
            border: 1px solid rgba(0,0,0,0.05);
        }
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .modal-title {
            font-size: 18px;
            font-weight: bold;
        }
        .close-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
        }
        .modal-footer {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
        }
        .modal-footer button {
            margin-left: 10px;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="demo-header">
        <div class="header-content">
            <div class="logo">碳排放管理系统</div>
            <div class="user-info">
                <span class="user-name">管理员</span>
                <a href="/login" class="logout-btn">退出登录</a>
            </div>
        </div>
    </div>

    <div class="demo-nav">
        <div class="nav-content">
            <a href="/admin" class="nav-item active"><i class="fas fa-tachometer-alt mr-2"></i>系统概览</a>
            <a href="/admin_users" class="nav-item"><i class="fas fa-users mr-2"></i>用户管理</a>
            <a href="/admin_quotas" class="nav-item"><i class="fas fa-chart-pie mr-2"></i>配额管理</a>
            <a href="/admin_verifications" class="nav-item"><i class="fas fa-clipboard-check mr-2"></i>核查管理</a>
            <a href="/admin_transactions" class="nav-item"><i class="fas fa-exchange-alt mr-2"></i>交易管理</a>
            <a href="/admin_reports" class="nav-item"><i class="fas fa-file-alt mr-2"></i>系统报告</a>
            <a href="/admin_settings" class="nav-item"><i class="fas fa-cog mr-2"></i>系统配置</a>
            <a href="/admin_logs" class="nav-item"><i class="fas fa-list-alt mr-2"></i>日志查看</a>
            <a href="/admin_blockchain_config" class="nav-item"><i class="fas fa-link mr-2"></i>区块链配置</a>
        </div>
    </div>

    <div class="container">
        <h1>系统概览</h1>

        <div class="dashboard">
            <div class="card stat-card">
                <div class="stat-label">企业用户</div>
                <div class="stat-value">15</div>
                <div class="stat-label">个</div>
            </div>
            <div class="card stat-card">
                <div class="stat-label">核查机构</div>
                <div class="stat-value">5</div>
                <div class="stat-label">个</div>
            </div>
            <div class="card stat-card">
                <div class="stat-label">总排放量</div>
                <div class="stat-value">25,450</div>
                <div class="stat-label">吨CO2e</div>
            </div>
            <div class="card stat-card">
                <div class="stat-label">交易总额</div>
                <div class="stat-value">1,250,000</div>
                <div class="stat-label">元</div>
            </div>
        </div>

        <div class="card">
            <div class="card-title">系统活动</div>
            <table>
                <thead>
                    <tr>
                        <th>时间</th>
                        <th>用户</th>
                        <th>活动类型</th>
                        <th>详情</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>2023-05-12 09:45:23</td>
                        <td>enterprise1</td>
                        <td>提交排放数据</td>
                        <td>提交了2023年4月的排放数据</td>
                    </tr>
                    <tr>
                        <td>2023-05-12 10:15:47</td>
                        <td>verifier1</td>
                        <td>核查排放数据</td>
                        <td>核查了enterprise1的排放数据</td>
                    </tr>
                    <tr>
                        <td>2023-05-12 11:30:12</td>
                        <td>enterprise2</td>
                        <td>交易</td>
                        <td>向enterprise3购买了500吨配额</td>
                    </tr>
                    <tr>
                        <td>2023-05-12 13:20:05</td>
                        <td>admin</td>
                        <td>系统配置</td>
                        <td>修改了碳价格配置</td>
                    </tr>
                    <tr>
                        <td>2023-05-12 14:45:33</td>
                        <td>enterprise3</td>
                        <td>生成报告</td>
                        <td>生成了2023年第一季度排放报告</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="card">
            <div class="card-title">用户列表</div>
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>用户名</th>
                        <th>角色</th>
                        <th>公司名称</th>
                        <th>注册时间</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>admin</td>
                        <td>管理员</td>
                        <td>系统管理</td>
                        <td>2023-01-01</td>
                        <td><span class="status status-active">活跃</span></td>
                        <td><a href="/admin_user_detail?id=1" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>enterprise1</td>
                        <td>企业</td>
                        <td>北京碳排放科技有限公司</td>
                        <td>2023-01-15</td>
                        <td><span class="status status-active">活跃</span></td>
                        <td><a href="/admin_user_detail?id=2" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td>enterprise2</td>
                        <td>企业</td>
                        <td>上海绿色能源有限公司</td>
                        <td>2023-01-20</td>
                        <td><span class="status status-active">活跃</span></td>
                        <td><a href="/admin_user_detail?id=3" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>4</td>
                        <td>verifier1</td>
                        <td>核查机构</td>
                        <td>国家碳排放核查中心</td>
                        <td>2023-02-01</td>
                        <td><span class="status status-active">活跃</span></td>
                        <td><a href="/admin_user_detail?id=4" class="btn btn-primary">查看</a></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // 简单的演示脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 导航切换 - 不需要额外的事件监听器，因为我们使用的是链接
            // 根据当前URL设置活动导航项
            const currentPath = window.location.pathname;
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                if (item.getAttribute('href') === currentPath) {
                    navItems.forEach(i => i.classList.remove('active'));
                    item.classList.add('active');
                }
            });

            // 退出登录
            document.querySelector('.logout-btn').addEventListener('click', function() {
                window.location.href = '/login';
            });

            // 添加系统活动图表
            const activityChartContainer = document.createElement('div');
            activityChartContainer.className = 'chart-container';
            activityChartContainer.innerHTML = '<canvas id="activityChart"></canvas>';

            const activityCard = document.querySelector('.card:nth-child(2)');
            activityCard.insertBefore(activityChartContainer, activityCard.querySelector('table'));

            const activityCtx = document.getElementById('activityChart').getContext('2d');
            const activityChart = new Chart(activityCtx, {
                type: 'line',
                data: {
                    labels: ['5月6日', '5月7日', '5月8日', '5月9日', '5月10日', '5月11日', '5月12日'],
                    datasets: [
                        {
                            label: '排放数据提交',
                            data: [5, 7, 4, 6, 8, 9, 12],
                            borderColor: 'rgba(46, 204, 113, 1)',
                            backgroundColor: 'rgba(46, 204, 113, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        },
                        {
                            label: '核查活动',
                            data: [3, 4, 2, 5, 6, 7, 8],
                            borderColor: 'rgba(52, 152, 219, 1)',
                            backgroundColor: 'rgba(52, 152, 219, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        },
                        {
                            label: '交易活动',
                            data: [2, 3, 1, 4, 3, 5, 6],
                            borderColor: 'rgba(155, 89, 182, 1)',
                            backgroundColor: 'rgba(155, 89, 182, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '活动数量'
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: '最近7天系统活动'
                        }
                    }
                }
            });
        });
    </script>
{% endblock %}
