"""
碳足迹计算工具
"""

class CarbonCalculator:
    """碳足迹计算工具"""
    
    # 排放因子 (kgCO2e/单位)
    EMISSION_FACTORS = {
        # 电力排放因子 (kgCO2e/kWh)
        'electricity': {
            'default': 0.5839,  # 全国平均
            'north': 0.6419,    # 华北电网
            'northeast': 0.7769,  # 东北电网
            'east': 0.5427,     # 华东电网
            'central': 0.5257,  # 华中电网
            'northwest': 0.6220,  # 西北电网
            'south': 0.4873     # 南方电网
        },
        
        # 燃料排放因子
        'fuel': {
            'coal': 2.86,       # 煤炭 (kgCO2e/kg)
            'natural_gas': 2.1,  # 天然气 (kgCO2e/m³)
            'gasoline': 2.3,    # 汽油 (kgCO2e/L)
            'diesel': 2.68,     # 柴油 (kgCO2e/L)
            'lpg': 1.51,        # 液化石油气 (kgCO2e/kg)
            'fuel_oil': 3.07    # 燃料油 (kgCO2e/kg)
        },
        
        # 交通排放因子 (kgCO2e/km)
        'transportation': {
            'car_gasoline': 0.19,  # 汽油车
            'car_diesel': 0.16,    # 柴油车
            'car_electric': 0.05,  # 电动车
            'bus': 0.105,          # 公交车
            'train': 0.041,        # 火车
            'plane': 0.255,        # 飞机
            'ship': 0.025          # 船舶
        },
        
        # 废弃物排放因子 (kgCO2e/kg)
        'waste': {
            'landfill': 0.99,      # 填埋
            'incineration': 0.58,  # 焚烧
            'recycling': 0.02,     # 回收
            'composting': 0.08     # 堆肥
        },
        
        # 工业过程排放因子
        'industrial': {
            'cement': 0.52,        # 水泥生产 (kgCO2e/kg)
            'steel': 1.85,         # 钢铁生产 (kgCO2e/kg)
            'aluminum': 9.7,       # 铝生产 (kgCO2e/kg)
            'ammonia': 2.1,        # 氨生产 (kgCO2e/kg)
            'lime': 0.75           # 石灰生产 (kgCO2e/kg)
        }
    }
    
    @staticmethod
    def calculate_electricity_emission(consumption_kwh, region='default'):
        """计算电力消耗产生的碳排放"""
        factor = CarbonCalculator.EMISSION_FACTORS['electricity'].get(region, 
                                                                     CarbonCalculator.EMISSION_FACTORS['electricity']['default'])
        return consumption_kwh * factor
    
    @staticmethod
    def calculate_fuel_emission(fuel_type, consumption):
        """计算燃料消耗产生的碳排放"""
        if fuel_type in CarbonCalculator.EMISSION_FACTORS['fuel']:
            return consumption * CarbonCalculator.EMISSION_FACTORS['fuel'][fuel_type]
        return 0
    
    @staticmethod
    def calculate_transportation_emission(vehicle_type, distance_km, fuel_efficiency=None):
        """计算交通产生的碳排放"""
        if vehicle_type in CarbonCalculator.EMISSION_FACTORS['transportation']:
            if fuel_efficiency and vehicle_type in ['car_gasoline', 'car_diesel']:
                # 如果提供了燃油效率，使用自定义计算
                fuel_consumption = distance_km * fuel_efficiency / 100  # L
                
                if vehicle_type == 'car_gasoline':
                    return fuel_consumption * CarbonCalculator.EMISSION_FACTORS['fuel']['gasoline']
                elif vehicle_type == 'car_diesel':
                    return fuel_consumption * CarbonCalculator.EMISSION_FACTORS['fuel']['diesel']
            
            # 使用默认排放因子
            return distance_km * CarbonCalculator.EMISSION_FACTORS['transportation'][vehicle_type]
        
        return 0
    
    @staticmethod
    def calculate_waste_emission(waste_type, weight_kg):
        """计算废弃物处理产生的碳排放"""
        if waste_type in CarbonCalculator.EMISSION_FACTORS['waste']:
            return weight_kg * CarbonCalculator.EMISSION_FACTORS['waste'][waste_type]
        return 0
    
    @staticmethod
    def calculate_industrial_emission(process_type, production_kg):
        """计算工业过程产生的碳排放"""
        if process_type in CarbonCalculator.EMISSION_FACTORS['industrial']:
            return production_kg * CarbonCalculator.EMISSION_FACTORS['industrial'][process_type]
        return 0
    
    @staticmethod
    def calculate_total_emission(data):
        """计算总碳排放量"""
        total = 0
        breakdown = {
            'electricity': 0,
            'fuel': 0,
            'transportation': 0,
            'waste': 0,
            'industrial': 0
        }
        
        # 电力排放
        if 'electricity' in data:
            region = data.get('electricity_region', 'default')
            electricity_emission = CarbonCalculator.calculate_electricity_emission(
                data['electricity'], region)
            total += electricity_emission
            breakdown['electricity'] = electricity_emission
        
        # 燃料排放
        for fuel_type in ['coal', 'natural_gas', 'gasoline', 'diesel', 'lpg', 'fuel_oil']:
            if fuel_type in data:
                fuel_emission = CarbonCalculator.calculate_fuel_emission(fuel_type, data[fuel_type])
                total += fuel_emission
                breakdown['fuel'] += fuel_emission
        
        # 交通排放
        if 'transportation' in data:
            for item in data['transportation']:
                if 'vehicle_type' in item and 'distance' in item:
                    transportation_emission = CarbonCalculator.calculate_transportation_emission(
                        item['vehicle_type'], 
                        item['distance'],
                        item.get('fuel_efficiency')
                    )
                    total += transportation_emission
                    breakdown['transportation'] += transportation_emission
        
        # 废弃物排放
        if 'waste' in data:
            for item in data['waste']:
                if 'waste_type' in item and 'weight' in item:
                    waste_emission = CarbonCalculator.calculate_waste_emission(
                        item['waste_type'], item['weight'])
                    total += waste_emission
                    breakdown['waste'] += waste_emission
        
        # 工业过程排放
        if 'industrial' in data:
            for item in data['industrial']:
                if 'process_type' in item and 'production' in item:
                    industrial_emission = CarbonCalculator.calculate_industrial_emission(
                        item['process_type'], item['production'])
                    total += industrial_emission
                    breakdown['industrial'] += industrial_emission
        
        return {
            'total_emission': total,
            'unit': 'kgCO2e',
            'breakdown': breakdown
        }
