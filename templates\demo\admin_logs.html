<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放管理系统 - 日志查看</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/unified-nav.css') }}">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background: linear-gradient(to right, #1B5E20, #388E3C);
            color: white;
            padding: 20px 0;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-name {
            margin-right: 15px;
        }
        .logout-btn {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 30px;
            cursor: pointer;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .logout-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        nav {
            background: linear-gradient(to right, #2E7D32, #43A047);
            padding: 10px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
        }
        .nav-item {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            margin-right: 5px;
            border-radius: 30px;
            transition: all 0.3s ease;
        }
        .nav-item:hover, .nav-item.active {
            background: linear-gradient(to right, #1B5E20, #2E7D32);
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        .card {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            transform: translateY(-5px);
        }
        .card-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .filter-container {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }
        .filter-item {
            flex: 1;
        }
        .filter-item label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #2c3e50;
        }
        .filter-item select, .filter-item input {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 30px;
            font-size: 14px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        th, td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        th {
            background: linear-gradient(to right, #43A047, #66BB6A);
            color: white;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 14px;
            letter-spacing: 0.5px;
        }
        tr:last-child td {
            border-bottom: none;
        }
        tr:nth-child(even) {
            background-color: rgba(0,0,0,0.02);
        }
        tr:hover {
            background-color: rgba(76, 175, 80, 0.05);
        }
        .log-level {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            display: inline-block;
        }
        .log-info {
            background: linear-gradient(to right, #3498db, #2980b9);
            color: white;
        }
        .log-warning {
            background: linear-gradient(to right, #f39c12, #e67e22);
            color: white;
        }
        .log-error {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
        }
        .log-debug {
            background: linear-gradient(to right, #95a5a6, #7f8c8d);
            color: white;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        .btn-primary {
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            color: white;
        }
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }
        .pagination a {
            color: #2c3e50;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 30px;
            margin: 0 5px;
            transition: all 0.3s ease;
        }
        .pagination a.active {
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            color: white;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        .pagination a:hover:not(.active) {
            background-color: #ddd;
        }
        .chart-container {
            height: 300px;
            margin-top: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo">碳排放管理系统</div>
            <div class="user-info">
                <span class="user-name">管理员</span>
                <a href="/login" class="logout-btn">退出登录</a>
            </div>
        </div>
    </header>

    <div class="demo-nav">
        <div class="nav-content">
            <a href="/admin" class="nav-item"><i class="fas fa-tachometer-alt mr-2"></i>系统概览</a>
            <a href="/admin_users" class="nav-item"><i class="fas fa-users mr-2"></i>用户管理</a>
            <a href="/admin_quotas" class="nav-item"><i class="fas fa-chart-pie mr-2"></i>配额管理</a>
            <a href="/admin_verifications" class="nav-item"><i class="fas fa-clipboard-check mr-2"></i>核查管理</a>
            <a href="/admin_transactions" class="nav-item"><i class="fas fa-exchange-alt mr-2"></i>交易管理</a>
            <a href="/admin_reports" class="nav-item"><i class="fas fa-file-alt mr-2"></i>系统报告</a>
            <a href="/admin_settings" class="nav-item"><i class="fas fa-cog mr-2"></i>系统配置</a>
            <a href="/admin_logs" class="nav-item active"><i class="fas fa-list-alt mr-2"></i>日志查看</a>
            <a href="/admin_blockchain_config" class="nav-item"><i class="fas fa-link mr-2"></i>区块链配置</a>
        </div>
    </div>

    <div class="container">
        <h1>系统日志</h1>

        <div class="card">
            <div class="card-title">日志统计</div>
            <div class="chart-container">
                <canvas id="logChart"></canvas>
            </div>
        </div>

        <div class="card">
            <div class="card-title">
                <span>日志筛选</span>
                <button class="btn btn-primary">导出日志</button>
            </div>
            <div class="filter-container">
                <div class="filter-item">
                    <label for="log-level">日志级别</label>
                    <select id="log-level">
                        <option value="all">全部</option>
                        <option value="info">信息</option>
                        <option value="warning">警告</option>
                        <option value="error">错误</option>
                        <option value="debug">调试</option>
                    </select>
                </div>
                <div class="filter-item">
                    <label for="log-module">模块</label>
                    <select id="log-module">
                        <option value="all">全部</option>
                        <option value="auth">认证</option>
                        <option value="emission">排放数据</option>
                        <option value="verification">核查</option>
                        <option value="transaction">交易</option>
                        <option value="blockchain">区块链</option>
                    </select>
                </div>
                <div class="filter-item">
                    <label for="log-date-from">开始日期</label>
                    <input type="date" id="log-date-from" value="2023-05-01">
                </div>
                <div class="filter-item">
                    <label for="log-date-to">结束日期</label>
                    <input type="date" id="log-date-to" value="2023-05-12">
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-title">日志列表</div>
            <table>
                <thead>
                    <tr>
                        <th>时间</th>
                        <th>级别</th>
                        <th>模块</th>
                        <th>用户</th>
                        <th>IP地址</th>
                        <th>消息</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>2023-05-12 09:45:23</td>
                        <td><span class="log-level log-info">信息</span></td>
                        <td>认证</td>
                        <td>admin</td>
                        <td>*************</td>
                        <td>管理员登录成功</td>
                    </tr>
                    <tr>
                        <td>2023-05-12 09:30:15</td>
                        <td><span class="log-level log-warning">警告</span></td>
                        <td>认证</td>
                        <td>enterprise1</td>
                        <td>*************</td>
                        <td>登录失败：密码错误</td>
                    </tr>
                    <tr>
                        <td>2023-05-12 09:15:47</td>
                        <td><span class="log-level log-info">信息</span></td>
                        <td>排放数据</td>
                        <td>enterprise1</td>
                        <td>*************</td>
                        <td>提交了新的排放数据 ID:1001</td>
                    </tr>
                    <tr>
                        <td>2023-05-12 08:45:33</td>
                        <td><span class="log-level log-error">错误</span></td>
                        <td>区块链</td>
                        <td>system</td>
                        <td>127.0.0.1</td>
                        <td>区块链同步失败：连接超时</td>
                    </tr>
                    <tr>
                        <td>2023-05-12 08:30:12</td>
                        <td><span class="log-level log-info">信息</span></td>
                        <td>核查</td>
                        <td>verifier1</td>
                        <td>192.168.1.102</td>
                        <td>核查了排放数据 ID:1000</td>
                    </tr>
                    <tr>
                        <td>2023-05-12 08:15:05</td>
                        <td><span class="log-level log-debug">调试</span></td>
                        <td>系统</td>
                        <td>system</td>
                        <td>127.0.0.1</td>
                        <td>系统启动完成，所有服务正常运行</td>
                    </tr>
                    <tr>
                        <td>2023-05-11 17:45:23</td>
                        <td><span class="log-level log-info">信息</span></td>
                        <td>交易</td>
                        <td>enterprise2</td>
                        <td>192.168.1.103</td>
                        <td>创建了新的交易 ID:2001</td>
                    </tr>
                    <tr>
                        <td>2023-05-11 17:30:47</td>
                        <td><span class="log-level log-warning">警告</span></td>
                        <td>系统</td>
                        <td>system</td>
                        <td>127.0.0.1</td>
                        <td>数据库连接池达到80%容量</td>
                    </tr>
                    <tr>
                        <td>2023-05-11 17:15:12</td>
                        <td><span class="log-level log-info">信息</span></td>
                        <td>认证</td>
                        <td>enterprise3</td>
                        <td>192.168.1.104</td>
                        <td>用户登录成功</td>
                    </tr>
                    <tr>
                        <td>2023-05-11 16:45:33</td>
                        <td><span class="log-level log-error">错误</span></td>
                        <td>排放数据</td>
                        <td>enterprise3</td>
                        <td>192.168.1.104</td>
                        <td>排放数据保存失败：数据格式错误</td>
                    </tr>
                </tbody>
            </table>
            <div class="pagination">
                <a href="#">&laquo;</a>
                <a href="#" class="active">1</a>
                <a href="#">2</a>
                <a href="#">3</a>
                <a href="#">4</a>
                <a href="#">5</a>
                <a href="#">&raquo;</a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 导航切换
            const currentPath = window.location.pathname;
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                if (item.getAttribute('href') === currentPath) {
                    navItems.forEach(i => i.classList.remove('active'));
                    item.classList.add('active');
                }
            });

            // 日志统计图表
            const ctx = document.getElementById('logChart').getContext('2d');
            const logChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['5月6日', '5月7日', '5月8日', '5月9日', '5月10日', '5月11日', '5月12日'],
                    datasets: [
                        {
                            label: '信息',
                            data: [65, 72, 68, 75, 82, 78, 70],
                            backgroundColor: 'rgba(52, 152, 219, 0.7)',
                        },
                        {
                            label: '警告',
                            data: [15, 12, 18, 14, 10, 16, 12],
                            backgroundColor: 'rgba(243, 156, 18, 0.7)',
                        },
                        {
                            label: '错误',
                            data: [5, 3, 7, 4, 2, 5, 3],
                            backgroundColor: 'rgba(231, 76, 60, 0.7)',
                        },
                        {
                            label: '调试',
                            data: [25, 30, 28, 32, 27, 29, 31],
                            backgroundColor: 'rgba(149, 165, 166, 0.7)',
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            stacked: true,
                        },
                        y: {
                            stacked: true,
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: '最近7天日志统计'
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>
