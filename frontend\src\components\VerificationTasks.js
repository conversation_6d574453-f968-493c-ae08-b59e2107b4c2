import React, { useState, useEffect } from 'react';
import axios from 'axios';
import '../styles/VerificationTasks.css';

function VerificationTasks() {
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    deadline: '',
    priority: 'medium',
    assignedTo: ''
  });

  useEffect(() => {
    fetchTasks();
  }, []);

  const fetchTasks = async () => {
    try {
      const response = await axios.get('/api/verification-tasks', {
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      setTasks(response.data);
      setLoading(false);
    } catch (err) {
      setError('获取核查任务失败');
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await axios.post('/api/verification-tasks', formData, {
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      fetchTasks();
      setFormData({
        title: '',
        description: '',
        deadline: '',
        priority: 'medium',
        assignedTo: ''
      });
    } catch (err) {
      setError('创建核查任务失败');
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleStatusChange = async (taskId, newStatus) => {
    try {
      await axios.patch(`/api/verification-tasks/${taskId}`, 
        { status: newStatus },
        { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } }
      );
      fetchTasks();
    } catch (err) {
      setError('更新任务状态失败');
    }
  };

  if (loading) return <div className="loading">加载中...</div>;

  return (
    <div className="verification-tasks">
      <h2>核查任务管理</h2>
      {error && <div className="alert alert-danger">{error}</div>}
      
      <div className="task-form">
        <h3>创建新核查任务</h3>
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="title">任务标题</label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              required
              className="form-control"
            />
          </div>
          <div className="form-group">
            <label htmlFor="description">任务描述</label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              required
              className="form-control"
              rows="3"
            />
          </div>
          <div className="form-group">
            <label htmlFor="deadline">截止日期</label>
            <input
              type="date"
              id="deadline"
              name="deadline"
              value={formData.deadline}
              onChange={handleChange}
              required
              className="form-control"
            />
          </div>
          <div className="form-group">
            <label htmlFor="priority">优先级</label>
            <select
              id="priority"
              name="priority"
              value={formData.priority}
              onChange={handleChange}
              required
              className="form-control"
            >
              <option value="low">低</option>
              <option value="medium">中</option>
              <option value="high">高</option>
            </select>
          </div>
          <div className="form-group">
            <label htmlFor="assignedTo">分配给</label>
            <input
              type="text"
              id="assignedTo"
              name="assignedTo"
              value={formData.assignedTo}
              onChange={handleChange}
              required
              className="form-control"
            />
          </div>
          <button type="submit" className="btn btn-primary">
            创建任务
          </button>
        </form>
      </div>

      <div className="task-list">
        <h3>任务列表</h3>
        <div className="table-responsive">
          <table className="table">
            <thead>
              <tr>
                <th>标题</th>
                <th>描述</th>
                <th>截止日期</th>
                <th>优先级</th>
                <th>分配给</th>
                <th>状态</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              {tasks.map(task => (
                <tr key={task.id}>
                  <td>{task.title}</td>
                  <td>{task.description}</td>
                  <td>{new Date(task.deadline).toLocaleDateString()}</td>
                  <td>
                    <span className={`priority ${task.priority}`}>
                      {task.priority === 'low' ? '低' :
                       task.priority === 'medium' ? '中' : '高'}
                    </span>
                  </td>
                  <td>{task.assignedTo}</td>
                  <td>
                    <span className={`status ${task.status}`}>
                      {task.status === 'pending' ? '待处理' :
                       task.status === 'in_progress' ? '进行中' :
                       task.status === 'completed' ? '已完成' : '已取消'}
                    </span>
                  </td>
                  <td>
                    <select
                      value={task.status}
                      onChange={(e) => handleStatusChange(task.id, e.target.value)}
                      className="status-select"
                    >
                      <option value="pending">待处理</option>
                      <option value="in_progress">进行中</option>
                      <option value="completed">已完成</option>
                      <option value="cancelled">已取消</option>
                    </select>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default VerificationTasks; 