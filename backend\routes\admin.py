"""
管理员相关路由
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity

from backend import db
from backend.models.user import User
from backend.models.activity import Activity
from backend.utils.auth import admin_required

admin_bp = Blueprint('admin', __name__)

@admin_bp.route('/users', methods=['GET'])
@admin_required
def get_users():
    users = User.query.all()
    return jsonify({
        'users': [user.to_dict() for user in users]
    }), 200

@admin_bp.route('/users/<int:user_id>', methods=['GET'])
@admin_required
def get_user(user_id):
    user = User.query.get(user_id)
    if not user:
        return jsonify({'error': '用户不存在'}), 404

    return jsonify({
        'user': user.to_dict()
    }), 200

@admin_bp.route('/users/<int:user_id>', methods=['PUT'])
@admin_required
def update_user(user_id):
    user = User.query.get(user_id)
    if not user:
        return jsonify({'error': '用户不存在'}), 404

    data = request.get_json()

    # 更新用户信息
    if 'role' in data:
        user.role = data['role']

    if 'email' in data:
        # 检查邮箱是否已被其他用户使用
        existing_user = User.query.filter_by(email=data['email']).first()
        if existing_user and existing_user.id != user.id:
            return jsonify({'error': '邮箱已被使用'}), 400
        user.email = data['email']

    if 'company_name' in data:
        user.company_name = data['company_name']

    if 'credit_code' in data:
        user.credit_code = data['credit_code']

    if 'password' in data:
        user.set_password(data['password'])

    db.session.commit()

    # 记录活动
    activity = Activity(
        user_id=get_jwt_identity(),
        activity_type='update_user',
        description=f'管理员更新了用户 {user.username} 的信息'
    )
    db.session.add(activity)
    db.session.commit()

    return jsonify({
        'message': '用户信息更新成功',
        'user': user.to_dict()
    }), 200

@admin_bp.route('/users/<int:user_id>', methods=['DELETE'])
@admin_required
def delete_user(user_id):
    user = User.query.get(user_id)
    if not user:
        return jsonify({'error': '用户不存在'}), 404

    # 记录活动
    activity = Activity(
        user_id=get_jwt_identity(),
        activity_type='delete_user',
        description=f'管理员删除了用户 {user.username}'
    )
    db.session.add(activity)

    # 删除用户
    db.session.delete(user)
    db.session.commit()

    return jsonify({
        'message': '用户删除成功'
    }), 200

@admin_bp.route('/activities', methods=['GET'])
@admin_required
def get_activities():
    activities = Activity.query.order_by(Activity.timestamp.desc()).all()
    return jsonify({
        'activities': [activity.to_dict() for activity in activities]
    }), 200
