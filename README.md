# 碳排放管理系统 (2025版)

这是一个基于 Flask、React 和区块链技术的碳排放管理系统，用于管理企业的碳排放数据、核查、交易和预测分析。本系统是本科毕业设计作品，采用绿色环保风格设计。

## 功能特点

- **用户管理**：支持管理员、企业用户和核查机构三种角色
- **排放数据管理**：企业用户提交排放数据，核查机构进行核查
- **碳交易**：企业用户之间进行碳配额交易
- **碳计算器**：计算不同活动的碳排放量
- **预测分析**：分析排放趋势，预测未来排放
- **报告生成**：生成排放报告、交易报告、核查报告和统计报告
- **区块链集成**：将关键数据记录到区块链，保证数据不可篡改
- **绿色环保UI**：采用统一的绿色环保风格设计，提供良好的用户体验

## 技术栈

- **后端**：Flask (Python)
- **前端**：HTML, CSS, JavaScript
- **数据库**：MySQL
- **区块链**：以太坊 (Solidity)
- **认证**：基于会话的认证
- **API**：RESTful API
- **可视化**：SVG图表和数据可视化

## 系统模块

### 1. 用户管理模块

- 用户注册、登录和权限管理
- 三种角色：管理员、企业用户和核查机构
- 用户信息管理和密码修改

### 2. 排放数据管理模块

- 企业用户提交排放数据
- 支持多种排放源和计算方法
- 数据审核和版本控制

### 3. 核查管理模块

- 核查机构接收和处理核查任务
- 核查过程记录和结果反馈
- 核查报告生成和管理

### 4. 碳交易模块

- 企业间碳配额交易
- 交易记录和历史查询
- 交易统计和分析

### 5. 碳计算器模块

- 多种活动的碳排放计算
- 自定义排放因子
- 计算结果保存和分享

### 6. 预测分析模块

- 排放趋势分析
- 未来排放预测
- 减排潜力评估

### 7. 报告生成模块

- 多种报告模板
- 自动生成PDF和HTML报告
- 报告管理和分享

### 8. 区块链模块

- 关键数据上链
- 数据验证和溯源
- 区块链浏览器集成

## 安装说明

### 环境要求

- Python 3.8+
- MySQL 5.7+
- 现代浏览器（Chrome, Firefox, Edge等）
- Ganache（本地以太坊节点）

### 安装步骤

1. 克隆项目：
```bash
git clone https://github.com/yourusername/carbon-emission-system.git
cd carbon-emission-system
```

2. 创建虚拟环境：
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows
```

3. 安装后端依赖：
```bash
pip install -r requirements.txt
```

4. 安装前端依赖：
```bash
cd frontend
npm install
cd ..
```

5. 配置环境变量：
```bash
cp .env.example .env
# 编辑 .env 文件，设置数据库连接信息和其他配置
```

6. 初始化数据库：
```bash
python init_db.py
```

7. 生成演示数据（可选）：
```bash
python generate_demo_data.py
```

8. 启动系统：
```bash
python run.py
```

## 区块链配置指南

系统使用以太坊区块链来存储关键数据，确保数据的不可篡改和透明化。以下是配置区块链的步骤：

### 1. 安装Ganache

Ganache是一个本地以太坊区块链，用于开发和测试智能合约。您可以从[Ganache官网](https://www.trufflesuite.com/ganache)下载并安装。

### 2. 启动Ganache

启动Ganache后，它会创建10个测试账户，每个账户有100个测试以太币。请记下RPC服务器URL（默认为`http://127.0.0.1:8545`）。

### 3. 配置区块链连接

访问系统的区块链配置页面（`/admin/blockchain/config`），配置以下信息：

- **以太坊节点URL**：Ganache的RPC服务器URL
- **管理员地址**：Ganache中第1个账户的地址
- **管理员私钥**：Ganache中第1个账户的私钥
- **企业地址**：Ganache中第2、3、4个账户的地址
- **企业私钥**：Ganache中第2、3、4个账户的私钥
- **核查机构地址**：Ganache中第5、6个账户的地址
- **核查机构私钥**：Ganache中第5、6个账户的私钥

### 4. 部署智能合约

在区块链配置页面中，点击"部署智能合约"按钮，系统会自动部署智能合约，并将合约地址保存到配置中。

### 5. 验证区块链连接

部署完成后，您可以在系统中进行以下操作来验证区块链连接：

- 企业提交排放数据
- 核查机构核查排放数据
- 企业之间进行交易
- 管理员对违规企业进行惩罚

这些操作都会将数据上传到区块链，您可以在Ganache中查看交易记录。

## 项目结构

```
CMS/
├── backend/                   # 后端部分
│   ├── __init__.py            # 应用初始化和创建
│   ├── models/                # 数据库模型
│   │   ├── user.py            # 用户模型
│   │   ├── emission.py        # 排放数据模型
│   │   ├── verification.py    # 核查记录模型
│   │   ├── activity.py        # 活动记录模型
│   │   ├── carbon_quota.py    # 碳配额模型
│   │   ├── transaction.py     # 交易记录模型
│   │   └── report.py          # 报告模型
│   ├── routes/                # API路由
│   │   ├── auth.py            # 认证相关路由
│   │   ├── admin.py           # 管理员相关路由
│   │   ├── verification.py    # 核查相关路由
│   │   ├── dashboard.py       # 仪表板相关路由
│   │   ├── emissions.py       # 排放数据相关路由
│   │   ├── transaction.py     # 交易相关路由
│   │   ├── calculator.py      # 碳计算器相关路由
│   │   ├── prediction.py      # 预测分析相关路由
│   │   └── report.py          # 报告相关路由
│   ├── utils/                 # 工具类
│   │   ├── carbon_calculator.py # 碳足迹计算工具
│   │   ├── prediction.py      # 排放预测工具
│   │   └── report_generator.py # 报告生成工具
│   └── blockchain/            # 区块链客户端
│       └── client.py          # 区块链客户端
│
├── blockchain/                # 区块链智能合约
│   └── contracts/             # 智能合约
│       └── CarbonEmission.sol # 碳排放合约
│
├── frontend/                  # 前端部分（React）
│   ├── src/                   # React源代码
│   │   ├── components/        # React组件
│   │   ├── pages/             # 页面组件
│   │   ├── contexts/          # React上下文
│   │   ├── services/          # 服务
│   │   ├── styles/            # CSS样式
│   │   └── App.js             # 应用入口
│   └── package.json           # 前端项目配置
│
├── templates/                 # 报告模板
│   └── reports/               # 报告HTML模板
│
├── .env                       # 环境变量配置
├── .env.example               # 环境变量示例
├── create_tables.sql          # 数据库表创建SQL脚本
├── db_utils.py                # 数据库工具函数
├── init_db.py                 # 数据库初始化脚本
├── generate_demo_data.py      # 演示数据生成脚本
├── start.py                   # 一键启动脚本
├── requirements.txt           # 依赖列表
└── run.py                     # 应用启动脚本
```

## 系统演示

### 演示账号

- **管理员**：admin / admin123
- **企业用户**：enterprise1 / password123
- **核查机构**：verifier1 / password123

### 演示流程

1. **登录系统**：
   - 打开浏览器访问系统首页
   - 使用企业用户账号登录

2. **提交排放数据**：
   - 进入排放数据页面
   - 点击"提交新数据"按钮
   - 填写排放数据表单并提交

3. **查看仪表板**：
   - 进入仪表板页面
   - 查看排放数据统计和趋势图表

4. **切换用户**：
   - 退出登录
   - 使用核查机构账号登录

5. **核查排放数据**：
   - 进入核查任务页面
   - 选择待核查的排放数据
   - 填写核查意见并提交

6. **切换用户**：
   - 退出登录
   - 使用企业用户账号登录

7. **查看核查结果**：
   - 进入排放数据页面
   - 查看排放数据的核查状态和意见

8. **交易碳配额**：
   - 进入交易页面
   - 创建新的交易
   - 查看交易历史

9. **使用碳计算器**：
   - 进入碳计算器页面
   - 输入活动数据
   - 查看计算结果

10. **查看预测分析**：
    - 进入预测分析页面
    - 查看排放趋势和预测图表

11. **生成报告**：
    - 进入报告页面
    - 选择报告类型和参数
    - 生成并下载报告

## 2025年系统更新

2025年版本的碳排放管理系统进行了以下更新：

1. **UI界面升级**：
   - 采用统一的绿色环保风格
   - 优化用户界面，提升用户体验
   - 增加响应式设计，适配不同设备

2. **功能增强**：
   - 改进报告生成模块，增加更多报告模板
   - 优化核查流程，提高核查效率
   - 增强数据可视化功能，提供更直观的数据展示

3. **性能优化**：
   - 提高系统响应速度
   - 优化数据库查询性能
   - 减少资源占用

4. **安全增强**：
   - 加强数据安全保护
   - 完善用户权限管理
   - 增加操作日志记录

## 系统特色

1. **绿色环保设计**：
   - 系统采用绿色环保风格设计，符合碳排放管理的主题
   - 使用渐变色、圆角设计和阴影效果，提升视觉体验
   - 统一的色彩方案和设计元素，保持系统风格一致性

2. **数据可视化**：
   - 使用SVG图表展示排放数据和趋势
   - 提供多种图表类型（柱状图、折线图、饼图等）
   - 支持数据筛选和导出

3. **报告模板**：
   - 提供多种专业报告模板
   - 支持自定义报告内容和格式
   - 生成美观、专业的PDF和HTML报告

4. **区块链集成**：
   - 将关键数据记录到区块链，确保数据不可篡改
   - 提供数据验证和溯源功能
   - 增强系统的可信度和透明度

## 创新点

1. **区块链技术应用**：
   - 将区块链技术应用于碳排放管理
   - 保证数据的不可篡改性和透明性
   - 提高系统的可信度和安全性

2. **碳计算器功能**：
   - 提供多种活动的碳排放计算
   - 支持自定义排放因子
   - 生成详细的计算报告

3. **预测分析功能**：
   - 使用机器学习算法预测未来排放
   - 提供多种预测模型
   - 支持模型训练和评估

4. **自动化报告生成**：
   - 自动生成各类报告
   - 支持多种报告格式（PDF、HTML等）
   - 提高工作效率

## 未来展望

1. **功能扩展**：
   - 增加碳足迹追踪功能
   - 添加碳中和规划工具
   - 集成更多数据源和分析工具

2. **技术升级**：
   - 引入人工智能技术，提升预测精度
   - 优化区块链集成，提高性能和可扩展性
   - 增强系统安全性和稳定性

3. **生态建设**：
   - 构建碳排放管理生态系统
   - 支持第三方插件和扩展
   - 促进行业合作和数据共享

## 系统局限性与未来改进

1. **当前局限性**：
   - 区块链集成仅支持本地Ganache节点
   - 预测模型精度有限
   - 用户界面可进一步优化

2. **未来改进方向**：
   - 支持连接到公共以太坊测试网络（如Rinkeby、Goerli等）
   - 优化智能合约，减少Gas消耗
   - 增加区块链浏览器功能，方便查看链上数据
   - 优化预测模型，提高预测精度
   - 改进用户界面，提升用户体验
   - 添加更多的数据可视化功能
   - 实现移动端适配

## 作者信息

- **姓名**：XXX
- **学号**：XXX
- **指导教师**：XXX
- **学院**：XXX学院

## 版权声明

© 2025 碳排放管理系统 版权所有