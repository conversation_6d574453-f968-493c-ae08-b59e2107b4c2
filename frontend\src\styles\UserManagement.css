.user-management {
  padding: 20px;
}

.user-management h2 {
  margin-bottom: 30px;
  color: #333;
}

.user-form {
  background: white;
  padding: 25px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.user-form h3 {
  margin-bottom: 20px;
  color: #444;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #555;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.form-control:focus {
  border-color: #667eea;
  outline: none;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn-primary {
  background-color: #667eea;
  color: white;
}

.btn-primary:hover {
  background-color: #5a6fd6;
}

.btn-secondary {
  background-color: #6b7280;
  color: white;
  padding: 8px 16px;
  font-size: 14px;
}

.btn-secondary:hover {
  background-color: #4b5563;
}

.btn-danger {
  background-color: #ef4444;
  color: white;
  padding: 8px 16px;
  font-size: 14px;
}

.btn-danger:hover {
  background-color: #dc2626;
}

.user-list {
  background: white;
  padding: 25px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-list h3 {
  margin-bottom: 20px;
  color: #444;
}

.table-responsive {
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #444;
}

.table tr:hover {
  background-color: #f8f9fa;
}

.role {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.role.admin {
  background-color: #fee2e2;
  color: #991b1b;
}

.role.operator {
  background-color: #fef3c7;
  color: #92400e;
}

.role.viewer {
  background-color: #dbeafe;
  color: #1e40af;
}

.status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status.active {
  background-color: #dcfce7;
  color: #166534;
}

.status.inactive {
  background-color: #fee2e2;
  color: #991b1b;
}

.status.pending {
  background-color: #fef3c7;
  color: #92400e;
}

.status-select {
  padding: 6px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background-color: white;
  margin-right: 10px;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: 18px;
  color: #666;
}

.alert {
  padding: 12px;
  border-radius: 5px;
  margin-bottom: 20px;
}

.alert-danger {
  background-color: #fee2e2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-management {
    padding: 15px;
  }

  .user-form,
  .user-list {
    padding: 15px;
  }

  .table th,
  .table td {
    padding: 8px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 5px;
  }

  .status-select {
    margin-right: 0;
    width: 100%;
  }

  .btn-secondary,
  .btn-danger {
    width: 100%;
  }
} 