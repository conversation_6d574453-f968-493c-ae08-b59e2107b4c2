<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放管理系统 - 企业详情</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background: linear-gradient(to right, #1B5E20, #388E3C);
            color: white;
            padding: 20px 0;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-name {
            margin-right: 15px;
        }
        .logout-btn {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 30px;
            cursor: pointer;
            text-decoration: none;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .logout-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        nav {
            background: linear-gradient(to right, #2E7D32, #43A047);
            padding: 10px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
        }
        .nav-item {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            margin-right: 5px;
            border-radius: 30px;
            transition: all 0.3s ease;
        }
        .nav-item:hover, .nav-item.active {
            background: linear-gradient(to right, #1B5E20, #2E7D32);
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        .card {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            transform: translateY(-5px);
        }
        .card-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        .btn-primary {
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            color: white;
        }
        .btn-success {
            background: linear-gradient(to right, #2ecc71, #27ae60);
            color: white;
        }
        .btn-danger {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
        }
        .detail-section {
            margin-bottom: 30px;
            background-color: #f9f9f9;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.05);
            border-left: 5px solid #4CAF50;
        }
        .detail-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2E7D32;
            border-bottom: 1px solid #A5D6A7;
            padding-bottom: 10px;
        }
        .detail-row {
            display: flex;
            margin-bottom: 15px;
            border-bottom: 1px dashed rgba(0,0,0,0.05);
            padding-bottom: 10px;
        }
        .detail-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        .detail-label {
            width: 180px;
            font-weight: bold;
            color: #388E3C;
        }
        .detail-value {
            flex: 1;
        }
        .status {
            display: inline-block;
            padding: 8px 15px;
            border-radius: 30px;
            font-size: 12px;
            font-weight: bold;
            color: white;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        .status-active {
            background: linear-gradient(to right, #2ecc71, #27ae60);
        }
        .status-inactive {
            background: linear-gradient(to right, #e74c3c, #c0392b);
        }
        .chart-container {
            height: 300px;
            margin-top: 20px;
            background-color: #f1f8e9;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        .chart-placeholder {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 10px;
        }
        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        th, td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        th {
            background: linear-gradient(to right, #43A047, #66BB6A);
            color: white;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 14px;
            letter-spacing: 0.5px;
        }
        tr:last-child td {
            border-bottom: none;
        }
        tr:nth-child(even) {
            background-color: rgba(0,0,0,0.02);
        }
        tr:hover {
            background-color: rgba(76, 175, 80, 0.05);
        }
        .actions {
            display: flex;
            justify-content: flex-end;
            gap: 15px;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px dashed rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo">碳排放管理系统</div>
            <div class="user-info">
                <span class="user-name">国家碳排放核查中心</span>
                <a href="/login" class="logout-btn">退出登录</a>
            </div>
        </div>
    </header>

    <nav>
        <div class="nav-content">
            <a href="/verifier" class="nav-item">仪表板</a>
            <a href="/verifier_tasks" class="nav-item">待核查任务</a>
            <a href="/verifier_records" class="nav-item">核查记录</a>
            <a href="/verifier_enterprises" class="nav-item active">企业管理</a>
            <a href="/verifier_reports" class="nav-item">报告生成</a>
        </div>
    </nav>

    <div class="container">
        <div class="card-title">
            <h1>企业详情</h1>
            <div>
                <a href="/verifier_enterprises" class="btn btn-primary">返回列表</a>
            </div>
        </div>

        <div class="card">
            <div class="detail-section">
                <div class="detail-title">基本信息</div>

                <div class="detail-row">
                    <div class="detail-label">企业ID</div>
                    <div class="detail-value">1</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">企业名称</div>
                    <div class="detail-value">北京碳排放科技有限公司</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">行业</div>
                    <div class="detail-value">能源生产</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">统一社会信用代码</div>
                    <div class="detail-value">91110000123456789A</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">注册时间</div>
                    <div class="detail-value">2023-01-15</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">状态</div>
                    <div class="detail-value">
                        <span class="status status-active">活跃</span>
                    </div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">联系人</div>
                    <div class="detail-value">张三</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">联系电话</div>
                    <div class="detail-value">13800138000</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">电子邮箱</div>
                    <div class="detail-value"><EMAIL></div>
                </div>
            </div>

            <div class="detail-section">
                <div class="detail-title">排放概况</div>

                <div class="detail-row">
                    <div class="detail-label">年度配额</div>
                    <div class="detail-value">5,000 吨CO2e</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">当前排放量</div>
                    <div class="detail-value">2,350 吨CO2e</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">剩余配额</div>
                    <div class="detail-value">2,650 吨CO2e</div>
                </div>

                <div class="chart-container">
                    <!-- 使用内联SVG代替外部图片，确保图表始终能够显示 -->
                    <svg width="100%" height="100%" viewBox="0 0 1160 300" xmlns="http://www.w3.org/2000/svg">
                        <!-- 背景网格 -->
                        <g stroke="#eee" stroke-width="1">
                            <!-- 水平线 -->
                            <line x1="50" y1="50" x2="1100" y2="50" />
                            <line x1="50" y1="100" x2="1100" y2="100" />
                            <line x1="50" y1="150" x2="1100" y2="150" />
                            <line x1="50" y1="200" x2="1100" y2="200" />
                            <line x1="50" y1="250" x2="1100" y2="250" />
                            <!-- 垂直线 -->
                            <line x1="50" y1="50" x2="50" y2="250" />
                            <line x1="150" y1="50" x2="150" y2="250" />
                            <line x1="250" y1="50" x2="250" y2="250" />
                            <line x1="350" y1="50" x2="350" y2="250" />
                            <line x1="450" y1="50" x2="450" y2="250" />
                            <line x1="550" y1="50" x2="550" y2="250" />
                            <line x1="650" y1="50" x2="650" y2="250" />
                            <line x1="750" y1="50" x2="750" y2="250" />
                            <line x1="850" y1="50" x2="850" y2="250" />
                            <line x1="950" y1="50" x2="950" y2="250" />
                            <line x1="1050" y1="50" x2="1050" y2="250" />
                        </g>

                        <!-- 坐标轴 -->
                        <g stroke="#333" stroke-width="2">
                            <line x1="50" y1="250" x2="1100" y2="250" />
                            <line x1="50" y1="50" x2="50" y2="250" />
                        </g>

                        <!-- 坐标轴标签 -->
                        <g fill="#333" font-size="12" text-anchor="middle">
                            <!-- X轴标签 -->
                            <text x="100" y="270">1月</text>
                            <text x="200" y="270">2月</text>
                            <text x="300" y="270">3月</text>
                            <text x="400" y="270">4月</text>
                            <text x="500" y="270">5月</text>
                            <text x="600" y="270">6月</text>
                            <text x="700" y="270">7月</text>
                            <text x="800" y="270">8月</text>
                            <text x="900" y="270">9月</text>
                            <text x="1000" y="270">10月</text>
                            <text x="1100" y="270">11月</text>

                            <!-- Y轴标签 -->
                            <text x="35" y="250" text-anchor="end">0</text>
                            <text x="35" y="200" text-anchor="end">200</text>
                            <text x="35" y="150" text-anchor="end">400</text>
                            <text x="35" y="100" text-anchor="end">600</text>
                            <text x="35" y="50" text-anchor="end">800</text>
                        </g>

                        <!-- 图表标题 -->
                        <text x="580" y="30" fill="#2E7D32" font-size="16" font-weight="bold" text-anchor="middle">月度碳排放趋势 (吨CO2e)</text>

                        <!-- 数据点 -->
                        <g fill="#4CAF50">
                            <circle cx="100" cy="150" r="5" />
                            <circle cx="200" cy="170" r="5" />
                            <circle cx="300" cy="130" r="5" />
                            <circle cx="400" cy="110" r="5" />
                            <circle cx="500" cy="120" r="5" />
                            <circle cx="600" cy="100" r="5" />
                            <circle cx="700" cy="90" r="5" />
                            <circle cx="800" cy="80" r="5" />
                            <circle cx="900" cy="70" r="5" />
                            <circle cx="1000" cy="60" r="5" />
                        </g>

                        <!-- 折线 -->
                        <polyline
                            points="100,150 200,170 300,130 400,110 500,120 600,100 700,90 800,80 900,70 1000,60"
                            fill="none"
                            stroke="#4CAF50"
                            stroke-width="3"
                        />

                        <!-- 区域填充 -->
                        <path
                            d="M100,150 L200,170 L300,130 L400,110 L500,120 L600,100 L700,90 L800,80 L900,70 L1000,60 L1000,250 L100,250 Z"
                            fill="url(#gradient)"
                            opacity="0.3"
                        />

                        <!-- 渐变定义 -->
                        <defs>
                            <linearGradient id="gradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                <stop offset="0%" stop-color="#4CAF50" />
                                <stop offset="100%" stop-color="#ffffff" />
                            </linearGradient>
                        </defs>

                        <!-- 图例 -->
                        <rect x="900" y="20" width="15" height="15" fill="#4CAF50" />
                        <text x="925" y="33" fill="#333" font-size="12">实际排放量</text>
                    </svg>
                </div>
            </div>

            <div class="detail-section">
                <div class="detail-title">核查记录</div>

                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>排放源</th>
                            <th>排放量</th>
                            <th>核查结论</th>
                            <th>核查时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1001</td>
                            <td>燃煤锅炉</td>
                            <td>450 吨CO2e</td>
                            <td><span class="status status-active">通过</span></td>
                            <td>2023-02-15</td>
                            <td><a href="/verification_detail?id=1001" class="btn btn-primary">查看</a></td>
                        </tr>
                        <tr>
                            <td>1004</td>
                            <td>交通运输</td>
                            <td>250 吨CO2e</td>
                            <td><span class="status status-active">通过</span></td>
                            <td>2023-05-10</td>
                            <td><a href="/verification_detail?id=1004" class="btn btn-primary">查看</a></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="actions">
                <a href="/verifier_enterprises" class="btn btn-primary">返回列表</a>
                <a href="/reports/enterprise_assessment_report.html" target="_blank" class="btn btn-success">生成报告</a>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 导航切换 - 不需要额外的事件监听器，因为我们使用的是链接
            // 根据当前URL设置活动导航项
            const currentPath = window.location.pathname;
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                if (item.getAttribute('href') === currentPath) {
                    navItems.forEach(i => i.classList.remove('active'));
                    item.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>
