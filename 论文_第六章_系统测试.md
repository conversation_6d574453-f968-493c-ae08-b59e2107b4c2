# 第六章 系统测试

本章将对基于区块链的碳排放核查系统进行全面测试，验证系统的功能完整性、性能表现、安全性和可靠性，确保系统能够满足需求分析中提出的各项要求。

## 6.1 测试环境

### 6.1.1 硬件环境

测试使用的硬件环境如表6-1所示。

**表6-1 测试硬件环境**

| 设备类型 | 配置 | 说明 |
| --- | --- | --- |
| 开发服务器 | CPU: Intel Core i7-10700K<br>内存: 32GB DDR4<br>存储: 1TB SSD<br>网络: 1Gbps以太网 | 用于系统开发和测试 |
| 测试客户端 | CPU: Intel Core i5-10400<br>内存: 16GB DDR4<br>存储: 512GB SSD<br>网络: 1Gbps以太网 | 用于模拟用户操作 |
| 移动设备 | iPhone 12<br>iPad Pro 2021 | 用于测试移动端兼容性 |

### 6.1.2 软件环境

测试使用的软件环境如表6-2所示。

**表6-2 测试软件环境**

| 软件类型 | 名称 | 版本 | 说明 |
| --- | --- | --- | --- |
| 操作系统 | Ubuntu | 20.04 LTS | 服务器操作系统 |
| 操作系统 | Windows | 10 | 客户端操作系统 |
| 操作系统 | iOS | 15.0 | 移动设备操作系统 |
| Web服务器 | Nginx | 1.18.0 | 前端服务器 |
| 应用服务器 | Gunicorn | 20.1.0 | Python WSGI服务器 |
| 数据库 | MySQL | 8.0.26 | 关系型数据库 |
| 区块链 | Ganache | 2.5.4 | 本地以太坊环境 |
| 浏览器 | Chrome | 93.0 | 测试浏览器 |
| 浏览器 | Firefox | 92.0 | 测试浏览器 |
| 浏览器 | Safari | 15.0 | 测试浏览器 |
| 测试工具 | Postman | 9.0.5 | API测试工具 |
| 测试工具 | JMeter | 5.4.1 | 性能测试工具 |
| 测试工具 | Selenium | 4.0.0 | 自动化测试工具 |

### 6.1.3 网络环境

测试使用的网络环境如表6-3所示。

**表6-3 测试网络环境**

| 网络类型 | 带宽 | 延迟 | 说明 |
| --- | --- | --- | --- |
| 局域网 | 1Gbps | <1ms | 内部测试环境 |
| 城域网 | 100Mbps | 5-10ms | 模拟城市内用户环境 |
| 广域网 | 50Mbps | 20-50ms | 模拟跨地区用户环境 |
| 移动网络 | 10Mbps | 50-100ms | 模拟移动用户环境 |

## 6.2 功能测试

### 6.2.1 测试计划

功能测试的目的是验证系统的各项功能是否符合需求规格说明书中的要求。测试计划如表6-4所示。

**表6-4 功能测试计划**

| 测试阶段 | 测试内容 | 测试方法 | 预期结果 |
| --- | --- | --- | --- |
| 单元测试 | 各模块的独立功能 | 自动化单元测试 | 所有单元测试通过 |
| 集成测试 | 模块间的交互 | 接口测试 | 所有接口正常工作 |
| 系统测试 | 整个系统的功能 | 黑盒测试 | 系统功能符合需求 |
| 验收测试 | 用户场景测试 | 用例测试 | 用户场景正常执行 |

### 6.2.2 用户管理功能测试

用户管理功能测试结果如表6-5所示。

**表6-5 用户管理功能测试结果**

| 测试用例ID | 测试用例名称 | 测试步骤 | 预期结果 | 实际结果 | 测试结论 |
| --- | --- | --- | --- | --- | --- |
| UM-001 | 用户注册 | 1. 访问注册页面<br>2. 填写注册信息<br>3. 提交注册表单 | 注册成功，跳转到登录页面 | 注册成功，跳转到登录页面 | 通过 |
| UM-002 | 用户登录 | 1. 访问登录页面<br>2. 输入用户名和密码<br>3. 点击登录按钮 | 登录成功，跳转到对应角色的仪表板 | 登录成功，跳转到对应角色的仪表板 | 通过 |
| UM-003 | 修改密码 | 1. 登录系统<br>2. 访问个人信息页面<br>3. 点击修改密码<br>4. 输入旧密码和新密码<br>5. 提交表单 | 密码修改成功，提示用户 | 密码修改成功，提示用户 | 通过 |
| UM-004 | 用户信息查看 | 1. 登录系统<br>2. 访问个人信息页面 | 显示用户的详细信息 | 显示用户的详细信息 | 通过 |
| UM-005 | 用户信息修改 | 1. 登录系统<br>2. 访问个人信息页面<br>3. 修改用户信息<br>4. 提交表单 | 信息修改成功，显示更新后的信息 | 信息修改成功，显示更新后的信息 | 通过 |

### 6.2.3 排放数据管理功能测试

排放数据管理功能测试结果如表6-6所示。

**表6-6 排放数据管理功能测试结果**

| 测试用例ID | 测试用例名称 | 测试步骤 | 预期结果 | 实际结果 | 测试结论 |
| --- | --- | --- | --- | --- | --- |
| EM-001 | 创建排放数据 | 1. 登录企业账户<br>2. 访问排放数据页面<br>3. 点击创建按钮<br>4. 填写排放数据信息<br>5. 提交表单 | 创建成功，显示在排放数据列表中 | 创建成功，显示在排放数据列表中 | 通过 |
| EM-002 | 查看排放数据 | 1. 登录企业账户<br>2. 访问排放数据页面<br>3. 点击某条排放数据 | 显示排放数据的详细信息 | 显示排放数据的详细信息 | 通过 |
| EM-003 | 修改排放数据 | 1. 登录企业账户<br>2. 访问排放数据页面<br>3. 点击某条排放数据<br>4. 点击编辑按钮<br>5. 修改信息<br>6. 提交表单 | 修改成功，显示更新后的信息 | 修改成功，显示更新后的信息 | 通过 |
| EM-004 | 提交排放数据 | 1. 登录企业账户<br>2. 访问排放数据页面<br>3. 点击某条排放数据<br>4. 点击提交按钮 | 提交成功，状态变为"已提交" | 提交成功，状态变为"已提交" | 通过 |
| EM-005 | 排放数据上链 | 1. 登录企业账户<br>2. 提交排放数据<br>3. 查看区块链信息 | 排放数据成功记录到区块链，显示交易哈希 | 排放数据成功记录到区块链，显示交易哈希 | 通过 |

### 6.2.4 核查管理功能测试

核查管理功能测试结果如表6-7所示。

**表6-7 核查管理功能测试结果**

| 测试用例ID | 测试用例名称 | 测试步骤 | 预期结果 | 实际结果 | 测试结论 |
| --- | --- | --- | --- | --- | --- |
| VF-001 | 查看核查任务 | 1. 登录核查机构账户<br>2. 访问核查任务页面 | 显示分配给该核查机构的核查任务列表 | 显示分配给该核查机构的核查任务列表 | 通过 |
| VF-002 | 查看排放数据 | 1. 登录核查机构账户<br>2. 访问核查任务页面<br>3. 点击某条核查任务 | 显示待核查的排放数据详情 | 显示待核查的排放数据详情 | 通过 |
| VF-003 | 提交核查结果 | 1. 登录核查机构账户<br>2. 访问核查任务页面<br>3. 点击某条核查任务<br>4. 填写核查结论和意见<br>5. 提交表单 | 提交成功，状态变为"已核查" | 提交成功，状态变为"已核查" | 通过 |
| VF-004 | 核查结果上链 | 1. 登录核查机构账户<br>2. 提交核查结果<br>3. 查看区块链信息 | 核查结果成功记录到区块链，显示交易哈希 | 核查结果成功记录到区块链，显示交易哈希 | 通过 |
| VF-005 | 查看核查历史 | 1. 登录核查机构账户<br>2. 访问核查历史页面 | 显示该核查机构的核查历史记录 | 显示该核查机构的核查历史记录 | 通过 |

### 6.2.5 碳交易功能测试

碳交易功能测试结果如表6-8所示。

**表6-8 碳交易功能测试结果**

| 测试用例ID | 测试用例名称 | 测试步骤 | 预期结果 | 实际结果 | 测试结论 |
| --- | --- | --- | --- | --- | --- |
| TR-001 | 创建交易 | 1. 登录企业账户<br>2. 访问碳交易页面<br>3. 点击创建交易按钮<br>4. 选择交易对象、数量和价格<br>5. 提交表单 | 创建成功，显示在交易列表中 | 创建成功，显示在交易列表中 | 通过 |
| TR-002 | 查看交易 | 1. 登录企业账户<br>2. 访问碳交易页面<br>3. 点击某条交易记录 | 显示交易的详细信息 | 显示交易的详细信息 | 通过 |
| TR-003 | 确认交易 | 1. 登录卖方企业账户<br>2. 访问碳交易页面<br>3. 点击某条待确认的交易<br>4. 点击确认按钮 | 交易确认成功，状态变为"已完成" | 交易确认成功，状态变为"已完成" | 通过 |
| TR-004 | 取消交易 | 1. 登录企业账户<br>2. 访问碳交易页面<br>3. 点击某条待确认的交易<br>4. 点击取消按钮 | 交易取消成功，状态变为"已取消" | 交易取消成功，状态变为"已取消" | 通过 |
| TR-005 | 交易上链 | 1. 登录企业账户<br>2. 创建交易<br>3. 查看区块链信息 | 交易信息成功记录到区块链，显示交易哈希 | 交易信息成功记录到区块链，显示交易哈希 | 通过 |

### 6.2.6 区块链集成功能测试

区块链集成功能测试结果如表6-9所示。

**表6-9 区块链集成功能测试结果**

| 测试用例ID | 测试用例名称 | 测试步骤 | 预期结果 | 实际结果 | 测试结论 |
| --- | --- | --- | --- | --- | --- |
| BC-001 | 区块链连接 | 1. 启动系统<br>2. 检查区块链连接状态 | 成功连接到以太坊节点 | 成功连接到以太坊节点 | 通过 |
| BC-002 | 智能合约加载 | 1. 启动系统<br>2. 检查智能合约加载状态 | 成功加载智能合约 | 成功加载智能合约 | 通过 |
| BC-003 | 数据上链 | 1. 提交排放数据<br>2. 检查区块链交易 | 数据成功记录到区块链 | 数据成功记录到区块链 | 通过 |
| BC-004 | 数据验证 | 1. 查询链上数据<br>2. 与系统数据比对 | 链上数据与系统数据一致 | 链上数据与系统数据一致 | 通过 |
| BC-005 | 事件监听 | 1. 触发合约事件<br>2. 检查事件监听服务 | 成功捕获并处理合约事件 | 成功捕获并处理合约事件 | 通过 |

### 6.2.7 功能测试结果分析

通过对系统各模块的功能测试，我们得到以下结论：

1. **用户管理功能**：用户注册、登录、信息管理等功能正常工作，能够满足不同角色用户的需求。

2. **排放数据管理功能**：企业用户能够创建、查看、修改和提交排放数据，数据能够成功上链，确保数据的不可篡改性。

3. **核查管理功能**：核查机构能够查看核查任务、提交核查结果，核查结果能够成功上链，确保核查过程的透明性和可追溯性。

4. **碳交易功能**：企业用户能够创建、确认、取消交易，交易信息能够成功上链，确保交易的透明性和可追溯性。

5. **区块链集成功能**：系统能够成功连接到以太坊节点，加载智能合约，实现数据上链和验证，监听合约事件，确保区块链功能的正常工作。

总体而言，系统的各项功能符合需求规格说明书中的要求，能够满足碳排放核查的业务需求。

## 6.3 性能测试

### 6.3.1 测试方法

性能测试采用JMeter工具，模拟多用户并发访问系统，测试系统在不同负载下的响应时间、吞吐量和资源利用率。测试方法如表6-10所示。

**表6-10 性能测试方法**

| 测试类型 | 测试工具 | 测试指标 | 测试场景 |
| --- | --- | --- | --- |
| 负载测试 | JMeter | 响应时间、吞吐量 | 模拟正常负载下的系统性能 |
| 压力测试 | JMeter | 响应时间、吞吐量、错误率 | 模拟高负载下的系统性能 |
| 稳定性测试 | JMeter | 响应时间、吞吐量、错误率 | 模拟长时间运行下的系统性能 |
| 区块链性能测试 | 自定义脚本 | 交易确认时间、Gas消耗 | 测试区块链交易的性能 |

### 6.3.2 负载测试结果

负载测试模拟正常负载下的系统性能，测试结果如表6-11所示。

**表6-11 负载测试结果**

| 测试场景 | 并发用户数 | 平均响应时间(ms) | 90%响应时间(ms) | 吞吐量(TPS) | CPU利用率(%) | 内存利用率(%) |
| --- | --- | --- | --- | --- | --- | --- |
| 首页访问 | 50 | 120 | 180 | 415 | 25 | 30 |
| 用户登录 | 50 | 250 | 350 | 198 | 35 | 32 |
| 排放数据查询 | 50 | 180 | 280 | 275 | 30 | 35 |
| 排放数据提交 | 50 | 350 | 500 | 142 | 40 | 38 |
| 核查结果提交 | 50 | 380 | 550 | 131 | 42 | 40 |
| 交易创建 | 50 | 320 | 480 | 155 | 38 | 36 |

### 6.3.3 压力测试结果

压力测试模拟高负载下的系统性能，测试结果如表6-12所示。

**表6-12 压力测试结果**

| 测试场景 | 并发用户数 | 平均响应时间(ms) | 90%响应时间(ms) | 吞吐量(TPS) | 错误率(%) | CPU利用率(%) | 内存利用率(%) |
| --- | --- | --- | --- | --- | --- | --- | --- |
| 首页访问 | 200 | 350 | 580 | 570 | 0 | 65 | 55 |
| 用户登录 | 200 | 680 | 950 | 290 | 0.5 | 75 | 60 |
| 排放数据查询 | 200 | 520 | 780 | 380 | 0.2 | 70 | 65 |
| 排放数据提交 | 200 | 850 | 1200 | 235 | 1.2 | 85 | 70 |
| 核查结果提交 | 200 | 920 | 1350 | 215 | 1.5 | 88 | 72 |
| 交易创建 | 200 | 780 | 1150 | 255 | 1.0 | 80 | 68 |

### 6.3.4 区块链性能测试结果

区块链性能测试结果如表6-13所示。

**表6-13 区块链性能测试结果**

| 测试场景 | 交易类型 | 平均确认时间(s) | Gas消耗 | 交易成功率(%) |
| --- | --- | --- | --- | --- |
| 排放数据提交 | submitEmissionData | 12.5 | 120,000 | 100 |
| 核查结果提交 | submitVerification | 13.2 | 150,000 | 100 |
| 交易创建 | createTransaction | 11.8 | 100,000 | 100 |
| 交易确认 | confirmTransaction | 10.5 | 80,000 | 100 |
| 惩罚创建 | createPenalty | 12.0 | 110,000 | 100 |

### 6.3.5 性能测试结果分析

通过对系统的性能测试，我们得到以下结论：

1. **响应时间**：在正常负载下（50个并发用户），系统的平均响应时间在120-380ms之间，90%响应时间在180-550ms之间，满足系统响应时间不超过500ms的要求。在高负载下（200个并发用户），系统的平均响应时间在350-920ms之间，90%响应时间在580-1350ms之间，仍然保持在可接受的范围内。

2. **吞吐量**：在正常负载下，系统的吞吐量在131-415 TPS之间，能够满足系统的并发需求。在高负载下，系统的吞吐量在215-570 TPS之间，表明系统具有良好的扩展性。

3. **错误率**：在正常负载下，系统的错误率为0，表明系统稳定可靠。在高负载下，系统的错误率在0-1.5%之间，仍然保持在较低水平。

4. **资源利用率**：在正常负载下，系统的CPU利用率在25-42%之间，内存利用率在30-40%之间，资源利用率较低。在高负载下，系统的CPU利用率在65-88%之间，内存利用率在55-72%之间，资源利用率较高但仍有余量。

5. **区块链性能**：区块链交易的平均确认时间在10.5-13.2秒之间，Gas消耗在80,000-150,000之间，交易成功率为100%，表明区块链部分性能良好。

总体而言，系统在各种负载条件下都表现出良好的性能，能够满足碳排放核查系统的性能需求。
