<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放管理系统 - 系统报告</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/unified-nav.css') }}">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background: linear-gradient(to right, #1B5E20, #388E3C);
            color: white;
            padding: 20px 0;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-name {
            margin-right: 15px;
        }
        .logout-btn {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 30px;
            cursor: pointer;
            text-decoration: none;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .logout-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        nav {
            background: linear-gradient(to right, #2E7D32, #43A047);
            padding: 10px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
        }
        .nav-item {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            margin-right: 5px;
            border-radius: 30px;
            transition: all 0.3s ease;
        }
        .nav-item:hover, .nav-item.active {
            background: linear-gradient(to right, #1B5E20, #2E7D32);
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        .card {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            transform: translateY(-5px);
        }
        .card-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        .btn-primary {
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            color: white;
        }
        .btn-success {
            background: linear-gradient(to right, #2ecc71, #27ae60);
            color: white;
        }
        .btn-danger {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
        }
        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        th, td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        th {
            background: linear-gradient(to right, #43A047, #66BB6A);
            color: white;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 14px;
            letter-spacing: 0.5px;
        }
        tr:last-child td {
            border-bottom: none;
        }
        tr:nth-child(even) {
            background-color: rgba(0,0,0,0.02);
        }
        tr:hover {
            background-color: rgba(76, 175, 80, 0.05);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 30px;
            font-size: 14px;
            box-sizing: border-box;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 2px 10px rgba(76, 175, 80, 0.2);
        }
        textarea {
            border-radius: 15px;
            min-height: 100px;
        }
        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
        }
        .form-col {
            flex: 1;
        }
        .report-templates {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        .report-template {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 25px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,0.05);
        }
        .report-template:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        .report-template.selected {
            border: 2px solid #4CAF50;
            background: linear-gradient(to bottom, #f1f8e9, #e8f5e9);
            box-shadow: 0 10px 30px rgba(76, 175, 80, 0.2);
        }
        .report-icon {
            font-size: 60px;
            margin-bottom: 15px;
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .report-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .report-description {
            font-size: 12px;
            color: #7f8c8d;
        }
        .report-format {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            background: linear-gradient(to right, #f1f8e9, #dcedc8);
            margin-top: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            font-weight: bold;
            color: #2E7D32;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo">碳排放管理系统</div>
            <div class="user-info">
                <span class="user-name">系统管理员</span>
                <a href="/login" class="logout-btn">退出登录</a>
            </div>
        </div>
    </header>

    <div class="demo-nav">
        <div class="nav-content">
            <a href="/admin" class="nav-item"><i class="fas fa-tachometer-alt mr-2"></i>系统概览</a>
            <a href="/admin_users" class="nav-item"><i class="fas fa-users mr-2"></i>用户管理</a>
            <a href="/admin_quotas" class="nav-item"><i class="fas fa-chart-pie mr-2"></i>配额管理</a>
            <a href="/admin_verifications" class="nav-item"><i class="fas fa-clipboard-check mr-2"></i>核查管理</a>
            <a href="/admin_transactions" class="nav-item"><i class="fas fa-exchange-alt mr-2"></i>交易管理</a>
            <a href="/admin_reports" class="nav-item active"><i class="fas fa-file-alt mr-2"></i>系统报告</a>
            <a href="/admin_settings" class="nav-item"><i class="fas fa-cog mr-2"></i>系统配置</a>
            <a href="/admin_logs" class="nav-item"><i class="fas fa-list-alt mr-2"></i>日志查看</a>
            <a href="/admin_blockchain_config" class="nav-item"><i class="fas fa-link mr-2"></i>区块链配置</a>
        </div>
    </div>

    <div class="container">
        <h1>系统报告生成</h1>

        <div class="card">
            <div class="card-title">选择报告模板</div>

            <div class="report-templates">
                <div class="report-template selected" data-type="system">
                    <div class="report-icon">📊</div>
                    <div class="report-title">系统运行报告</div>
                    <div class="report-description">包含系统运行状态、用户活跃度和数据处理情况</div>
                    <div class="report-format">PDF, Word</div>
                </div>

                <div class="report-template" data-type="user">
                    <div class="report-icon">👥</div>
                    <div class="report-title">用户活动报告</div>
                    <div class="report-description">包含用户登录、操作和数据提交情况</div>
                    <div class="report-format">PDF, Excel</div>
                </div>

                <div class="report-template" data-type="transaction">
                    <div class="report-icon">💹</div>
                    <div class="report-title">交易统计报告</div>
                    <div class="report-description">包含交易量、价格趋势和市场活跃度</div>
                    <div class="report-format">PDF, PowerPoint</div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-title">报告参数</div>

            <form id="report-form">
                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label for="report-title">报告标题</label>
                            <input type="text" id="report-title" name="title" value="2023年第一季度系统运行报告">
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label for="report-format">报告格式</label>
                            <select id="report-format" name="format">
                                <option value="pdf">PDF</option>
                                <option value="word">Word</option>
                                <option value="excel">Excel</option>
                                <option value="ppt">PowerPoint</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label for="period-start">报告期间开始</label>
                            <input type="date" id="period-start" name="period_start" value="2023-01-01">
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label for="period-end">报告期间结束</label>
                            <input type="date" id="period-end" name="period_end" value="2023-03-31">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="report-sections">报告章节</label>
                    <select id="report-sections" name="sections" multiple size="5">
                        <option value="summary" selected>摘要</option>
                        <option value="system_status" selected>系统状态</option>
                        <option value="user_activity" selected>用户活动</option>
                        <option value="data_processing" selected>数据处理</option>
                        <option value="security" selected>安全状况</option>
                        <option value="blockchain" selected>区块链同步</option>
                        <option value="recommendations" selected>改进建议</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="report-remarks">备注</label>
                    <textarea id="report-remarks" name="remarks" placeholder="请输入备注信息"></textarea>
                </div>

                <div style="text-align: right; margin-top: 20px;">
                    <button type="button" class="btn btn-primary" id="preview-report">预览报告</button>
                    <button type="button" class="btn btn-success" id="generate-report">生成报告</button>
                </div>
            </form>
        </div>

        <div class="card">
            <div class="card-title">历史报告</div>

            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>标题</th>
                        <th>类型</th>
                        <th>格式</th>
                        <th>报告期间</th>
                        <th>生成时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>3001</td>
                        <td>2022年第四季度系统运行报告</td>
                        <td>系统运行报告</td>
                        <td>PDF</td>
                        <td>2022-10-01 至 2022-12-31</td>
                        <td>2023-01-10</td>
                        <td>
                            <a href="/reports/system_report.html" target="_blank" class="btn btn-primary">查看</a>
                            <a href="#" class="btn btn-success">下载</a>
                        </td>
                    </tr>
                    <tr>
                        <td>3002</td>
                        <td>2022年度用户活动报告</td>
                        <td>用户活动报告</td>
                        <td>EXCEL</td>
                        <td>2022-01-01 至 2022-12-31</td>
                        <td>2023-01-15</td>
                        <td>
                            <a href="/reports/system_report.html" target="_blank" class="btn btn-primary">查看</a>
                            <a href="#" class="btn btn-success">下载</a>
                        </td>
                    </tr>
                    <tr>
                        <td>3003</td>
                        <td>2022年度交易统计报告</td>
                        <td>交易统计报告</td>
                        <td>PPT</td>
                        <td>2022-01-01 至 2022-12-31</td>
                        <td>2023-01-20</td>
                        <td>
                            <a href="/reports/system_report.html" target="_blank" class="btn btn-primary">查看</a>
                            <a href="#" class="btn btn-success">下载</a>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 报告模板选择
            const reportTemplates = document.querySelectorAll('.report-template');
            reportTemplates.forEach(template => {
                template.addEventListener('click', function() {
                    reportTemplates.forEach(t => t.classList.remove('selected'));
                    this.classList.add('selected');

                    // 根据选择的模板类型更新表单
                    const type = this.getAttribute('data-type');
                    updateFormByType(type);
                });
            });

            // 预览报告按钮点击事件
            document.getElementById('preview-report').addEventListener('click', function() {
                const type = document.querySelector('.report-template.selected').getAttribute('data-type');
                const title = document.getElementById('report-title').value;
                const format = document.getElementById('report-format').value;

                // 根据报告类型打开不同的预览页面
                let previewUrl = '';

                switch(type) {
                    case 'system':
                        previewUrl = '/reports/system_report.html';
                        break;
                    case 'user':
                        previewUrl = '/reports/system_report.html';
                        break;
                    case 'transaction':
                        previewUrl = '/reports/system_report.html';
                        break;
                }

                // 在新窗口中打开预览
                if (previewUrl) {
                    window.open(previewUrl, '_blank');
                }
            });

            // 生成报告按钮点击事件
            document.getElementById('generate-report').addEventListener('click', function() {
                const type = document.querySelector('.report-template.selected').getAttribute('data-type');
                const title = document.getElementById('report-title').value;
                const format = document.getElementById('report-format').value;
                const periodStart = document.getElementById('period-start').value;
                const periodEnd = document.getElementById('period-end').value;

                // 获取报告类型的中文名称
                let typeText = '';
                switch(type) {
                    case 'system':
                        typeText = '系统运行报告';
                        break;
                    case 'user':
                        typeText = '用户活动报告';
                        break;
                    case 'transaction':
                        typeText = '交易统计报告';
                        break;
                }

                // 在实际应用中，这里应该发送AJAX请求生成报告
                // 这里只是演示，显示一个提示
                alert(`正在生成${typeText}: ${title}，格式: ${format}，报告期间: ${periodStart} 至 ${periodEnd}`);

                // 模拟生成报告后添加到历史报告列表
                setTimeout(function() {
                    addReportToHistory(title, typeText, format, periodStart, periodEnd);
                }, 1000);
            });

            // 根据报告类型更新表单
            function updateFormByType(type) {
                const titleInput = document.getElementById('report-title');
                const formatSelect = document.getElementById('report-format');

                switch(type) {
                    case 'system':
                        titleInput.value = '2023年第一季度系统运行报告';
                        formatSelect.value = 'pdf';
                        break;
                    case 'user':
                        titleInput.value = '2023年第一季度用户活动报告';
                        formatSelect.value = 'excel';
                        break;
                    case 'transaction':
                        titleInput.value = '2023年第一季度交易统计报告';
                        formatSelect.value = 'ppt';
                        break;
                }
            }

            // 添加报告到历史记录
            function addReportToHistory(title, type, format, startDate, endDate) {
                const tbody = document.querySelector('.card:last-child table tbody');
                const now = new Date();
                const dateStr = `${now.getFullYear()}-${(now.getMonth()+1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')}`;

                // 创建新行
                const newRow = document.createElement('tr');
                newRow.innerHTML = `
                    <td>${3004 + tbody.children.length}</td>
                    <td>${title}</td>
                    <td>${type}</td>
                    <td>${format.toUpperCase()}</td>
                    <td>${startDate} 至 ${endDate}</td>
                    <td>${dateStr}</td>
                    <td>
                        <a href="#" class="btn btn-primary view-report">查看</a>
                        <a href="#" class="btn btn-success">下载</a>
                    </td>
                `;

                // 添加到表格顶部
                if (tbody.firstChild) {
                    tbody.insertBefore(newRow, tbody.firstChild);
                } else {
                    tbody.appendChild(newRow);
                }

                // 为新添加的查看按钮添加事件
                const viewBtn = newRow.querySelector('.view-report');
                viewBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    window.open('/reports/system_report.html', '_blank');
                });

                // 显示成功提示
                alert(`报告"${title}"已成功生成！`);
            }

            // 为历史报告中的查看按钮添加事件
            document.querySelectorAll('.card:last-child table tbody .btn-primary').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    window.open('/reports/system_report.html', '_blank');
                });
            });
        });
    </script>
</body>
</html>
