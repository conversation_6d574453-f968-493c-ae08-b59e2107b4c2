<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放管理系统 - 系统配置</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/unified-nav.css') }}">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background: linear-gradient(to right, #1B5E20, #388E3C);
            color: white;
            padding: 20px 0;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-name {
            margin-right: 15px;
        }
        .logout-btn {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 30px;
            cursor: pointer;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .logout-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        nav {
            background: linear-gradient(to right, #2E7D32, #43A047);
            padding: 10px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
        }
        .nav-item {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            margin-right: 5px;
            border-radius: 30px;
            transition: all 0.3s ease;
        }
        .nav-item:hover, .nav-item.active {
            background: linear-gradient(to right, #1B5E20, #2E7D32);
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        .card {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            transform: translateY(-5px);
        }
        .card-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #2c3e50;
        }
        input, select, textarea {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 30px;
            font-size: 14px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            box-sizing: border-box;
        }
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 2px 10px rgba(76, 175, 80, 0.2);
        }
        textarea {
            border-radius: 15px;
            min-height: 100px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        .btn-primary {
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            color: white;
        }
        .btn-success {
            background: linear-gradient(to right, #2ecc71, #27ae60);
            color: white;
        }
        .settings-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #4CAF50;
        }
        input:focus + .slider {
            box-shadow: 0 0 1px #4CAF50;
        }
        input:checked + .slider:before {
            transform: translateX(26px);
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo">碳排放管理系统</div>
            <div class="user-info">
                <span class="user-name">管理员</span>
                <a href="/login" class="logout-btn">退出登录</a>
            </div>
        </div>
    </header>

    <div class="demo-nav">
        <div class="nav-content">
            <a href="/admin" class="nav-item"><i class="fas fa-tachometer-alt mr-2"></i>系统概览</a>
            <a href="/admin_users" class="nav-item"><i class="fas fa-users mr-2"></i>用户管理</a>
            <a href="/admin_quotas" class="nav-item"><i class="fas fa-chart-pie mr-2"></i>配额管理</a>
            <a href="/admin_verifications" class="nav-item"><i class="fas fa-clipboard-check mr-2"></i>核查管理</a>
            <a href="/admin_transactions" class="nav-item"><i class="fas fa-exchange-alt mr-2"></i>交易管理</a>
            <a href="/admin_reports" class="nav-item"><i class="fas fa-file-alt mr-2"></i>系统报告</a>
            <a href="/admin_settings" class="nav-item active"><i class="fas fa-cog mr-2"></i>系统配置</a>
            <a href="/admin_logs" class="nav-item"><i class="fas fa-list-alt mr-2"></i>日志查看</a>
            <a href="/admin_blockchain_config" class="nav-item"><i class="fas fa-link mr-2"></i>区块链配置</a>
        </div>
    </div>

    <div class="container">
        <h1>系统配置</h1>

        <div class="settings-grid">
            <div class="card">
                <div class="card-title">基本设置</div>
                <form>
                    <div class="form-group">
                        <label for="system-name">系统名称</label>
                        <input type="text" id="system-name" value="碳排放管理系统" />
                    </div>
                    <div class="form-group">
                        <label for="admin-email">管理员邮箱</label>
                        <input type="email" id="admin-email" value="<EMAIL>" />
                    </div>
                    <div class="form-group">
                        <label for="system-language">系统语言</label>
                        <select id="system-language">
                            <option value="zh-CN" selected>简体中文</option>
                            <option value="en-US">English</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="system-timezone">系统时区</label>
                        <select id="system-timezone">
                            <option value="Asia/Shanghai" selected>中国标准时间 (UTC+8)</option>
                            <option value="UTC">协调世界时 (UTC)</option>
                            <option value="America/New_York">美国东部时间 (UTC-5)</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary">保存设置</button>
                </form>
            </div>

            <div class="card">
                <div class="card-title">交易设置</div>
                <form>
                    <div class="form-group">
                        <label for="min-price">最低交易价格 (元/吨)</label>
                        <input type="number" id="min-price" value="50" />
                    </div>
                    <div class="form-group">
                        <label for="max-price">最高交易价格 (元/吨)</label>
                        <input type="number" id="max-price" value="200" />
                    </div>
                    <div class="form-group">
                        <label for="transaction-fee">交易手续费 (%)</label>
                        <input type="number" id="transaction-fee" value="2" step="0.1" />
                    </div>
                    <div class="form-group">
                        <label for="min-transaction">最小交易量 (吨)</label>
                        <input type="number" id="min-transaction" value="10" />
                    </div>
                    <button type="submit" class="btn btn-primary">保存设置</button>
                </form>
            </div>

            <div class="card">
                <div class="card-title">区块链设置</div>
                <div class="form-group">
                    <p>请前往<a href="/admin_blockchain_config" style="color: #4CAF50; font-weight: bold;">区块链配置页面</a>进行详细配置。</p>
                </div>
                <form>
                    <div class="form-group">
                        <label for="blockchain-url">区块链节点URL</label>
                        <input type="text" id="blockchain-url" value="https://example-blockchain.com/node" disabled />
                    </div>
                    <div class="form-group">
                        <label for="contract-address">智能合约地址</label>
                        <input type="text" id="contract-address" value="0x1234567890abcdef1234567890abcdef12345678" disabled />
                    </div>
                    <div class="form-group">
                        <label>区块链同步</label>
                        <div style="margin-top: 10px;">
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                            <span style="margin-left: 10px;">启用自动同步</span>
                        </div>
                    </div>
                    <a href="/admin_blockchain_config" class="btn btn-primary">前往区块链配置</a>
                </form>
            </div>

            <div class="card">
                <div class="card-title">通知设置</div>
                <form>
                    <div class="form-group">
                        <label>邮件通知</label>
                        <div style="margin-top: 10px;">
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                            <span style="margin-left: 10px;">启用邮件通知</span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="smtp-server">SMTP服务器</label>
                        <input type="text" id="smtp-server" value="smtp.example.com" />
                    </div>
                    <div class="form-group">
                        <label for="smtp-port">SMTP端口</label>
                        <input type="number" id="smtp-port" value="587" />
                    </div>
                    <div class="form-group">
                        <label for="smtp-username">SMTP用户名</label>
                        <input type="text" id="smtp-username" value="<EMAIL>" />
                    </div>
                    <div class="form-group">
                        <label for="smtp-password">SMTP密码</label>
                        <input type="password" id="smtp-password" value="********" />
                    </div>
                    <button type="submit" class="btn btn-primary">保存设置</button>
                </form>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 导航切换
            const currentPath = window.location.pathname;
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                if (item.getAttribute('href') === currentPath) {
                    navItems.forEach(i => i.classList.remove('active'));
                    item.classList.add('active');
                }
            });

            // 表单提交
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    alert('设置已保存');
                });
            });
        });
    </script>
</body>
</html>
