"""
测试区块链连接和基本功能
"""

import os
import sys
import json
import time
import logging
from web3 import Web3
from web3.middleware import geth_poa_middleware
from dotenv import load_dotenv

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('blockchain_test')

# 加载环境变量
load_dotenv()

def test_ganache_connection():
    """测试Ganache连接"""
    logger.info("测试Ganache连接...")
    
    # 获取以太坊节点URL
    ethereum_node_url = os.getenv('ETHEREUM_NODE_URL', 'http://127.0.0.1:8545')
    
    try:
        # 连接到以太坊节点
        w3 = Web3(Web3.HTTPProvider(ethereum_node_url))
        
        # 如果使用的是PoA共识的网络，需要添加这个中间件
        w3.middleware_onion.inject(geth_poa_middleware, layer=0)
        
        # 检查连接
        if w3.is_connected():
            logger.info(f"成功连接到Ganache节点: {ethereum_node_url}")
            logger.info(f"当前区块号: {w3.eth.block_number}")
            
            # 获取账户列表
            accounts = w3.eth.accounts
            logger.info(f"找到 {len(accounts)} 个账户:")
            
            for i, account in enumerate(accounts[:5]):  # 只显示前5个账户
                balance = w3.eth.get_balance(account)
                balance_eth = w3.from_wei(balance, 'ether')
                logger.info(f"账户 {i+1}: {account}, 余额: {balance_eth} ETH")
            
            return True, w3
        else:
            logger.error(f"无法连接到Ganache节点: {ethereum_node_url}")
            return False, None
    except Exception as e:
        logger.error(f"连接Ganache失败: {str(e)}")
        return False, None

def test_contract_deployment(w3):
    """测试智能合约部署"""
    logger.info("测试智能合约部署...")
    
    # 获取合约地址
    contract_address = os.getenv('CONTRACT_ADDRESS')
    
    if not contract_address or contract_address == '******************************************':
        logger.warning("未设置有效的CONTRACT_ADDRESS环境变量")
        logger.info("尝试部署新的智能合约...")
        
        try:
            # 获取管理员账户
            admin_private_key = os.getenv('ADMIN_PRIVATE_KEY')
            
            if not admin_private_key:
                logger.error("未设置ADMIN_PRIVATE_KEY环境变量，无法部署合约")
                return False, None
            
            # 运行部署脚本
            logger.info("运行部署脚本: python blockchain/deploy_contract.py")
            os.system("python blockchain/deploy_contract.py")
            
            # 重新加载环境变量
            load_dotenv()
            
            # 获取新的合约地址
            contract_address = os.getenv('CONTRACT_ADDRESS')
            
            if not contract_address or contract_address == '******************************************':
                logger.error("部署合约失败，未获取到有效的合约地址")
                return False, None
        except Exception as e:
            logger.error(f"部署合约失败: {str(e)}")
            return False, None
    
    try:
        # 加载合约ABI
        abi_path = 'blockchain/contracts/artifacts/CarbonEmission_metadata.json'
        
        if not os.path.exists(abi_path):
            logger.error(f"合约ABI文件不存在: {abi_path}")
            return False, None
        
        with open(abi_path, 'r') as f:
            contract_metadata = json.load(f)
            contract_abi = contract_metadata['output']['abi']
        
        # 创建合约实例
        contract = w3.eth.contract(
            address=w3.to_checksum_address(contract_address),
            abi=contract_abi
        )
        
        logger.info(f"成功加载智能合约: {contract_address}")
        
        # 尝试调用合约方法
        try:
            # 获取排放数据数量
            emission_data_count = contract.functions.emissionDataCount().call()
            logger.info(f"排放数据数量: {emission_data_count}")
            
            # 获取核查记录数量
            verification_record_count = contract.functions.verificationRecordCount().call()
            logger.info(f"核查记录数量: {verification_record_count}")
            
            # 获取交易数量
            transaction_count = contract.functions.transactionCount().call()
            logger.info(f"交易数量: {transaction_count}")
            
            # 获取惩罚记录数量
            penalty_count = contract.functions.penaltyCount().call()
            logger.info(f"惩罚记录数量: {penalty_count}")
            
            logger.info("合约方法调用测试通过")
        except Exception as e:
            logger.error(f"调用合约方法失败: {str(e)}")
            return False, contract
        
        return True, contract
    except Exception as e:
        logger.error(f"加载合约失败: {str(e)}")
        return False, None

def test_register_roles(w3, contract):
    """测试注册角色"""
    logger.info("测试注册角色...")
    
    # 获取管理员账户
    admin_private_key = os.getenv('ADMIN_PRIVATE_KEY')
    
    if not admin_private_key:
        logger.error("未设置ADMIN_PRIVATE_KEY环境变量，无法注册角色")
        return False
    
    try:
        # 导入账户
        from eth_account import Account
        admin_account = Account.from_key(admin_private_key)
        admin_address = admin_account.address
        
        logger.info(f"使用管理员账户: {admin_address}")
        
        # 获取企业和核查机构地址
        enterprise_addresses = [
            os.getenv('ENTERPRISE_1_ADDRESS'),
            os.getenv('ENTERPRISE_2_ADDRESS')
        ]
        enterprise_addresses = [addr for addr in enterprise_addresses if addr and addr != '******************************************']
        
        verifier_addresses = [
            os.getenv('VERIFIER_1_ADDRESS')
        ]
        verifier_addresses = [addr for addr in verifier_addresses if addr and addr != '******************************************']
        
        if not enterprise_addresses and not verifier_addresses:
            logger.warning("未设置企业和核查机构地址，使用Ganache账户")
            
            # 使用Ganache账户
            accounts = w3.eth.accounts
            
            if len(accounts) >= 4:
                enterprise_addresses = [accounts[1], accounts[2]]
                verifier_addresses = [accounts[3]]
            else:
                logger.error("Ganache账户数量不足，无法注册角色")
                return False
        
        # 注册管理员
        logger.info(f"注册管理员: {admin_address}")
        try:
            # 检查是否已经是管理员
            is_admin = contract.functions.isAdmin(admin_address).call()
            
            if is_admin:
                logger.info(f"账户 {admin_address} 已经是管理员")
            else:
                # 获取nonce
                nonce = w3.eth.get_transaction_count(admin_address)
                
                # 构建交易
                tx = contract.functions.registerAdmin(admin_address).build_transaction({
                    'from': admin_address,
                    'nonce': nonce,
                    'gas': 200000,
                    'gasPrice': w3.eth.gas_price
                })
                
                # 签名交易
                signed_tx = w3.eth.account.sign_transaction(tx, private_key=admin_private_key)
                
                # 发送交易
                tx_hash = w3.eth.send_raw_transaction(signed_tx.rawTransaction)
                
                # 等待交易被确认
                tx_receipt = w3.eth.wait_for_transaction_receipt(tx_hash)
                
                logger.info(f"管理员注册成功，交易哈希: {tx_hash.hex()}")
        except Exception as e:
            logger.error(f"注册管理员失败: {str(e)}")
        
        # 注册企业
        for i, enterprise_address in enumerate(enterprise_addresses):
            logger.info(f"注册企业 {i+1}: {enterprise_address}")
            try:
                # 检查是否已经是企业
                is_enterprise = contract.functions.isEnterprise(enterprise_address).call()
                
                if is_enterprise:
                    logger.info(f"账户 {enterprise_address} 已经是企业")
                else:
                    # 获取nonce
                    nonce = w3.eth.get_transaction_count(admin_address)
                    
                    # 构建交易
                    tx = contract.functions.registerEnterprise(w3.to_checksum_address(enterprise_address)).build_transaction({
                        'from': admin_address,
                        'nonce': nonce,
                        'gas': 200000,
                        'gasPrice': w3.eth.gas_price
                    })
                    
                    # 签名交易
                    signed_tx = w3.eth.account.sign_transaction(tx, private_key=admin_private_key)
                    
                    # 发送交易
                    tx_hash = w3.eth.send_raw_transaction(signed_tx.rawTransaction)
                    
                    # 等待交易被确认
                    tx_receipt = w3.eth.wait_for_transaction_receipt(tx_hash)
                    
                    logger.info(f"企业注册成功，交易哈希: {tx_hash.hex()}")
            except Exception as e:
                logger.error(f"注册企业失败: {str(e)}")
        
        # 注册核查机构
        for i, verifier_address in enumerate(verifier_addresses):
            logger.info(f"注册核查机构 {i+1}: {verifier_address}")
            try:
                # 检查是否已经是核查机构
                is_verifier = contract.functions.isVerifier(verifier_address).call()
                
                if is_verifier:
                    logger.info(f"账户 {verifier_address} 已经是核查机构")
                else:
                    # 获取nonce
                    nonce = w3.eth.get_transaction_count(admin_address)
                    
                    # 构建交易
                    tx = contract.functions.registerVerifier(w3.to_checksum_address(verifier_address)).build_transaction({
                        'from': admin_address,
                        'nonce': nonce,
                        'gas': 200000,
                        'gasPrice': w3.eth.gas_price
                    })
                    
                    # 签名交易
                    signed_tx = w3.eth.account.sign_transaction(tx, private_key=admin_private_key)
                    
                    # 发送交易
                    tx_hash = w3.eth.send_raw_transaction(signed_tx.rawTransaction)
                    
                    # 等待交易被确认
                    tx_receipt = w3.eth.wait_for_transaction_receipt(tx_hash)
                    
                    logger.info(f"核查机构注册成功，交易哈希: {tx_hash.hex()}")
            except Exception as e:
                logger.error(f"注册核查机构失败: {str(e)}")
        
        logger.info("角色注册测试完成")
        return True
    except Exception as e:
        logger.error(f"注册角色失败: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("开始测试区块链连接和基本功能...")
    
    # 测试Ganache连接
    success, w3 = test_ganache_connection()
    if not success:
        logger.error("Ganache连接测试失败，退出程序")
        sys.exit(1)
    
    # 测试智能合约部署
    success, contract = test_contract_deployment(w3)
    if not success:
        logger.error("智能合约部署测试失败，退出程序")
        sys.exit(1)
    
    # 测试注册角色
    success = test_register_roles(w3, contract)
    if not success:
        logger.error("注册角色测试失败，退出程序")
        sys.exit(1)
    
    logger.info("区块链连接和基本功能测试完成")
    print("\n区块链测试结果:")
    print("=" * 40)
    print("Ganache连接: 成功")
    print("智能合约部署: 成功")
    print("角色注册: 成功")
    print("=" * 40)
    print("\n现在您可以启动系统，测试与区块链的交互功能。")

if __name__ == "__main__":
    main()
