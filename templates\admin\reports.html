<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放核查系统 - 报告管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container">
            <a class="navbar-brand" href="/"><i class="fas fa-leaf me-2"></i>碳排放核查系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin"><i class="fas fa-tachometer-alt me-1"></i>仪表板</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/users"><i class="fas fa-users me-1"></i>用户管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/quotas"><i class="fas fa-chart-pie me-1"></i>配额管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/penalties"><i class="fas fa-exclamation-triangle me-1"></i>惩罚管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/admin/reports"><i class="fas fa-file-alt me-1"></i>报告管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/blockchain/config"><i class="fas fa-link me-1"></i>区块链配置</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>{{ current_user.get('username', '管理员') }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="/admin/profile"><i class="fas fa-id-card me-1"></i>个人资料</a></li>
                            <li><a class="dropdown-item" href="/admin/settings"><i class="fas fa-cog me-1"></i>系统设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-1"></i>退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mt-4">
        <!-- 消息提示 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-file-alt me-2"></i>报告管理</h1>
            <a href="/admin/reports/generate" class="btn btn-success">
                <i class="fas fa-plus me-1"></i>生成报告
            </a>
        </div>

        <!-- 报告统计 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">总报告数</h6>
                                <h2 class="card-text">{{ reports|length if reports else 0 }}</h2>
                            </div>
                            <i class="fas fa-file-alt fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">已发布</h6>
                                <h2 class="card-text">{{ reports|selectattr('status', 'equalto', '已发布')|list|length if reports else 0 }}</h2>
                            </div>
                            <i class="fas fa-check-circle fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">草稿</h6>
                                <h2 class="card-text">{{ reports|selectattr('status', 'equalto', '草稿')|list|length if reports else 0 }}</h2>
                            </div>
                            <i class="fas fa-edit fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">本月新增</h6>
                                <h2 class="card-text">{{ reports|length if reports else 0 }}</h2>
                            </div>
                            <i class="fas fa-calendar-alt fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 报告筛选 -->
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0"><i class="fas fa-filter me-2"></i>筛选报告</h5>
            </div>
            <div class="card-body">
                <form method="get" action="/admin/reports" class="row g-3">
                    <div class="col-md-3">
                        <label for="title" class="form-label">报告标题</label>
                        <input type="text" class="form-control" id="title" name="title" placeholder="输入报告标题">
                    </div>
                    <div class="col-md-3">
                        <label for="type" class="form-label">报告类型</label>
                        <select class="form-select" id="type" name="type">
                            <option value="">全部类型</option>
                            <option value="季度报告">季度报告</option>
                            <option value="年度报告">年度报告</option>
                            <option value="专题报告">专题报告</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">状态</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">全部状态</option>
                            <option value="草稿">草稿</option>
                            <option value="已发布">已发布</option>
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-1"></i>搜索
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 报告列表 -->
        <div class="card">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0"><i class="fas fa-list me-2"></i>报告列表</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>标题</th>
                                <th>类型</th>
                                <th>生成日期</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if reports %}
                                {% for report in reports %}
                                    <tr>
                                        <td>{{ report.id }}</td>
                                        <td>{{ report.title }}</td>
                                        <td>{{ report.type }}</td>
                                        <td>{{ report.generation_date }}</td>
                                        <td>
                                            {% if report.status == '草稿' %}
                                                <span class="badge bg-warning">草稿</span>
                                            {% elif report.status == '已发布' %}
                                                <span class="badge bg-success">已发布</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="/admin/reports/{{ report.id }}" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="/admin/reports/{{ report.id }}/edit" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="/admin/reports/{{ report.id }}/download" class="btn btn-sm btn-success">
                                                    <i class="fas fa-download"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ report.id }}">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                            
                                            <!-- 删除确认模态框 -->
                                            <div class="modal fade" id="deleteModal{{ report.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ report.id }}" aria-hidden="true">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title" id="deleteModalLabel{{ report.id }}">确认删除</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            确定要删除报告 <strong>{{ report.title }}</strong> 吗？此操作不可逆。
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                                            <form action="/admin/reports/{{ report.id }}/delete" method="post" style="display: inline;">
                                                                <button type="submit" class="btn btn-danger">确认删除</button>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="6" class="text-center">暂无报告数据</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1" aria-disabled="true">上一页</a>
                        </li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                        <li class="page-item">
                            <a class="page-link" href="#">下一页</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">© 2025 碳排放核查系统 | 基于区块链技术</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
