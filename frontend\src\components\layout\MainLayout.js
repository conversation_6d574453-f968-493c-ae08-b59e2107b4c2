import React from 'react';
import { Outlet, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import '../../styles/layout/MainLayout.css';

const MainLayout = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  return (
    <div className="main-layout">
      <nav className="main-nav">
        <div className="nav-brand">
          <img src="/logo.png" alt="Logo" className="nav-logo" />
          <span className="nav-title">碳排放管理系统</span>
        </div>
        <div className="nav-menu">
          <button
            className="nav-item"
            onClick={() => navigate('/dashboard')}
          >
            仪表板
          </button>
          <button
            className="nav-item"
            onClick={() => navigate('/emissions')}
          >
            排放管理
          </button>
          <button
            className="nav-item"
            onClick={() => navigate('/trading')}
          >
            碳交易
          </button>
          <button
            className="nav-item"
            onClick={() => navigate('/calculator')}
          >
            碳足迹计算
          </button>
          <button
            className="nav-item"
            onClick={() => navigate('/prediction')}
          >
            预测分析
          </button>
          <button
            className="nav-item"
            onClick={() => navigate('/reports')}
          >
            报告管理
          </button>
          {user?.role === 'admin' && (
            <button
              className="nav-item"
              onClick={() => navigate('/admin')}
            >
              系统管理
            </button>
          )}
        </div>
        <div className="nav-user">
          <span className="user-name">{user?.username}</span>
          <button className="logout-btn" onClick={handleLogout}>
            退出
          </button>
        </div>
      </nav>
      <main className="main-content">
        <Outlet />
      </main>
    </div>
  );
};

export default MainLayout;