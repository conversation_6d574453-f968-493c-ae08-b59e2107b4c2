.report-management {
  padding: 20px;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.report-header h2 {
  margin: 0;
  color: #333;
}

.report-actions {
  display: flex;
  gap: 10px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 5px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn-primary {
  background-color: #667eea;
  color: white;
}

.btn-primary:hover {
  background-color: #5a6fd6;
}

.btn-secondary {
  background-color: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background-color: #4b5563;
}

.btn-danger {
  background-color: #ef4444;
  color: white;
}

.btn-danger:hover {
  background-color: #dc2626;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.report-filters {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.search-box {
  flex: 1;
}

.filter-box {
  width: 200px;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.form-control:focus {
  border-color: #667eea;
  outline: none;
}

.report-table {
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 20px;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #444;
  cursor: pointer;
  user-select: none;
}

th:hover {
  background-color: #f3f4f6;
}

.sort-icon {
  margin-left: 5px;
  color: #667eea;
}

.type-tag {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.type-tag.daily {
  background-color: #e9ecef;
  color: #495057;
}

.type-tag.weekly {
  background-color: #dbeafe;
  color: #1e40af;
}

.type-tag.monthly {
  background-color: #fef3c7;
  color: #92400e;
}

.type-tag.custom {
  background-color: #f3e8ff;
  color: #6b21a8;
}

.status-tag {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-tag.active {
  background-color: #dcfce7;
  color: #166534;
}

.status-tag.paused {
  background-color: #fef3c7;
  color: #92400e;
}

.status-tag.completed {
  background-color: #dbeafe;
  color: #1e40af;
}

.status-tag.failed {
  background-color: #fee2e2;
  color: #991b1b;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
}

.page-info {
  color: #666;
  font-size: 14px;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 10px;
  padding: 20px;
  width: 100%;
  max-width: 500px;
}

.modal-content h3 {
  margin: 0 0 20px 0;
  color: #333;
}

.create-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.form-group label {
  font-weight: 500;
  color: #444;
}

.schedule-settings {
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 5px;
}

.schedule-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.day-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.day-checkbox {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
}

.day-checkbox input[type="checkbox"] {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: 18px;
  color: #666;
}

.alert {
  padding: 12px;
  border-radius: 5px;
  margin-bottom: 20px;
}

.alert-danger {
  background-color: #fee2e2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .report-management {
    padding: 15px;
  }

  .report-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .report-actions {
    flex-direction: column;
  }

  .report-filters {
    flex-direction: column;
  }

  .filter-box {
    width: 100%;
  }

  .report-table {
    overflow-x: auto;
  }

  th, td {
    padding: 8px;
    white-space: nowrap;
  }

  .action-buttons {
    flex-direction: column;
  }

  .modal-content {
    margin: 15px;
  }

  .day-selector {
    flex-direction: column;
  }
} 