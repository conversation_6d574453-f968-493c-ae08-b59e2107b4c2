"""
区块链事件监听服务
监听区块链上的事件，并更新数据库
"""

import os
import time
import threading
import json
import logging
from web3 import Web3
from web3.middleware import geth_poa_middleware
from dotenv import load_dotenv

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('blockchain_event_listener')

# 加载环境变量
load_dotenv()

class BlockchainEventListener:
    """区块链事件监听服务"""

    def __init__(self, app=None):
        """初始化事件监听服务"""
        self.app = app
        self.web3 = None
        self.contract = None
        self.running = False
        self.thread = None
        self.last_block = 0

        if app is not None:
            self.init_app(app)

    def init_app(self, app):
        """初始化应用"""
        self.app = app

        # 连接到以太坊节点
        ethereum_node_url = os.getenv('ETHEREUM_NODE_URL', 'http://127.0.0.1:8545')
        self.web3 = Web3(Web3.HTTPProvider(ethereum_node_url))

        # 如果使用的是PoA共识的网络，需要添加这个中间件
        self.web3.middleware_onion.inject(geth_poa_middleware, layer=0)

        # 检查连接
        if not self.web3.is_connected():
            logger.warning("无法连接到以太坊节点，事件监听服务将不会启动")
            return

        # 加载合约ABI和地址
        try:
            with open('blockchain/contracts/artifacts/CarbonEmission_metadata.json', 'r') as f:
                contract_metadata = json.load(f)
                contract_abi = contract_metadata['output']['abi']
        except Exception as e:
            logger.error(f"加载合约ABI失败: {str(e)}")
            return

        # 合约地址
        contract_address = os.getenv('CONTRACT_ADDRESS')
        if not contract_address:
            logger.warning("未设置CONTRACT_ADDRESS环境变量，事件监听服务将不会启动")
            return

        # 创建合约实例
        self.contract = self.web3.eth.contract(
            address=self.web3.to_checksum_address(contract_address),
            abi=contract_abi
        )

        # 获取最新区块号
        self.last_block = self.web3.eth.block_number

        logger.info(f"区块链事件监听服务初始化成功，当前区块号: {self.last_block}")

        # 注册关闭时的清理函数
        app.teardown_appcontext(self.teardown)

    def start(self):
        """启动事件监听服务"""
        if self.running:
            logger.warning("事件监听服务已经在运行")
            return

        if not self.web3 or not self.contract:
            logger.warning("区块链连接未初始化，无法启动事件监听服务")
            return

        self.running = True
        self.thread = threading.Thread(target=self._listen_events)
        self.thread.daemon = True
        self.thread.start()

        logger.info("区块链事件监听服务已启动")

    def stop(self):
        """停止事件监听服务"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=5)
            self.thread = None

        logger.info("区块链事件监听服务已停止")

    def teardown(self, exception):
        """应用上下文结束时的清理函数"""
        self.stop()

    def _listen_events(self):
        """监听区块链事件的主循环"""
        logger.info(f"开始监听区块链事件，从区块 {self.last_block} 开始")

        while self.running:
            try:
                # 获取当前区块号
                current_block = self.web3.eth.block_number

                # 如果有新区块
                if current_block > self.last_block:
                    logger.info(f"发现新区块: {self.last_block + 1} 到 {current_block}")

                    # 获取事件
                    self._process_emission_data_events(self.last_block + 1, current_block)
                    self._process_verification_events(self.last_block + 1, current_block)
                    self._process_transaction_events(self.last_block + 1, current_block)
                    self._process_penalty_events(self.last_block + 1, current_block)

                    # 更新最后处理的区块号
                    self.last_block = current_block

                # 休眠一段时间
                time.sleep(10)

            except Exception as e:
                logger.error(f"监听事件时发生错误: {str(e)}")
                time.sleep(30)  # 发生错误时等待较长时间

    def _process_emission_data_events(self, from_block, to_block):
        """处理排放数据提交事件"""
        try:
            # 获取EmissionDataSubmitted事件
            events = self.contract.events.EmissionDataSubmitted.get_logs(
                fromBlock=from_block,
                toBlock=to_block
            )

            logger.info(f"获取到 {len(events)} 个排放数据提交事件")

            # 处理每个事件
            for event in events:
                emission_id = event['args']['id']
                enterprise = event['args']['enterprise']

                logger.info(f"处理排放数据提交事件: ID={emission_id}, 企业={enterprise}")

                # 在应用上下文中更新数据库
                with self.app.app_context():
                    from backend.models.emission import EmissionData
                    from backend import db

                    # 查找对应的排放数据记录
                    emission = EmissionData.query.get(emission_id)
                    if emission:
                        # 更新区块链信息
                        emission.blockchain_hash = event['transactionHash'].hex()
                        emission.blockchain_block = event['blockNumber']
                        db.session.commit()

                        logger.info(f"更新排放数据记录成功: ID={emission_id}")
                    else:
                        logger.warning(f"未找到对应的排放数据记录: ID={emission_id}")

        except Exception as e:
            logger.error(f"处理排放数据提交事件时发生错误: {str(e)}")

    def _process_verification_events(self, from_block, to_block):
        """处理核查结果提交事件"""
        try:
            # 获取VerificationRecordCreated事件
            events = self.contract.events.VerificationRecordCreated.get_logs(
                fromBlock=from_block,
                toBlock=to_block
            )

            logger.info(f"获取到 {len(events)} 个核查结果提交事件")

            # 处理每个事件
            for event in events:
                verification_id = event['args']['id']
                verifier = event['args']['verifier']
                enterprise = event['args']['enterprise']

                logger.info(f"处理核查结果提交事件: ID={verification_id}, 核查机构={verifier}, 企业={enterprise}")

                # 在应用上下文中更新数据库
                with self.app.app_context():
                    from backend.models.verification import Verification
                    from backend import db

                    # 查找对应的核查记录
                    verification = Verification.query.get(verification_id)
                    if verification:
                        # 更新区块链信息
                        verification.blockchain_hash = event['transactionHash'].hex()
                        verification.blockchain_block = event['blockNumber']
                        db.session.commit()

                        logger.info(f"更新核查记录成功: ID={verification_id}")
                    else:
                        logger.warning(f"未找到对应的核查记录: ID={verification_id}")

        except Exception as e:
            logger.error(f"处理核查结果提交事件时发生错误: {str(e)}")

    def _process_transaction_events(self, from_block, to_block):
        """处理交易事件"""
        try:
            # 获取TransactionCreated事件
            events = self.contract.events.TransactionCreated.get_logs(
                fromBlock=from_block,
                toBlock=to_block
            )

            logger.info(f"获取到 {len(events)} 个交易创建事件")

            # 处理每个事件
            for event in events:
                transaction_id = event['args']['id']
                buyer = event['args']['buyer']
                seller = event['args']['seller']

                logger.info(f"处理交易创建事件: ID={transaction_id}, 买方={buyer}, 卖方={seller}")

                # 在应用上下文中更新数据库
                with self.app.app_context():
                    from backend.models.transaction import Transaction
                    from backend import db

                    # 查找对应的交易记录
                    transaction = Transaction.query.get(transaction_id)
                    if transaction:
                        # 更新区块链信息
                        transaction.blockchain_hash = event['transactionHash'].hex()
                        transaction.blockchain_block = event['blockNumber']
                        db.session.commit()

                        logger.info(f"更新交易记录成功: ID={transaction_id}")
                    else:
                        logger.warning(f"未找到对应的交易记录: ID={transaction_id}")

            # 获取TransactionConfirmed事件
            events = self.contract.events.TransactionConfirmed.get_logs(
                fromBlock=from_block,
                toBlock=to_block
            )

            logger.info(f"获取到 {len(events)} 个交易确认事件")

            # 处理每个事件
            for event in events:
                transaction_id = event['args']['id']
                seller = event['args']['seller']

                logger.info(f"处理交易确认事件: ID={transaction_id}, 卖方={seller}")

                # 在应用上下文中更新数据库
                with self.app.app_context():
                    from backend.models.transaction import Transaction
                    from backend import db

                    # 查找对应的交易记录
                    transaction = Transaction.query.get(transaction_id)
                    if transaction:
                        # 更新交易状态
                        transaction.status = 'completed'
                        db.session.commit()

                        logger.info(f"更新交易状态成功: ID={transaction_id}, 状态=completed")
                    else:
                        logger.warning(f"未找到对应的交易记录: ID={transaction_id}")

            # 获取TransactionCancelled事件
            events = self.contract.events.TransactionCancelled.get_logs(
                fromBlock=from_block,
                toBlock=to_block
            )

            logger.info(f"获取到 {len(events)} 个交易取消事件")

            # 处理每个事件
            for event in events:
                transaction_id = event['args']['id']

                logger.info(f"处理交易取消事件: ID={transaction_id}")

                # 在应用上下文中更新数据库
                with self.app.app_context():
                    from backend.models.transaction import Transaction
                    from backend import db

                    # 查找对应的交易记录
                    transaction = Transaction.query.get(transaction_id)
                    if transaction:
                        # 更新交易状态
                        transaction.status = 'cancelled'
                        db.session.commit()

                        logger.info(f"更新交易状态成功: ID={transaction_id}, 状态=cancelled")
                    else:
                        logger.warning(f"未找到对应的交易记录: ID={transaction_id}")

        except Exception as e:
            logger.error(f"处理交易事件时发生错误: {str(e)}")

    def _process_penalty_events(self, from_block, to_block):
        """处理惩罚事件"""
        try:
            # 获取PenaltyCreated事件
            events = self.contract.events.PenaltyCreated.get_logs(
                fromBlock=from_block,
                toBlock=to_block
            )

            logger.info(f"获取到 {len(events)} 个惩罚创建事件")

            # 处理每个事件
            for event in events:
                penalty_id = event['args']['id']
                enterprise = event['args']['enterprise']

                logger.info(f"处理惩罚创建事件: ID={penalty_id}, 企业={enterprise}")

                # 在应用上下文中更新数据库
                with self.app.app_context():
                    from backend.models.penalty import Penalty
                    from backend import db

                    # 查找对应的惩罚记录
                    penalty = Penalty.query.get(penalty_id)
                    if penalty:
                        # 更新区块链信息
                        penalty.blockchain_hash = event['transactionHash'].hex()
                        penalty.blockchain_block = event['blockNumber']
                        db.session.commit()

                        logger.info(f"更新惩罚记录成功: ID={penalty_id}")
                    else:
                        logger.warning(f"未找到对应的惩罚记录: ID={penalty_id}")

        except Exception as e:
            logger.error(f"处理惩罚事件时发生错误: {str(e)}")
