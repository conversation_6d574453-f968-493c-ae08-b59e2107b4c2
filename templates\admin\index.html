<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放核查系统 - 管理员仪表板</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container">
            <a class="navbar-brand" href="/"><i class="fas fa-leaf me-2"></i>碳排放核查系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin"><i class="fas fa-tachometer-alt me-1"></i>仪表板</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/users"><i class="fas fa-users me-1"></i>用户管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/quotas"><i class="fas fa-chart-pie me-1"></i>配额管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/penalties"><i class="fas fa-exclamation-triangle me-1"></i>惩罚管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/reports"><i class="fas fa-file-alt me-1"></i>报告管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/blockchain/config"><i class="fas fa-link me-1"></i>区块链配置</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>{{ current_user.get('username', '管理员') }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="/admin/profile"><i class="fas fa-id-card me-1"></i>个人资料</a></li>
                            <li><a class="dropdown-item" href="/admin/settings"><i class="fas fa-cog me-1"></i>系统设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-1"></i>退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mt-4">
        <!-- 消息提示 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- 错误信息 -->
        {% if error %}
            <div class="alert alert-danger">
                <h4 class="alert-heading">发生错误!</h4>
                <p>{{ error }}</p>
            </div>
        {% endif %}

        <!-- 仪表板标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-tachometer-alt me-2"></i>管理员仪表板</h1>
            <div>
                <span class="badge bg-primary">当前时间: <span id="current-time"></span></span>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">用户总数</h6>
                                <h2 class="card-text">{{ user_count|default(0) }}</h2>
                            </div>
                            <i class="fas fa-users fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">企业用户</h6>
                                <h2 class="card-text">{{ enterprise_count|default(0) }}</h2>
                            </div>
                            <i class="fas fa-building fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">核查机构</h6>
                                <h2 class="card-text">{{ verifier_count|default(0) }}</h2>
                            </div>
                            <i class="fas fa-check-double fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">管理员</h6>
                                <h2 class="card-text">{{ admin_count|default(0) }}</h2>
                            </div>
                            <i class="fas fa-user-shield fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统状态 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0"><i class="fas fa-server me-2"></i>系统状态</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-group">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                数据库连接
                                {% if system_status and system_status.database_connected %}
                                    <span class="badge bg-success rounded-pill"><i class="fas fa-check"></i> 正常</span>
                                {% else %}
                                    <span class="badge bg-danger rounded-pill"><i class="fas fa-times"></i> 异常</span>
                                {% endif %}
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                区块链连接
                                {% if system_status and system_status.blockchain_connected %}
                                    <span class="badge bg-success rounded-pill"><i class="fas fa-check"></i> 正常</span>
                                {% else %}
                                    <span class="badge bg-warning rounded-pill"><i class="fas fa-exclamation-triangle"></i> 模拟模式</span>
                                {% endif %}
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                事件监听服务
                                {% if system_status and system_status.event_listener_running %}
                                    <span class="badge bg-success rounded-pill"><i class="fas fa-check"></i> 运行中</span>
                                {% else %}
                                    <span class="badge bg-secondary rounded-pill"><i class="fas fa-pause"></i> 已停止</span>
                                {% endif %}
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0"><i class="fas fa-users me-2"></i>最近登录用户</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>用户名</th>
                                        <th>角色</th>
                                        <th>最后登录时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if recent_users %}
                                        {% for user in recent_users %}
                                            <tr>
                                                <td>{{ user.username }}</td>
                                                <td>
                                                    {% if user.role == 'admin' %}
                                                        <span class="badge bg-warning">管理员</span>
                                                    {% elif user.role == 'enterprise' %}
                                                        <span class="badge bg-success">企业</span>
                                                    {% elif user.role == 'verifier' %}
                                                        <span class="badge bg-info">核查机构</span>
                                                    {% else %}
                                                        <span class="badge bg-secondary">未知</span>
                                                    {% endif %}
                                                </td>
                                                <td>{{ user.last_login.strftime('%Y-%m-%d %H:%M:%S') if user.last_login else '从未登录' }}</td>
                                            </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="3" class="text-center">暂无数据</td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0"><i class="fas fa-bolt me-2"></i>快速操作</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <a href="/admin/users/add" class="btn btn-primary w-100">
                                    <i class="fas fa-user-plus me-2"></i>添加用户
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="/admin/quotas/allocate" class="btn btn-success w-100">
                                    <i class="fas fa-chart-pie me-2"></i>分配配额
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="/admin/reports/generate" class="btn btn-info w-100">
                                    <i class="fas fa-file-alt me-2"></i>生成报告
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="/admin/blockchain/config" class="btn btn-warning w-100">
                                    <i class="fas fa-cog me-2"></i>区块链配置
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">© 2025 碳排放核查系统 | 基于区块链技术</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // 显示当前时间
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleString('zh-CN');
        }
        updateTime();
        setInterval(updateTime, 1000);
    </script>
</body>
</html>
