"""
数据库工具模块
提供数据库连接、初始化和验证功能
"""

import os
import pymysql
from datetime import datetime
from dotenv import load_dotenv
from backend import db, create_app
from backend.models.user import User

# 加载环境变量
load_dotenv()

def get_db_connection():
    """获取数据库连接"""
    # 从环境变量获取数据库连接信息
    db_url = os.environ.get('DATABASE_URL')

    if not db_url:
        raise ValueError('错误: 未找到DATABASE_URL环境变量')

    # 解析数据库连接字符串
    # 格式: mysql+pymysql://username:password@host:port/dbname?charset=utf8mb4
    # 移除协议前缀
    db_url = db_url.replace('mysql+pymysql://', '')
    # 分离用户名密码和主机信息

    auth, rest = db_url.split('@')
    username, password = auth.split(':')

    # 分离主机和数据库名
    host_port, dbname = rest.split('/')

    # 处理可能的查询参数
    if '?' in dbname:
        dbname = dbname.split('?')[0]

    # 分离主机和端口
    if ':' in host_port:
        host, port = host_port.split(':')
        port = int(port)
    else:
        host = host_port
        port = 3306

    # 连接数据库
    conn = pymysql.connect(
        host=host,
        port=port,
        user=username,
        password=password,
        database=dbname,
        charset='utf8mb4'
    )

    return conn

def test_connection():
    """测试数据库连接"""
    print('测试数据库连接...')
    try:
        conn = get_db_connection()

        # 测试查询
        with conn.cursor() as cursor:
            cursor.execute('SELECT VERSION()')
            version = cursor.fetchone()
            print(f'数据库连接成功! MySQL版本: {version[0]}')

        # 关闭连接
        conn.close()
        return True
    except Exception as e:
        print(f'数据库连接失败: {str(e)}')
        return False

def execute_sql_script(script_path):
    """执行SQL脚本"""
    print(f'执行SQL脚本: {script_path}')

    try:
        # 读取SQL脚本
        with open(script_path, 'r', encoding='utf-8') as f:
            sql_script = f.read()

        conn = get_db_connection()

        # 执行SQL脚本
        with conn.cursor() as cursor:
            # 分割SQL语句
            statements = sql_script.split(';')

            # 执行每条SQL语句
            for statement in statements:
                statement = statement.strip()
                if statement:
                    try:
                        cursor.execute(statement)
                        print(f'执行SQL语句成功: {statement[:50]}...')
                    except Exception as e:
                        print(f'执行SQL语句失败: {statement[:50]}...')
                        print(f'错误信息: {str(e)}')

        # 提交事务
        conn.commit()

        # 关闭连接
        conn.close()

        print('SQL脚本执行完成')
        return True
    except Exception as e:
        print(f'执行SQL脚本失败: {str(e)}')
        return False

def init_db_with_sqlalchemy():
    """使用SQLAlchemy初始化数据库"""
    print('开始初始化数据库...')
    app = create_app()

    print('创建应用上下文...')
    with app.app_context():
        print('开始创建数据库表...')
        try:
            # 创建所有表
            db.create_all()
            print('数据库表创建成功')

            # 创建管理员用户
            admin = User.query.filter_by(username='admin').first()
            if not admin:
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    role='admin',
                    company_name='系统管理',
                    credit_code='000000000000000000',
                    created_at=datetime.now()
                )
                admin.set_password('admin123')
                db.session.add(admin)
                print('已创建管理员用户: admin (密码: admin123)')
            else:
                print('管理员用户已存在，跳过创建')

            # 创建企业用户
            enterprise1 = User.query.filter_by(username='enterprise1').first()
            if not enterprise1:
                enterprise1 = User(
                    username='enterprise1',
                    email='<EMAIL>',
                    role='enterprise',
                    company_name='北京碳排放科技有限公司',
                    credit_code='91110000123456789A',
                    created_at=datetime.now()
                )
                enterprise1.set_password('enterprise123')
                db.session.add(enterprise1)
                print('已创建企业用户: enterprise1 (密码: enterprise123)')
            else:
                print('企业用户enterprise1已存在，跳过创建')

            enterprise2 = User.query.filter_by(username='enterprise2').first()
            if not enterprise2:
                enterprise2 = User(
                    username='enterprise2',
                    email='<EMAIL>',
                    role='enterprise',
                    company_name='上海绿色能源有限公司',
                    credit_code='91310000123456789B',
                    created_at=datetime.now()
                )
                enterprise2.set_password('enterprise123')
                db.session.add(enterprise2)
                print('已创建企业用户: enterprise2 (密码: enterprise123)')
            else:
                print('企业用户enterprise2已存在，跳过创建')

            # 创建核查机构用户
            verifier1 = User.query.filter_by(username='verifier1').first()
            if not verifier1:
                verifier1 = User(
                    username='verifier1',
                    email='<EMAIL>',
                    role='verifier',
                    company_name='国家碳排放核查中心',
                    credit_code='91100000123456789C',
                    created_at=datetime.now()
                )
                verifier1.set_password('verifier123')
                db.session.add(verifier1)
                print('已创建核查机构用户: verifier1 (密码: verifier123)')
            else:
                print('核查机构用户verifier1已存在，跳过创建')

            # 提交所有更改
            db.session.commit()

            print('数据库初始化完成')
            return True
        except Exception as e:
            print(f'数据库初始化失败: {str(e)}')
            return False

def verify_database():
    """验证数据库初始化"""
    print('验证数据库初始化...')

    try:
        conn = get_db_connection()

        # 验证表是否存在
        with conn.cursor() as cursor:
            # 获取所有表
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()

            print(f'数据库中的表:')
            for table in tables:
                print(f'  - {table[0]}')

            # 验证用户表
            cursor.execute("SELECT COUNT(*) FROM `user`")
            user_count = cursor.fetchone()[0]
            print(f'用户表中的记录数: {user_count}')

            # 验证管理员用户
            cursor.execute("SELECT * FROM `user` WHERE `role` = 'admin'")
            admin_users = cursor.fetchall()
            print(f'管理员用户数: {len(admin_users)}')

            # 验证系统配置
            cursor.execute("SELECT COUNT(*) FROM `system_config`")
            config_count = cursor.fetchone()[0]
            print(f'系统配置数: {config_count}')

        # 关闭连接
        conn.close()

        print('数据库验证完成')
        return True
    except Exception as e:
        print(f'数据库验证失败: {str(e)}')
        return False

if __name__ == '__main__':
    # 测试数据库连接
    if test_connection():
        # 验证数据库
        verify_database()
    else:
        print('数据库连接失败，无法验证数据库')
