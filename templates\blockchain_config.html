{% extends "base.html" %}

{% block title %}区块链配置{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="alert alert-info" role="alert">
        <h5 class="alert-heading"><i class="fas fa-info-circle"></i> 区块链配置说明</h5>
        <p>本页面用于配置区块链节点、部署智能合约，以及为不同角色分配区块链账户。请按照以下步骤操作：</p>
        <ol>
            <li>确保 Ganache 已启动并运行在 <code>http://127.0.0.1:8545</code>（默认地址）</li>
            <li>点击"获取Ganache账户"按钮，获取可用的账户列表</li>
            <li>将账户地址和私钥填入相应的表单字段</li>
            <li>点击"保存配置"按钮，保存配置信息</li>
            <li>点击"部署智能合约"按钮，部署智能合约</li>
        </ol>
        <p class="mb-0">完成以上步骤后，系统将能够与区块链进行交互，实现碳排放数据的上链和核查。</p>
    </div>

    <div class="card shadow">
        <div class="card-header bg-primary text-white">
            <h4 class="mb-0">区块链配置</h4>
        </div>
        <div class="card-body">
            {% if success_message %}
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle"></i> {{ success_message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endif %}

            {% if error_message %}
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle"></i> {{ error_message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endif %}

            <form method="POST" action="{{ url_for('blockchain_config.blockchain_config') }}">
                <div class="mb-4">
                    <h5 class="border-bottom pb-2">区块链连接信息</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="ethereum_node_url" class="form-label">以太坊节点URL</label>
                                <input type="text" class="form-control" id="ethereum_node_url" name="ethereum_node_url"
                                       value="{{ config.get('ETHEREUM_NODE_URL', 'http://127.0.0.1:8545') }}" required>
                                <div class="form-text">Ganache默认URL: http://127.0.0.1:8545</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="contract_address" class="form-label">合约地址</label>
                                <input type="text" class="form-control" id="contract_address" name="contract_address"
                                       value="{{ config.get('CONTRACT_ADDRESS', '') }}" placeholder="部署合约后自动填充">
                                <div class="form-text">部署合约后会自动更新</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <h5 class="border-bottom pb-2">管理员账户</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="admin_address" class="form-label">管理员地址</label>
                                <input type="text" class="form-control" id="admin_address" name="admin_address"
                                       value="{{ config.get('ADMIN_ADDRESS', '') }}" required>
                                <div class="form-text">Ganache中第1个账户的地址</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="admin_private_key" class="form-label">管理员私钥</label>
                                <input type="text" class="form-control" id="admin_private_key" name="admin_private_key"
                                       value="{{ config.get('ADMIN_PRIVATE_KEY', '') }}" required>
                                <div class="form-text">Ganache中第1个账户的私钥</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <h5 class="border-bottom pb-2">企业账户</h5>

                    <!-- 企业1 -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="enterprise_1_address" class="form-label">企业1地址</label>
                                <input type="text" class="form-control" id="enterprise_1_address" name="enterprise_1_address"
                                       value="{{ config.get('ENTERPRISE_1_ADDRESS', '') }}" required>
                                <div class="form-text">Ganache中第2个账户的地址</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="enterprise_1_key" class="form-label">企业1私钥</label>
                                <input type="text" class="form-control" id="enterprise_1_key" name="enterprise_1_key"
                                       value="{{ config.get('ENTERPRISE_1_KEY', '') }}" required>
                                <div class="form-text">Ganache中第2个账户的私钥</div>
                            </div>
                        </div>
                    </div>

                    <!-- 企业2 -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="enterprise_2_address" class="form-label">企业2地址</label>
                                <input type="text" class="form-control" id="enterprise_2_address" name="enterprise_2_address"
                                       value="{{ config.get('ENTERPRISE_2_ADDRESS', '') }}" required>
                                <div class="form-text">Ganache中第3个账户的地址</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="enterprise_2_key" class="form-label">企业2私钥</label>
                                <input type="text" class="form-control" id="enterprise_2_key" name="enterprise_2_key"
                                       value="{{ config.get('ENTERPRISE_2_KEY', '') }}" required>
                                <div class="form-text">Ganache中第3个账户的私钥</div>
                            </div>
                        </div>
                    </div>

                    <!-- 企业3 -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="enterprise_3_address" class="form-label">企业3地址</label>
                                <input type="text" class="form-control" id="enterprise_3_address" name="enterprise_3_address"
                                       value="{{ config.get('ENTERPRISE_3_ADDRESS', '') }}" required>
                                <div class="form-text">Ganache中第4个账户的地址</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="enterprise_3_key" class="form-label">企业3私钥</label>
                                <input type="text" class="form-control" id="enterprise_3_key" name="enterprise_3_key"
                                       value="{{ config.get('ENTERPRISE_3_KEY', '') }}" required>
                                <div class="form-text">Ganache中第4个账户的私钥</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <h5 class="border-bottom pb-2">核查机构账户</h5>

                    <!-- 核查机构1 -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="verifier_1_address" class="form-label">核查机构1地址</label>
                                <input type="text" class="form-control" id="verifier_1_address" name="verifier_1_address"
                                       value="{{ config.get('VERIFIER_1_ADDRESS', '') }}" required>
                                <div class="form-text">Ganache中第5个账户的地址</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="verifier_1_key" class="form-label">核查机构1私钥</label>
                                <input type="text" class="form-control" id="verifier_1_key" name="verifier_1_key"
                                       value="{{ config.get('VERIFIER_1_KEY', '') }}" required>
                                <div class="form-text">Ganache中第5个账户的私钥</div>
                            </div>
                        </div>
                    </div>

                    <!-- 核查机构2 -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="verifier_2_address" class="form-label">核查机构2地址</label>
                                <input type="text" class="form-control" id="verifier_2_address" name="verifier_2_address"
                                       value="{{ config.get('VERIFIER_2_ADDRESS', '') }}" required>
                                <div class="form-text">Ganache中第6个账户的地址</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="verifier_2_key" class="form-label">核查机构2私钥</label>
                                <input type="text" class="form-control" id="verifier_2_key" name="verifier_2_key"
                                       value="{{ config.get('VERIFIER_2_KEY', '') }}" required>
                                <div class="form-text">Ganache中第6个账户的私钥</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-between">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> 保存配置
                    </button>
                    <div>
                        <a href="{{ url_for('blockchain_config.fetch_ganache_accounts') }}" class="btn btn-info me-2">
                            <i class="fas fa-sync-alt"></i> 获取Ganache账户
                        </a>
                        <a href="{{ url_for('blockchain_config.deploy_contract') }}" class="btn btn-success"
                           onclick="return confirm('确定要部署智能合约吗？这将使用管理员账户部署新的合约。')">
                            <i class="fas fa-upload"></i> 部署智能合约
                        </a>
                    </div>
                </div>

                <div class="alert alert-warning mt-4" role="alert">
                    <h5 class="alert-heading"><i class="fas fa-exclamation-triangle"></i> 重要提示</h5>
                    <p>请确保妥善保管私钥信息，不要泄露给他人。私钥泄露可能导致账户资产被盗。</p>
                    <p class="mb-0">部署智能合约需要消耗一定的以太币，请确保管理员账户有足够的余额。</p>
                </div>
            </form>
        </div>
    </div>

    {% if ganache_accounts %}
    <div class="card shadow mt-4">
        <div class="card-header bg-info text-white">
            <h4 class="mb-0"><i class="fas fa-wallet"></i> Ganache账户信息</h4>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>账户</th>
                            <th>地址</th>
                            <th>余额</th>
                            <th>建议用途</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for account in ganache_accounts %}
                        <tr>
                            <td>账户 {{ loop.index }}</td>
                            <td><code>{{ account.address }}</code></td>
                            <td>{{ account.balance }} ETH</td>
                            <td>
                                {% if loop.index == 1 %}
                                <span class="badge bg-danger">管理员</span>
                                {% elif loop.index <= 4 %}
                                <span class="badge bg-primary">企业{{ loop.index - 1 }}</span>
                                {% else %}
                                <span class="badge bg-success">核查机构{{ loop.index - 4 }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary copy-address" data-address="{{ account.address }}">
                                    <i class="fas fa-copy"></i> 复制地址
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            <div class="alert alert-warning mt-3">
                <h5 class="alert-heading"><i class="fas fa-key"></i> 关于私钥</h5>
                <p>Ganache提供的账户私钥可以在Ganache UI界面中查看。请从Ganache界面获取私钥并填写到上面的表单中。</p>
                <p class="mb-0">建议按照表格中的"建议用途"列来分配账户角色，以便于管理和使用。</p>
            </div>

            <div class="text-center mt-4">
                <a href="{{ url_for('blockchain_config.blockchain_config') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> 返回配置页面
                </a>
            </div>
        </div>
    </div>
    {% endif %}
</div>

{% block head %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<style>
    .form-control:focus {
        border-color: #4CAF50;
        box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25);
    }
    .copy-success {
        animation: fadeInOut 2s ease;
    }
    @keyframes fadeInOut {
        0% { background-color: transparent; }
        20% { background-color: rgba(76, 175, 80, 0.2); }
        80% { background-color: rgba(76, 175, 80, 0.2); }
        100% { background-color: transparent; }
    }
    .card {
        transition: all 0.3s ease;
    }
    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .badge {
        font-size: 0.9em;
        padding: 0.5em 0.8em;
    }
    code {
        background-color: #f8f9fa;
        padding: 0.2em 0.4em;
        border-radius: 3px;
        font-size: 0.9em;
    }
</style>
{% endblock %}

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 复制地址功能
    const copyButtons = document.querySelectorAll('.copy-address');
    copyButtons.forEach(button => {
        button.addEventListener('click', function() {
            const address = this.getAttribute('data-address');
            navigator.clipboard.writeText(address).then(() => {
                // 保存原始内容和类
                const originalHTML = this.innerHTML;
                const originalClasses = [...this.classList];

                // 更改按钮外观
                this.innerHTML = '<i class="fas fa-check"></i> 已复制!';
                this.classList.remove('btn-outline-primary');
                this.classList.add('btn-success');

                // 高亮表格行
                const row = this.closest('tr');
                row.classList.add('copy-success');

                // 恢复原始状态
                setTimeout(() => {
                    this.innerHTML = originalHTML;
                    this.className = '';
                    originalClasses.forEach(cls => this.classList.add(cls));
                    row.classList.remove('copy-success');
                }, 2000);
            }).catch(err => {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制地址');
            });
        });
    });

    // 表单验证
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(event) {
            const ethereumNodeUrl = document.getElementById('ethereum_node_url').value;
            const adminAddress = document.getElementById('admin_address').value;
            const adminPrivateKey = document.getElementById('admin_private_key').value;

            if (!ethereumNodeUrl || !adminAddress || !adminPrivateKey) {
                event.preventDefault();
                alert('请至少填写以太坊节点URL、管理员地址和管理员私钥');
            }
        });
    }
});
</script>
{% endblock %}
