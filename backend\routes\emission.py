"""
排放数据管理路由
"""

from flask import Blueprint, request, jsonify, session, current_app
from backend.models.emission import EmissionData
from backend.models.user import User
from backend import db
from datetime import datetime
import hashlib
import os
import json

emission_bp = Blueprint('emission', __name__, url_prefix='/api/emission')

@emission_bp.route('/list', methods=['GET'])
def list_emissions():
    """获取排放数据列表"""
    # 检查用户是否已登录
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': '请先登录'}), 401
    
    # 获取查询参数
    enterprise_id = request.args.get('enterprise_id', type=int)
    source = request.args.get('source')
    status = request.args.get('status')
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    
    # 构建查询
    query = EmissionData.query
    
    # 如果是企业用户，只能查看自己的排放数据
    if session.get('role') == 'enterprise':
        query = query.filter(EmissionData.enterprise_id == session.get('user_id'))
    # 如果是管理员或核查机构，可以查看所有排放数据，但可以按企业ID筛选
    elif enterprise_id:
        query = query.filter(EmissionData.enterprise_id == enterprise_id)
    
    # 应用其他筛选条件
    if source:
        query = query.filter(EmissionData.source == source)
    if status:
        query = query.filter(EmissionData.status == status)
    if date_from:
        query = query.filter(EmissionData.submission_date >= date_from)
    if date_to:
        query = query.filter(EmissionData.submission_date <= date_to)
    
    # 执行查询
    emissions = query.order_by(EmissionData.submission_date.desc()).all()
    
    # 转换为JSON
    result = []
    for emission in emissions:
        # 获取企业名称
        enterprise = User.query.get(emission.enterprise_id)
        enterprise_name = enterprise.company_name if enterprise else '未知企业'
        
        result.append({
            'id': emission.id,
            'enterprise_id': emission.enterprise_id,
            'enterprise_name': enterprise_name,
            'source': emission.source,
            'amount': emission.amount,
            'unit': emission.unit,
            'submission_date': emission.submission_date.strftime('%Y-%m-%d'),
            'status': emission.status,
            'description': emission.description,
            'blockchain_hash': emission.blockchain_hash
        })
    
    return jsonify({'success': True, 'data': result})

@emission_bp.route('/detail/<int:emission_id>', methods=['GET'])
def get_emission_detail(emission_id):
    """获取排放数据详情"""
    # 检查用户是否已登录
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': '请先登录'}), 401
    
    # 获取排放数据
    emission = EmissionData.query.get(emission_id)
    if not emission:
        return jsonify({'success': False, 'message': '排放数据不存在'}), 404
    
    # 如果是企业用户，只能查看自己的排放数据
    if session.get('role') == 'enterprise' and emission.enterprise_id != session.get('user_id'):
        return jsonify({'success': False, 'message': '无权查看此排放数据'}), 403
    
    # 获取企业名称
    enterprise = User.query.get(emission.enterprise_id)
    enterprise_name = enterprise.company_name if enterprise else '未知企业'
    
    # 从区块链获取数据
    blockchain_data = {}
    if emission.blockchain_hash and hasattr(current_app, 'blockchain_client'):
        try:
            blockchain_data = current_app.blockchain_client.get_emission_data(emission.id)
        except Exception as e:
            print(f"从区块链获取数据失败: {str(e)}")
    
    # 构建结果
    result = {
        'id': emission.id,
        'enterprise_id': emission.enterprise_id,
        'enterprise_name': enterprise_name,
        'source': emission.source,
        'amount': emission.amount,
        'unit': emission.unit,
        'submission_date': emission.submission_date.strftime('%Y-%m-%d'),
        'status': emission.status,
        'description': emission.description,
        'blockchain_hash': emission.blockchain_hash,
        'blockchain_data': blockchain_data
    }
    
    return jsonify({'success': True, 'data': result})

@emission_bp.route('/add', methods=['POST'])
def add_emission():
    """添加排放数据"""
    # 检查用户是否已登录
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': '请先登录'}), 401
    
    # 检查用户是否是企业
    if session.get('role') != 'enterprise':
        return jsonify({'success': False, 'message': '只有企业用户可以添加排放数据'}), 403
    
    # 获取请求数据
    data = request.json
    if not data:
        return jsonify({'success': False, 'message': '请求数据为空'}), 400
    
    # 验证必填字段
    required_fields = ['source', 'amount', 'unit', 'description']
    for field in required_fields:
        if field not in data:
            return jsonify({'success': False, 'message': f'缺少必填字段: {field}'}), 400
    
    # 创建排放数据
    emission = EmissionData(
        enterprise_id=session.get('user_id'),
        source=data['source'],
        amount=data['amount'],
        unit=data['unit'],
        submission_date=datetime.now(),
        status='待提交',
        description=data['description']
    )
    
    # 保存到数据库
    try:
        db.session.add(emission)
        db.session.commit()
        
        return jsonify({
            'success': True, 
            'message': '排放数据添加成功', 
            'data': {'id': emission.id}
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'保存排放数据失败: {str(e)}'}), 500

@emission_bp.route('/update/<int:emission_id>', methods=['PUT'])
def update_emission(emission_id):
    """更新排放数据"""
    # 检查用户是否已登录
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': '请先登录'}), 401
    
    # 获取排放数据
    emission = EmissionData.query.get(emission_id)
    if not emission:
        return jsonify({'success': False, 'message': '排放数据不存在'}), 404
    
    # 检查权限
    if session.get('role') == 'enterprise' and emission.enterprise_id != session.get('user_id'):
        return jsonify({'success': False, 'message': '无权更新此排放数据'}), 403
    
    # 检查状态
    if emission.status not in ['待提交', '已拒绝']:
        return jsonify({'success': False, 'message': '只能更新待提交或已拒绝状态的排放数据'}), 400
    
    # 获取请求数据
    data = request.json
    if not data:
        return jsonify({'success': False, 'message': '请求数据为空'}), 400
    
    # 更新排放数据
    if 'source' in data:
        emission.source = data['source']
    if 'amount' in data:
        emission.amount = data['amount']
    if 'unit' in data:
        emission.unit = data['unit']
    if 'description' in data:
        emission.description = data['description']
    
    # 保存到数据库
    try:
        db.session.commit()
        return jsonify({'success': True, 'message': '排放数据更新成功'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'更新排放数据失败: {str(e)}'}), 500

@emission_bp.route('/submit/<int:emission_id>', methods=['POST'])
def submit_emission(emission_id):
    """提交排放数据到区块链"""
    # 检查用户是否已登录
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': '请先登录'}), 401
    
    # 获取排放数据
    emission = EmissionData.query.get(emission_id)
    if not emission:
        return jsonify({'success': False, 'message': '排放数据不存在'}), 404
    
    # 检查权限
    if session.get('role') == 'enterprise' and emission.enterprise_id != session.get('user_id'):
        return jsonify({'success': False, 'message': '无权提交此排放数据'}), 403
    
    # 检查状态
    if emission.status not in ['待提交', '已拒绝']:
        return jsonify({'success': False, 'message': '只能提交待提交或已拒绝状态的排放数据'}), 400
    
    # 生成数据哈希
    data_str = f"{emission.id}_{emission.enterprise_id}_{emission.source}_{emission.amount}_{emission.unit}_{emission.submission_date.isoformat()}"
    hash_value = hashlib.sha256(data_str.encode()).hexdigest()
    
    # 提交到区块链
    if hasattr(current_app, 'blockchain_client'):
        try:
            result = current_app.blockchain_client.submit_emission_data(
                emission_id=emission.id,
                enterprise_id=emission.enterprise_id,
                amount=emission.amount,
                timestamp=int(emission.submission_date.timestamp()),
                hash_value=hash_value
            )
            
            if result.get('success'):
                # 更新排放数据状态和区块链哈希
                emission.status = '已提交'
                emission.blockchain_hash = result.get('tx_hash')
                db.session.commit()
                
                return jsonify({
                    'success': True, 
                    'message': '排放数据提交成功', 
                    'data': {
                        'blockchain_hash': result.get('tx_hash'),
                        'block_number': result.get('block_number')
                    }
                })
            else:
                return jsonify({'success': False, 'message': f'提交到区块链失败: {result.get("error")}'}), 500
        except Exception as e:
            db.session.rollback()
            return jsonify({'success': False, 'message': f'提交排放数据失败: {str(e)}'}), 500
    else:
        # 模拟区块链提交
        emission.status = '已提交'
        emission.blockchain_hash = f"0x{hash_value[:40]}"
        db.session.commit()
        
        return jsonify({
            'success': True, 
            'message': '排放数据提交成功（模拟模式）', 
            'data': {
                'blockchain_hash': f"0x{hash_value[:40]}",
                'block_number': 12345678
            }
        })

@emission_bp.route('/delete/<int:emission_id>', methods=['DELETE'])
def delete_emission(emission_id):
    """删除排放数据"""
    # 检查用户是否已登录
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': '请先登录'}), 401
    
    # 获取排放数据
    emission = EmissionData.query.get(emission_id)
    if not emission:
        return jsonify({'success': False, 'message': '排放数据不存在'}), 404
    
    # 检查权限
    if session.get('role') == 'enterprise' and emission.enterprise_id != session.get('user_id'):
        return jsonify({'success': False, 'message': '无权删除此排放数据'}), 403
    
    # 检查状态
    if emission.status != '待提交':
        return jsonify({'success': False, 'message': '只能删除待提交状态的排放数据'}), 400
    
    # 删除排放数据
    try:
        db.session.delete(emission)
        db.session.commit()
        return jsonify({'success': True, 'message': '排放数据删除成功'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'删除排放数据失败: {str(e)}'}), 500

@emission_bp.route('/statistics', methods=['GET'])
def get_emission_statistics():
    """获取排放数据统计信息"""
    # 检查用户是否已登录
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': '请先登录'}), 401
    
    # 构建查询
    query = EmissionData.query
    
    # 如果是企业用户，只能查看自己的排放数据
    if session.get('role') == 'enterprise':
        query = query.filter(EmissionData.enterprise_id == session.get('user_id'))
    
    # 计算总排放量
    total_emission = 0
    verified_emission = 0
    pending_emission = 0
    
    emissions = query.all()
    for emission in emissions:
        if emission.unit == 'tCO2e':  # 确保单位一致
            total_emission += emission.amount
            if emission.status == '已核查':
                verified_emission += emission.amount
            elif emission.status in ['已提交', '待核查']:
                pending_emission += emission.amount
    
    # 按排放源统计
    source_stats = {}
    for emission in emissions:
        if emission.source not in source_stats:
            source_stats[emission.source] = 0
        source_stats[emission.source] += emission.amount
    
    # 按月份统计
    month_stats = {}
    for emission in emissions:
        month_key = emission.submission_date.strftime('%Y-%m')
        if month_key not in month_stats:
            month_stats[month_key] = 0
        month_stats[month_key] += emission.amount
    
    # 构建结果
    result = {
        'total_emission': total_emission,
        'verified_emission': verified_emission,
        'pending_emission': pending_emission,
        'source_stats': [{'source': k, 'amount': v} for k, v in source_stats.items()],
        'month_stats': [{'month': k, 'amount': v} for k, v in month_stats.items()]
    }
    
    return jsonify({'success': True, 'data': result})
