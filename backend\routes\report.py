"""
报告相关路由
"""

from datetime import datetime
from flask import Blueprint, request, jsonify, current_app, render_template
from flask_jwt_extended import jwt_required, get_jwt_identity
from sqlalchemy import func

from backend import db
from backend.models.user import User
from backend.models.emission import EmissionData
from backend.models.transaction import Transaction
from backend.models.carbon_quota import CarbonQuota
from backend.models.report import Report
from backend.models.activity import Activity

report_bp = Blueprint('report', __name__)

@report_bp.route('/emission', methods=['POST'])
@jwt_required()
def generate_emission_report():
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)

    if not user:
        return jsonify({'error': '用户不存在'}), 404

    data = request.get_json()

    # 验证必填字段
    required_fields = ['period_start', 'period_end']
    for field in required_fields:
        if field not in data:
            return jsonify({'error': f'缺少必填字段: {field}'}), 400

    # 解析日期
    try:
        period_start = datetime.strptime(data['period_start'], '%Y-%m-%d').date()
        period_end = datetime.strptime(data['period_end'], '%Y-%m-%d').date()
    except ValueError:
        return jsonify({'error': '日期格式无效，请使用YYYY-MM-DD格式'}), 400

    # 获取排放数据
    if user.role == 'admin' and 'enterprise_id' in data:
        enterprise = User.query.get(data['enterprise_id'])
        if not enterprise or enterprise.role != 'enterprise':
            return jsonify({'error': '企业不存在'}), 404

        emissions = EmissionData.query.filter(
            EmissionData.enterprise_id == data['enterprise_id'],
            EmissionData.emission_period_start >= period_start,
            EmissionData.emission_period_end <= period_end,
            EmissionData.status == 'verified'
        ).all()
    elif user.role == 'enterprise':
        enterprise = user
        emissions = EmissionData.query.filter(
            EmissionData.enterprise_id == current_user_id,
            EmissionData.emission_period_start >= period_start,
            EmissionData.emission_period_end <= period_end,
            EmissionData.status == 'verified'
        ).all()
    else:
        return jsonify({'error': '无权生成排放报告'}), 403

    # 如果没有数据，返回错误
    if not emissions:
        return jsonify({'error': '指定时间段内没有已核查的排放数据'}), 400

    # 生成报告内容
    report_content = current_app.report_generator.generate_emission_report(
        enterprise,
        emissions,
        period_start,
        period_end
    )

    # 保存报告
    report = Report(
        enterprise_id=enterprise.id,
        title=report_content['title'],
        report_type='emission',
        created_at=datetime.now(),
        period_start=period_start,
        period_end=period_end
    )
    report.set_content(report_content)

    # 生成HTML报告
    html_content = render_template(
        'reports/emission_report.html',
        enterprise=enterprise,
        period_start=period_start.strftime('%Y-%m-%d'),
        period_end=period_end.strftime('%Y-%m-%d'),
        report_date=datetime.now().strftime('%Y-%m-%d'),
        total_emission=sum(data.emission_amount for data in emissions),
        trend_chart="",  # 这里应该生成趋势图的base64编码
        source_chart="",  # 这里应该生成排放源占比图的base64编码
        source_summary=[
            {
                'emission_source': source,
                'total': sum(data.emission_amount for data in emissions if data.emission_source == source)
            }
            for source in set(data.emission_source for data in emissions)
        ],
        has_prediction=False,  # 是否包含预测数据
        emission_data=emissions
    )

    # 保存HTML报告
    file_path = f"reports/emission_{enterprise.id}_{datetime.now().strftime('%Y%m%d%H%M%S')}.html"
    report.file_path = file_path

    db.session.add(report)
    db.session.commit()

    # 记录活动
    activity = Activity(
        user_id=current_user_id,
        activity_type='generate_emission_report',
        description=f'用户生成了排放报告，ID: {report.id}'
    )
    db.session.add(activity)
    db.session.commit()

    return jsonify({
        'message': '排放报告生成成功',
        'report': report.to_dict(),
        'html_content': html_content
    }), 201

@report_bp.route('/trading', methods=['POST'])
@jwt_required()
def generate_trading_report():
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)

    if not user:
        return jsonify({'error': '用户不存在'}), 404

    data = request.get_json()

    # 验证必填字段
    required_fields = ['period_start', 'period_end']
    for field in required_fields:
        if field not in data:
            return jsonify({'error': f'缺少必填字段: {field}'}), 400

    # 解析日期
    try:
        period_start = datetime.strptime(data['period_start'], '%Y-%m-%d').date()
        period_end = datetime.strptime(data['period_end'], '%Y-%m-%d').date()
    except ValueError:
        return jsonify({'error': '日期格式无效，请使用YYYY-MM-DD格式'}), 400

    # 获取交易数据
    if user.role == 'admin' and 'enterprise_id' in data:
        enterprise = User.query.get(data['enterprise_id'])
        if not enterprise or enterprise.role != 'enterprise':
            return jsonify({'error': '企业不存在'}), 404

        transactions = Transaction.query.filter(
            (Transaction.seller_id == data['enterprise_id']) | (Transaction.buyer_id == data['enterprise_id']),
            Transaction.transaction_time >= period_start,
            Transaction.transaction_time <= period_end,
            Transaction.status == 'completed'
        ).all()
    elif user.role == 'enterprise':
        enterprise = user
        transactions = Transaction.query.filter(
            (Transaction.seller_id == current_user_id) | (Transaction.buyer_id == current_user_id),
            Transaction.transaction_time >= period_start,
            Transaction.transaction_time <= period_end,
            Transaction.status == 'completed'
        ).all()
    else:
        return jsonify({'error': '无权生成交易报告'}), 403

    # 如果没有数据，返回错误
    if not transactions:
        return jsonify({'error': '指定时间段内没有已完成的交易'}), 400

    # 生成报告内容
    report_content = current_app.report_generator.generate_trading_report(
        enterprise,
        transactions,
        period_start,
        period_end
    )

    # 保存报告
    report = Report(
        enterprise_id=enterprise.id,
        title=report_content['title'],
        report_type='trading',
        created_at=datetime.now(),
        period_start=period_start,
        period_end=period_end
    )
    report.set_content(report_content)

    db.session.add(report)
    db.session.commit()

    # 记录活动
    activity = Activity(
        user_id=current_user_id,
        activity_type='generate_trading_report',
        description=f'用户生成了交易报告，ID: {report.id}'
    )
    db.session.add(activity)
    db.session.commit()

    return jsonify({
        'message': '交易报告生成成功',
        'report': report.to_dict()
    }), 201

@report_bp.route('/compliance', methods=['POST'])
@jwt_required()
def generate_compliance_report():
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)

    if not user:
        return jsonify({'error': '用户不存在'}), 404

    data = request.get_json()

    # 验证必填字段
    required_fields = ['year']
    for field in required_fields:
        if field not in data:
            return jsonify({'error': f'缺少必填字段: {field}'}), 400

    year = data['year']

    # 获取排放数据和配额
    if user.role == 'admin' and 'enterprise_id' in data:
        enterprise = User.query.get(data['enterprise_id'])
        if not enterprise or enterprise.role != 'enterprise':
            return jsonify({'error': '企业不存在'}), 404

        # 使用Python的日期处理而不是数据库函数来过滤年份
        all_emissions = EmissionData.query.filter(
            EmissionData.enterprise_id == data['enterprise_id'],
            EmissionData.status == 'verified'
        ).all()

        # 过滤出指定年份的排放数据
        emissions = [
            emission for emission in all_emissions
            if emission.emission_period_start.year == year
        ]

        quota = CarbonQuota.query.filter_by(
            enterprise_id=data['enterprise_id'],
            year=year
        ).first()
    elif user.role == 'enterprise':
        enterprise = user
        # 使用Python的日期处理而不是数据库函数来过滤年份
        all_emissions = EmissionData.query.filter(
            EmissionData.enterprise_id == current_user_id,
            EmissionData.status == 'verified'
        ).all()

        # 过滤出指定年份的排放数据
        emissions = [
            emission for emission in all_emissions
            if emission.emission_period_start.year == year
        ]

        quota = CarbonQuota.query.filter_by(
            enterprise_id=current_user_id,
            year=year
        ).first()
    else:
        return jsonify({'error': '无权生成合规报告'}), 403

    # 如果没有数据或配额，返回错误
    if not emissions:
        return jsonify({'error': f'{year}年没有已核查的排放数据'}), 400

    if not quota:
        return jsonify({'error': f'{year}年没有配额记录'}), 400

    # 生成报告内容
    period_start = datetime(year, 1, 1).date()
    period_end = datetime(year, 12, 31).date()

    report_content = current_app.report_generator.generate_compliance_report(
        enterprise,
        emissions,
        quota,
        period_start,
        period_end
    )

    # 保存报告
    report = Report(
        enterprise_id=enterprise.id,
        title=report_content['title'],
        report_type='compliance',
        created_at=datetime.now(),
        period_start=period_start,
        period_end=period_end
    )
    report.set_content(report_content)

    db.session.add(report)
    db.session.commit()

    # 记录活动
    activity = Activity(
        user_id=current_user_id,
        activity_type='generate_compliance_report',
        description=f'用户生成了合规报告，ID: {report.id}'
    )
    db.session.add(activity)
    db.session.commit()

    return jsonify({
        'message': '合规报告生成成功',
        'report': report.to_dict()
    }), 201

@report_bp.route('', methods=['GET'])
@jwt_required()
def get_reports():
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)

    if not user:
        return jsonify({'error': '用户不存在'}), 404

    # 根据用户角色过滤报告
    if user.role == 'admin':
        # 管理员可以查看所有报告
        reports = Report.query.all()
    elif user.role == 'enterprise':
        # 企业用户只能查看自己的报告
        reports = Report.query.filter_by(enterprise_id=user.id).all()
    else:
        return jsonify({'error': '无权查看报告'}), 403

    return jsonify({
        'reports': [report.to_dict() for report in reports]
    }), 200

@report_bp.route('/<int:report_id>', methods=['GET'])
@jwt_required()
def get_report(report_id):
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)

    if not user:
        return jsonify({'error': '用户不存在'}), 404

    report = Report.query.get(report_id)
    if not report:
        return jsonify({'error': '报告不存在'}), 404

    # 检查权限
    if user.role == 'enterprise' and report.enterprise_id != user.id:
        return jsonify({'error': '无权查看此报告'}), 403

    return jsonify({
        'report': report.to_dict()
    }), 200
