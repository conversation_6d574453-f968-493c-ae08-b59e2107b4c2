.carbon-trading {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.carbon-trading h2 {
  text-align: center;
  margin-bottom: 30px;
  color: #2c3e50;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.success-message {
  background-color: #d4edda;
  color: #155724;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.trading-container {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  margin-bottom: 30px;
}

.quota-info {
  flex: 1;
  min-width: 250px;
}

.quota-card {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.quota-amount {
  font-size: 36px;
  font-weight: bold;
  color: #28a745;
  margin-bottom: 10px;
}

.quota-amount span {
  font-size: 18px;
  color: #6c757d;
}

.quota-label {
  color: #6c757d;
  font-size: 14px;
}

.trading-form {
  flex: 2;
  min-width: 300px;
}

.trading-form h3,
.quota-info h3,
.transactions-list h3,
.market-info h3 {
  margin-bottom: 20px;
  color: #2c3e50;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 10px;
}

.trading-form form {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #495057;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 16px;
}

.total-price .calculated-value {
  font-size: 18px;
  font-weight: bold;
  color: #28a745;
  padding: 10px 0;
}

.submit-btn {
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 12px 20px;
  font-size: 16px;
  cursor: pointer;
  width: 100%;
  margin-top: 10px;
}

.submit-btn:hover {
  background-color: #0069d9;
}

.submit-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.transactions-list {
  margin-bottom: 30px;
}

.transactions-list table {
  width: 100%;
  border-collapse: collapse;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.transactions-list th,
.transactions-list td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.transactions-list th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #495057;
}

.transactions-list tr:hover {
  background-color: #f8f9fa;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.pending {
  background-color: #ffeeba;
  color: #856404;
}

.status-badge.completed {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.cancelled {
  background-color: #f8d7da;
  color: #721c24;
}

.action-buttons {
  display: flex;
  gap: 5px;
}

.confirm-btn,
.cancel-btn {
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 12px;
  cursor: pointer;
}

.confirm-btn {
  background-color: #28a745;
  color: white;
}

.confirm-btn:hover {
  background-color: #218838;
}

.cancel-btn {
  background-color: #dc3545;
  color: white;
}

.cancel-btn:hover {
  background-color: #c82333;
}

.no-actions {
  color: #6c757d;
  font-style: italic;
  font-size: 12px;
}

.loading,
.no-data {
  text-align: center;
  padding: 30px;
  color: #6c757d;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.market-info {
  margin-bottom: 30px;
}

.market-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.stat-card {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex: 1;
  min-width: 200px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #007bff;
  margin-bottom: 10px;
}

.stat-label {
  color: #6c757d;
  font-size: 14px;
}

@media (max-width: 768px) {
  .trading-container {
    flex-direction: column;
  }
  
  .transactions-list {
    overflow-x: auto;
  }
  
  .market-stats {
    flex-direction: column;
  }
}
