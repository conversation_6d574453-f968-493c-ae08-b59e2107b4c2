"""
智能合约部署脚本
用于将CarbonEmission.sol合约部署到Ganache本地区块链
"""

import os
import json
import argparse
import sys
from web3 import Web3
from web3.middleware import geth_poa_middleware
from eth_account import Account
from dotenv import load_dotenv

# 尝试导入solcx，如果不存在则提供安装指导
try:
    from solcx import compile_source, install_solc

    SOLCX_AVAILABLE = True
except ImportError:
    SOLCX_AVAILABLE = False
    print("警告: 未安装py-solc-x模块，无法编译智能合约")
    print("请运行以下命令安装:")
    print("pip install py-solc-x==1.1.1")

# 加载环境变量
load_dotenv()


def compile_contract(contract_path):
    """编译智能合约"""
    print(f"编译合约: {contract_path}")

    # 检查是否安装了solcx
    if not SOLCX_AVAILABLE:
        print("错误: 未安装py-solc-x模块，无法编译智能合约")
        print("请运行以下命令安装:")
        print("pip install py-solc-x==1.1.1")

        # 检查是否存在预编译的合约
        artifacts_path = 'blockchain/contracts/artifacts/CarbonEmission_metadata.json'
        bin_path = 'blockchain/contracts/artifacts/CarbonEmission.bin'

        if os.path.exists(artifacts_path) and os.path.exists(bin_path):
            print(f"找到预编译的合约文件: {artifacts_path}")

            # 加载ABI
            with open(artifacts_path, 'r') as f:
                contract_metadata = json.load(f)
                abi = contract_metadata['output']['abi']

            # 加载字节码
            with open(bin_path, 'r') as f:
                bytecode = f.read().strip()

            return {'abi': abi, 'bin': bytecode}
        else:
            print("错误: 未找到预编译的合约文件")
            print("请先安装py-solc-x并重新运行此脚本，或者手动编译合约")
            sys.exit(1)

    # 确保solc编译器已安装
    try:
        install_solc(version='0.8.0')
    except Exception as e:
        print(f"安装solc编译器失败: {str(e)}")
        print("尝试使用已安装的编译器...")

    # 读取合约源代码
    with open(contract_path, 'r') as file:
        contract_source = file.read()

    # 编译合约
    compiled_sol = compile_source(
        contract_source,
        output_values=['abi', 'bin'],
        solc_version='0.8.0'
    )

    # 获取合约接口
    _, contract_interface = compiled_sol.popitem()

    # 保存ABI到文件
    os.makedirs('blockchain/contracts/artifacts', exist_ok=True)
    with open('blockchain/contracts/artifacts/CarbonEmission_metadata.json', 'w') as f:
        json.dump({
            'output': {
                'abi': contract_interface['abi']
            }
        }, f, indent=2)

    # 保存字节码到文件
    with open('blockchain/contracts/artifacts/CarbonEmission.bin', 'w') as f:
        f.write(contract_interface['bin'])

    print(f"合约ABI已保存到: blockchain/contracts/artifacts/CarbonEmission_metadata.json")
    print(f"合约字节码已保存到: blockchain/contracts/artifacts/CarbonEmission.bin")

    return contract_interface


def deploy_contract(contract_interface, private_key, node_url):
    """部署智能合约"""
    print(f"连接到以太坊节点: {node_url}")

    # 连接到以太坊节点
    w3 = Web3(Web3.HTTPProvider(node_url))

    # 如果使用的是PoA共识的网络，需要添加这个中间件
    w3.middleware_onion.inject(geth_poa_middleware, layer=0)

    # 检查连接
    if not w3.is_connected():
        raise Exception("无法连接到以太坊节点")

    print(f"成功连接到以太坊节点，当前区块号: {w3.eth.block_number}")

    # 创建账户
    account = Account.from_key(private_key)
    address = account.address

    print(f"使用账户地址: {address}")

    # 获取nonce
    nonce = w3.eth.get_transaction_count(address)

    # 创建合约
    contract = w3.eth.contract(
        abi=contract_interface['abi'],
        bytecode=contract_interface['bin']
    )

    # 构建部署交易
    transaction = contract.constructor().build_transaction({
        'from': address,
        'nonce': nonce,
        'gas': 5000000,
        'gasPrice': w3.eth.gas_price
    })

    # 签名交易
    signed_txn = w3.eth.account.sign_transaction(transaction, private_key=private_key)

    # 发送交易
    print("发送部署交易...")
    tx_hash = w3.eth.send_raw_transaction(signed_txn.rawTransaction)

    # 等待交易被确认
    print(f"等待交易确认，交易哈希: {tx_hash.hex()}")
    tx_receipt = w3.eth.wait_for_transaction_receipt(tx_hash)

    # 获取合约地址
    contract_address = tx_receipt.contractAddress

    print(f"合约已部署，地址: {contract_address}")
    print(f"交易哈希: {tx_hash.hex()}")
    print(f"区块号: {tx_receipt.blockNumber}")
    print(f"Gas使用量: {tx_receipt.gasUsed}")

    return contract_address


def register_roles(contract_address, private_key, node_url, enterprise_addresses, verifier_addresses):
    """注册企业和核查机构角色"""
    print(f"连接到以太坊节点: {node_url}")

    # 连接到以太坊节点
    w3 = Web3(Web3.HTTPProvider(node_url))

    # 如果使用的是PoA共识的网络，需要添加这个中间件
    w3.middleware_onion.inject(geth_poa_middleware, layer=0)

    # 检查连接
    if not w3.is_connected():
        raise Exception("无法连接到以太坊节点")

    # 创建账户
    account = Account.from_key(private_key)
    address = account.address

    print(f"使用管理员账户地址: {address}")

    # 加载合约ABI
    with open('blockchain/contracts/artifacts/CarbonEmission_metadata.json', 'r') as f:
        contract_metadata = json.load(f)
        contract_abi = contract_metadata['output']['abi']

    # 创建合约实例
    contract = w3.eth.contract(
        address=w3.to_checksum_address(contract_address),
        abi=contract_abi
    )

    # 管理员已在合约构造函数中自动注册
    print(f"管理员已在合约构造函数中自动注册: {address}")

    # 注册企业
    for i, enterprise_address in enumerate(enterprise_addresses):
        print(f"注册企业 {i + 1}: {enterprise_address}")
        nonce = w3.eth.get_transaction_count(address)
        tx = contract.functions.registerEnterprise(w3.to_checksum_address(enterprise_address)).build_transaction({
            'from': address,
            'nonce': nonce,
            'gas': 200000,
            'gasPrice': w3.eth.gas_price
        })
        signed_tx = w3.eth.account.sign_transaction(tx, private_key=private_key)
        tx_hash = w3.eth.send_raw_transaction(signed_tx.rawTransaction)
        w3.eth.wait_for_transaction_receipt(tx_hash)

    # 注册核查机构
    for i, verifier_address in enumerate(verifier_addresses):
        print(f"注册核查机构 {i + 1}: {verifier_address}")
        nonce = w3.eth.get_transaction_count(address)
        tx = contract.functions.registerVerifier(w3.to_checksum_address(verifier_address)).build_transaction({
            'from': address,
            'nonce': nonce,
            'gas': 200000,
            'gasPrice': w3.eth.gas_price
        })
        signed_tx = w3.eth.account.sign_transaction(tx, private_key=private_key)
        tx_hash = w3.eth.send_raw_transaction(signed_tx.rawTransaction)
        w3.eth.wait_for_transaction_receipt(tx_hash)

    print("角色注册完成")


def update_env_file(contract_address):
    """更新.env文件中的合约地址"""
    if os.path.exists('.env'):
        with open('.env', 'r') as f:
            env_content = f.read()

        # 替换CONTRACT_ADDRESS
        if 'CONTRACT_ADDRESS=' in env_content:
            env_content = env_content.replace(
                'CONTRACT_ADDRESS=******************************************',
                f'CONTRACT_ADDRESS={contract_address}'
            )
        else:
            env_content += f'\nCONTRACT_ADDRESS={contract_address}\n'

        with open('.env', 'w') as f:
            f.write(env_content)

        print(f".env文件已更新，CONTRACT_ADDRESS={contract_address}")
    else:
        print("警告: .env文件不存在，请手动创建并设置CONTRACT_ADDRESS")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='部署CarbonEmission智能合约到Ganache')
    parser.add_argument('--contract', default='blockchain/contracts/CarbonEmission.sol', help='合约文件路径')
    parser.add_argument('--node', default=os.getenv('ETHEREUM_NODE_URL', 'http://127.0.0.1:8545'), help='以太坊节点URL')
    parser.add_argument('--key', default=os.getenv('ADMIN_PRIVATE_KEY'), help='部署账户私钥')
    args = parser.parse_args()

    if not args.key:
        print("错误: 未提供部署账户私钥，请在.env文件中设置ADMIN_PRIVATE_KEY或使用--key参数")
        return

    try:
        # 编译合约
        contract_interface = compile_contract(args.contract)

        # 部署合约
        contract_address = deploy_contract(contract_interface, args.key, args.node)

        # 更新.env文件
        update_env_file(contract_address)

        # 获取企业和核查机构地址
        enterprise_addresses = [
            os.getenv('ENTERPRISE_1_ADDRESS'),
            os.getenv('ENTERPRISE_2_ADDRESS')
        ]
        enterprise_addresses = [addr for addr in enterprise_addresses if
                                addr and addr != '******************************************']

        verifier_addresses = [
            os.getenv('VERIFIER_1_ADDRESS')
        ]
        verifier_addresses = [addr for addr in verifier_addresses if
                              addr and addr != '******************************************']

        # 注册角色
        if enterprise_addresses or verifier_addresses:
            register_roles(contract_address, args.key, args.node, enterprise_addresses, verifier_addresses)

        print("\n部署完成！")
        print(f"合约地址: {contract_address}")
        print("请确保在.env文件中设置了正确的CONTRACT_ADDRESS")

    except Exception as e:
        print(f"部署失败: {str(e)}")


if __name__ == '__main__':
    main()
