# 应用配置
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here

# 数据库配置
DATABASE_URL=mysql+pymysql://wuhong:D7mH8rZ7a7Z2kJa8@***********:3306/ces?charset=utf8mb4

# 区块链配置 - Ganache
ETHEREUM_NODE_URL=http://127.0.0.1:8545  # Ganache部署位置
CONTRACT_ADDRESS=******************************************  # 部署合约后填写实际地址

# Ganache默认提供的第一个账户作为管理员
ADMIN_PRIVATE_KEY=******************************************000000000000000000000000  # Ganache中第一个账户的私钥
ADMIN_ADDRESS=******************************************  # Ganache中第一个账户的地址

# 企业账户配置 - 使用Ganache提供的账户
ENTERPRISE_1_KEY=******************************************000000000000000000000000  # Ganache中第二个账户的私钥
ENTERPRISE_1_ADDRESS=******************************************  # Ganache中第二个账户的地址

ENTERPRISE_2_KEY=******************************************000000000000000000000000  # Ganache中第三个账户的私钥
ENTERPRISE_2_ADDRESS=******************************************  # Ganache中第三个账户的地址

# 核查机构账户配置 - 使用Ganache提供的账户
VERIFIER_1_KEY=******************************************000000000000000000000000  # Ganache中第四个账户的私钥
VERIFIER_1_ADDRESS=******************************************  # Ganache中第四个账户的地址

# 文件上传配置
UPLOAD_FOLDER=uploads