"""
检查数据库结构
"""

import pymysql
import sys

# 数据库配置
DB_CONFIG = {
    'host': '***********',
    'port': 3306,
    'user': 'wuhong',
    'password': 'D7mH8rZ7a7Z2kJa8',
    'db': 'ces',
    'charset': 'utf8mb4'
}

def check_database_structure():
    """检查数据库结构"""
    try:
        # 连接数据库
        print(f"正在连接到数据库 {DB_CONFIG['db']} @ {DB_CONFIG['host']}...")
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 获取表列表
        cursor.execute('SHOW TABLES')
        tables = cursor.fetchall()
        
        print(f"\n数据库 {DB_CONFIG['db']} 中的表:")
        print("=" * 40)
        
        if not tables:
            print("数据库中没有表！")
        else:
            for i, table in enumerate(tables):
                print(f"{i+1}. {table[0]}")
                
                # 获取表结构
                cursor.execute(f'DESCRIBE {table[0]}')
                columns = cursor.fetchall()
                
                print(f"   表 {table[0]} 的结构:")
                for column in columns:
                    print(f"   - {column[0]} ({column[1]})")
                print()
        
        # 关闭连接
        conn.close()
        print("=" * 40)
        print("数据库检查完成")
        return True
    except Exception as e:
        print(f"检查数据库结构时出错: {str(e)}")
        return False

if __name__ == "__main__":
    success = check_database_structure()
    sys.exit(0 if success else 1)
