import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';
import BlockchainInfo from '../../components/BlockchainInfo';
import BlockchainVerification from '../../components/BlockchainVerification';
import '../../styles/EmissionDetail.css';

/**
 * 排放数据详情页面
 * 展示排放数据的详细信息，包括区块链信息
 */
const EmissionDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [emission, setEmission] = useState(null);
  const [verification, setVerification] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchEmissionData();
  }, [id]);

  const fetchEmissionData = async () => {
    setLoading(true);
    try {
      // 获取排放数据
      const emissionResponse = await axios.get(`/api/emissions/${id}`, {
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      
      setEmission(emissionResponse.data.emission);
      
      // 如果有核查记录，获取核查记录
      if (emissionResponse.data.emission.verification_id) {
        const verificationResponse = await axios.get(`/api/verifications/${emissionResponse.data.emission.verification_id}`, {
          headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
        });
        
        setVerification(verificationResponse.data.verification);
      }
      
      setError('');
    } catch (err) {
      console.error('获取排放数据失败:', err);
      setError('获取排放数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 格式化日期
  const formatDate = (dateString) => {
    if (!dateString) return '';
    
    try {
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch (e) {
      return dateString;
    }
  };

  // 获取状态标签和颜色
  const getStatusLabel = (status) => {
    switch (status) {
      case 'pending':
        return { label: '待核查', color: 'orange' };
      case 'approved':
        return { label: '已通过', color: 'green' };
      case 'rejected':
        return { label: '已拒绝', color: 'red' };
      default:
        return { label: status, color: 'gray' };
    }
  };

  if (loading) {
    return (
      <div className="emission-detail-loading">
        <i className="fas fa-spinner fa-spin"></i>
        <span>加载中...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="emission-detail-error">
        <i className="fas fa-exclamation-triangle"></i>
        <span>{error}</span>
        <button onClick={() => navigate('/emissions')} className="btn-back">
          返回列表
        </button>
      </div>
    );
  }

  if (!emission) {
    return (
      <div className="emission-detail-not-found">
        <i className="fas fa-search"></i>
        <span>未找到排放数据</span>
        <button onClick={() => navigate('/emissions')} className="btn-back">
          返回列表
        </button>
      </div>
    );
  }

  const statusInfo = getStatusLabel(emission.status);

  return (
    <div className="emission-detail">
      <div className="detail-header">
        <h2>排放数据详情</h2>
        <button onClick={() => navigate('/emissions')} className="btn-back">
          <i className="fas fa-arrow-left"></i> 返回列表
        </button>
      </div>

      {/* 基本信息 */}
      <div className="detail-section">
        <h3 className="section-title">基本信息</h3>
        <div className="detail-grid">
          <div className="detail-item">
            <span className="detail-label">ID</span>
            <span className="detail-value">{emission.id}</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">排放源</span>
            <span className="detail-value">{emission.emission_source}</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">排放量</span>
            <span className="detail-value">{emission.emission_amount} {emission.emission_unit || '吨CO2e'}</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">计算方法</span>
            <span className="detail-value">{emission.calculation_method}</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">提交时间</span>
            <span className="detail-value">{formatDate(emission.submission_time)}</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">状态</span>
            <span className="detail-value">
              <span className="status-badge" style={{ backgroundColor: statusInfo.color }}>
                {statusInfo.label}
              </span>
            </span>
          </div>
        </div>
      </div>

      {/* 区块链信息 */}
      {emission.blockchain_hash && (
        <div className="detail-section">
          <h3 className="section-title">区块链信息</h3>
          <BlockchainInfo
            transactionHash={emission.blockchain_hash}
            blockNumber={emission.blockchain_block}
            timestamp={emission.submission_time}
            status="confirmed"
            type="emission"
          />
          <BlockchainVerification
            dataId={emission.id}
            dataType="emission"
          />
        </div>
      )}

      {/* 核查信息 */}
      {verification && (
        <div className="detail-section">
          <h3 className="section-title">核查信息</h3>
          <div className="detail-grid">
            <div className="detail-item">
              <span className="detail-label">核查机构</span>
              <span className="detail-value">{verification.verifier_name}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">核查时间</span>
              <span className="detail-value">{formatDate(verification.verification_time)}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">核查结论</span>
              <span className="detail-value">
                <span className="status-badge" style={{ 
                  backgroundColor: verification.conclusion === 'approved' ? 'green' : 'red' 
                }}>
                  {verification.conclusion === 'approved' ? '通过' : '拒绝'}
                </span>
              </span>
            </div>
            <div className="detail-item detail-item-full">
              <span className="detail-label">核查意见</span>
              <span className="detail-value detail-comments">{verification.comments}</span>
            </div>
          </div>

          {/* 核查区块链信息 */}
          {verification.blockchain_hash && (
            <div className="verification-blockchain">
              <h4>核查区块链记录</h4>
              <BlockchainInfo
                transactionHash={verification.blockchain_hash}
                blockNumber={verification.blockchain_block}
                timestamp={verification.verification_time}
                status="confirmed"
                type="verification"
              />
              <BlockchainVerification
                dataId={verification.id}
                dataType="verification"
              />
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default EmissionDetail;
