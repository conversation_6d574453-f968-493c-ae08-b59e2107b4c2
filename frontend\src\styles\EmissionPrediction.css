.emission-prediction {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.emission-prediction h2 {
  text-align: center;
  margin-bottom: 30px;
  color: #2c3e50;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.success-message {
  background-color: #d4edda;
  color: #155724;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.prediction-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 30px;
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.control-group {
  flex: 1;
  min-width: 200px;
}

.control-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #495057;
}

.control-group select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 16px;
}

.refresh-btn,
.train-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  align-self: flex-end;
}

.refresh-btn {
  background-color: #17a2b8;
  color: white;
}

.refresh-btn:hover {
  background-color: #138496;
}

.train-btn {
  background-color: #28a745;
  color: white;
}

.train-btn:hover {
  background-color: #218838;
}

.refresh-btn:disabled,
.train-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.loading,
.no-data {
  text-align: center;
  padding: 50px;
  color: #6c757d;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 30px;
}

.prediction-charts {
  margin-bottom: 30px;
}

.chart-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.prediction-summary {
  margin-bottom: 30px;
}

.prediction-summary h3,
.model-info h3 {
  margin-bottom: 20px;
  color: #2c3e50;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 10px;
}

.summary-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.summary-card {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex: 1;
  min-width: 200px;
}

.summary-value {
  font-size: 24px;
  font-weight: bold;
  color: #007bff;
  margin-bottom: 10px;
}

.summary-label {
  color: #6c757d;
  font-size: 14px;
}

.model-info {
  margin-bottom: 30px;
}

.model-info table {
  width: 100%;
  border-collapse: collapse;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.model-info th,
.model-info td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.model-info th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #495057;
}

.model-info tr:hover {
  background-color: #f8f9fa;
}

@media (max-width: 768px) {
  .prediction-controls {
    flex-direction: column;
  }
  
  .summary-cards {
    flex-direction: column;
  }
  
  .model-info {
    overflow-x: auto;
  }
}
