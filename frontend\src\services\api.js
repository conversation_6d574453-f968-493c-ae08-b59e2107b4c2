import axios from 'axios';

// 创建 axios 实例
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => response.data,
  (error) => {
    if (error.response) {
      // 处理 401 未授权错误
      if (error.response.status === 401) {
        localStorage.removeItem('token');
        window.location.href = '/login';
      }
      return Promise.reject(error.response.data);
    }
    return Promise.reject({ error: '网络错误' });
  }
);

// API 端点
export const endpoints = {
  auth: {
    login: '/auth/login',
    register: '/auth/register',
    profile: '/auth/profile',
    changePassword: '/auth/change-password'
  },
  emissions: {
    list: '/emissions',
    detail: (id) => `/emissions/${id}`,
    create: '/emissions'
  },
  verifications: {
    list: '/verifications',
    detail: (id) => `/verifications/${id}`,
    submit: '/verifications'
  },
  admin: {
    users: '/admin/users',
    user: (id) => `/admin/users/${id}`,
    stats: '/admin/stats',
    activities: '/admin/activities',
    config: '/admin/system-config'
  },
  dashboard: '/dashboard'
};

// API 方法
export const authAPI = {
  login: (credentials) => api.post(endpoints.auth.login, credentials),
  register: (userData) => api.post(endpoints.auth.register, userData),
  getProfile: () => api.get(endpoints.auth.profile),
  updateProfile: (data) => api.put(endpoints.auth.profile, data),
  changePassword: (data) => api.post(endpoints.auth.changePassword, data)
};

export const emissionsAPI = {
  getAll: (params) => api.get(endpoints.emissions.list, { params }),
  getById: (id) => api.get(endpoints.emissions.detail(id)),
  create: (data) => api.post(endpoints.emissions.create, data)
};

export const verificationsAPI = {
  getAll: (params) => api.get(endpoints.verifications.list, { params }),
  getById: (id) => api.get(endpoints.verifications.detail(id)),
  submit: (data) => api.post(endpoints.verifications.submit, data)
};

export const adminAPI = {
  getUsers: (params) => api.get(endpoints.admin.users, { params }),
  getUser: (id) => api.get(endpoints.admin.user(id)),
  createUser: (data) => api.post(endpoints.admin.users, data),
  updateUser: (id, data) => api.put(endpoints.admin.user(id), data),
  deleteUser: (id) => api.delete(endpoints.admin.user(id)),
  getStats: () => api.get(endpoints.admin.stats),
  getActivities: (params) => api.get(endpoints.admin.activities, { params }),
  getConfig: () => api.get(endpoints.admin.config),
  updateConfig: (key, data) => api.put(`${endpoints.admin.config}/${key}`, data)
};

export const dashboardAPI = {
  getData: () => api.get(endpoints.dashboard)
};

export default api;

