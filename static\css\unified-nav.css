/**
 * 碳排放核查系统统一导航栏样式
 * 绿色环保主题
 */

/* 头部样式 */
.demo-header {
    background: linear-gradient(to right, #1B5E20, #388E3C);
    color: white;
    padding: 20px 0;
    box-shadow: 0 4px 10px rgba(0,0,0,0.2);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.logo {
    font-size: 24px;
    font-weight: bold;
}

.user-info {
    display: flex;
    align-items: center;
}

.user-name {
    margin-right: 15px;
    font-weight: 500;
}

.logout-btn {
    background: linear-gradient(to right, #e74c3c, #c0392b);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 30px;
    cursor: pointer;
    text-decoration: none;
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.logout-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 7px 15px rgba(0,0,0,0.2);
    color: white;
}

/* 导航栏样式 */
.demo-nav {
    background: linear-gradient(to right, #2E7D32, #43A047);
    padding: 0;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.nav-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    overflow-x: auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.nav-content::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.nav-item {
    color: white;
    text-decoration: none;
    padding: 15px 20px;
    margin-right: 5px;
    border-radius: 0;
    transition: all 0.3s ease;
    white-space: nowrap;
    font-weight: 500;
    font-size: 16px;
    letter-spacing: 0.5px;
    position: relative;
    display: flex;
    align-items: center;
}

.nav-item i {
    margin-right: 8px;
    font-size: 18px;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.nav-item.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-weight: 600;
}

.nav-item.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 20%;
    right: 20%;
    height: 3px;
    background-color: white;
    border-radius: 3px 3px 0 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .nav-item {
        padding: 12px 15px;
        font-size: 14px;
    }
    
    .nav-item i {
        margin-right: 5px;
        font-size: 16px;
    }
    
    .logo {
        font-size: 20px;
    }
    
    .user-name {
        font-size: 14px;
    }
    
    .logout-btn {
        padding: 6px 12px;
        font-size: 14px;
    }
}
