-- 创建用户表
CREATE TABLE IF NOT EXISTS `user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(80) NOT NULL,
  `email` varchar(120) NOT NULL,
  `password_hash` varchar(512) NOT NULL,
  `role` varchar(20) NOT NULL,
  `company_name` varchar(100) DEFAULT NULL,
  `credit_code` varchar(50) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `last_login` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建系统配置表
CREATE TABLE IF NOT EXISTS `system_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key` varchar(50) NOT NULL,
  `value` varchar(255) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `key` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建活动记录表
CREATE TABLE IF NOT EXISTS `activity` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `activity_type` varchar(50) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `timestamp` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `activity_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建碳配额表
CREATE TABLE IF NOT EXISTS `carbon_quota` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `enterprise_id` int(11) NOT NULL,
  `year` int(11) NOT NULL,
  `initial_amount` float NOT NULL,
  `current_amount` float NOT NULL,
  `last_updated` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `enterprise_id_year` (`enterprise_id`, `year`),
  CONSTRAINT `carbon_quota_ibfk_1` FOREIGN KEY (`enterprise_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建排放数据表
CREATE TABLE IF NOT EXISTS `emission_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `enterprise_id` int(11) NOT NULL,
  `emission_source` varchar(100) NOT NULL,
  `emission_amount` float NOT NULL,
  `emission_unit` varchar(20) NOT NULL,
  `calculation_method` varchar(50) NOT NULL,
  `emission_period_start` date NOT NULL,
  `emission_period_end` date NOT NULL,
  `status` varchar(20) NOT NULL,
  `submission_time` datetime NOT NULL,
  `proof_file_path` varchar(255) DEFAULT NULL,
  `blockchain_hash` varchar(255) DEFAULT NULL,
  `blockchain_block` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `enterprise_id` (`enterprise_id`),
  CONSTRAINT `emission_data_ibfk_1` FOREIGN KEY (`enterprise_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建核查记录表
CREATE TABLE IF NOT EXISTS `verification` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `emission_data_id` int(11) NOT NULL,
  `verifier_id` int(11) NOT NULL,
  `conclusion` varchar(20) NOT NULL,
  `comments` text,
  `verification_time` datetime NOT NULL,
  `blockchain_hash` varchar(255) DEFAULT NULL,
  `blockchain_block` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `emission_data_id` (`emission_data_id`),
  KEY `verifier_id` (`verifier_id`),
  CONSTRAINT `verification_ibfk_1` FOREIGN KEY (`emission_data_id`) REFERENCES `emission_data` (`id`),
  CONSTRAINT `verification_ibfk_2` FOREIGN KEY (`verifier_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建交易记录表
CREATE TABLE IF NOT EXISTS `transaction` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `seller_id` int(11) NOT NULL,
  `buyer_id` int(11) NOT NULL,
  `amount` float NOT NULL,
  `price` float NOT NULL,
  `total_price` float NOT NULL,
  `transaction_time` datetime NOT NULL,
  `status` varchar(20) NOT NULL,
  `blockchain_hash` varchar(255) DEFAULT NULL,
  `blockchain_block` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `seller_id` (`seller_id`),
  KEY `buyer_id` (`buyer_id`),
  CONSTRAINT `transaction_ibfk_1` FOREIGN KEY (`seller_id`) REFERENCES `user` (`id`),
  CONSTRAINT `transaction_ibfk_2` FOREIGN KEY (`buyer_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建惩罚记录表
CREATE TABLE IF NOT EXISTS `penalty` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `enterprise_id` int(11) NOT NULL,
  `amount` float NOT NULL,
  `reason` varchar(255) NOT NULL,
  `issue_date` datetime NOT NULL,
  `status` varchar(20) NOT NULL,
  `blockchain_hash` varchar(255) DEFAULT NULL,
  `blockchain_block` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `enterprise_id` (`enterprise_id`),
  CONSTRAINT `penalty_ibfk_1` FOREIGN KEY (`enterprise_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建碳计算记录表
CREATE TABLE IF NOT EXISTS `carbon_calculation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `enterprise_id` int(11) NOT NULL,
  `calculation_time` datetime NOT NULL,
  `input_data` text NOT NULL,
  `result_total` float NOT NULL,
  `result_breakdown` text NOT NULL,
  PRIMARY KEY (`id`),
  KEY `enterprise_id` (`enterprise_id`),
  CONSTRAINT `carbon_calculation_ibfk_1` FOREIGN KEY (`enterprise_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建预测模型表
CREATE TABLE IF NOT EXISTS `prediction_model` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text,
  `model_path` varchar(255) NOT NULL,
  `metrics` text,
  `created_at` datetime NOT NULL,
  `last_trained` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建报告表
CREATE TABLE IF NOT EXISTS `report` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `enterprise_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `report_type` varchar(50) NOT NULL,
  `content` text NOT NULL,
  `file_path` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `period_start` date NOT NULL,
  `period_end` date NOT NULL,
  PRIMARY KEY (`id`),
  KEY `enterprise_id` (`enterprise_id`),
  CONSTRAINT `report_ibfk_1` FOREIGN KEY (`enterprise_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 插入管理员用户
INSERT INTO `user` (`username`, `email`, `password_hash`, `role`, `company_name`, `credit_code`, `created_at`)
VALUES ('admin', '<EMAIL>', '$2b$12$1xxxxxxxxxxxxxxxxxxxxuZLbwxnpY0o58unSvIPxddLxGystU.O', 'admin', '系统管理', '000000000000000000', NOW())
ON DUPLICATE KEY UPDATE `username` = `username`;

-- 插入系统配置
INSERT INTO `system_config` (`key`, `value`, `description`)
VALUES
('system_name', '碳排放管理系统', '系统名称'),
('system_version', '1.0.0', '系统版本'),
('admin_email', '<EMAIL>', '管理员邮箱'),
('verification_period', '30', '核查周期（天）'),
('carbon_price', '50', '碳价格（元/吨）')
ON DUPLICATE KEY UPDATE `key` = `key`;
