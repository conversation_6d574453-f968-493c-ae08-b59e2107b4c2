{"compiler": {"version": "0.8.26+commit.8a97fa7a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "enterprise", "type": "address"}], "name": "EmissionDataSubmitted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "enterprise", "type": "address"}], "name": "PenaltyCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}], "name": "TransactionCancelled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "seller", "type": "address"}], "name": "TransactionConfirmed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "buyer", "type": "address"}, {"indexed": true, "internalType": "address", "name": "seller", "type": "address"}], "name": "TransactionCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "verifier", "type": "address"}, {"indexed": true, "internalType": "address", "name": "enterprise", "type": "address"}], "name": "VerificationRecordCreated", "type": "event"}, {"inputs": [{"internalType": "uint256", "name": "transactionId", "type": "uint256"}], "name": "cancelTransaction", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "transactionId", "type": "uint256"}], "name": "confirmTransaction", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "enterprise", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "string", "name": "reason", "type": "string"}], "name": "createPenalty", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "seller", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "price", "type": "uint256"}], "name": "createTransaction", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "emissionData", "outputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "address", "name": "enterprise", "type": "address"}, {"internalType": "string", "name": "emissionSource", "type": "string"}, {"internalType": "uint256", "name": "emissionAmount", "type": "uint256"}, {"internalType": "string", "name": "calculationMethod", "type": "string"}, {"internalType": "uint256", "name": "submissionTime", "type": "uint256"}, {"internalType": "string", "name": "status", "type": "string"}, {"internalType": "string", "name": "proofFileHash", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "emissionDataCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "getEmissionData", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "string", "name": "", "type": "string"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "string", "name": "", "type": "string"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "string", "name": "", "type": "string"}, {"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "enterprise", "type": "address"}], "name": "getEnterpriseAllPenalties", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "get<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "string", "name": "", "type": "string"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "getTransaction", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "getVerificationRecord", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "string", "name": "", "type": "string"}, {"internalType": "string", "name": "", "type": "string"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "isAdmin", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "isEnterprise", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "isVerifier", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "penalties", "outputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "address", "name": "enterprise", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "string", "name": "reason", "type": "string"}, {"internalType": "uint256", "name": "penaltyTime", "type": "uint256"}, {"internalType": "string", "name": "status", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "penaltyCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "enterprise", "type": "address"}], "name": "registerEnterprise", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "verifier", "type": "address"}], "name": "registerVerifier", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "emissionSource", "type": "string"}, {"internalType": "uint256", "name": "emissionAmount", "type": "uint256"}, {"internalType": "string", "name": "calculationMethod", "type": "string"}, {"internalType": "string", "name": "proofFileHash", "type": "string"}], "name": "submitEmissionData", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "emissionDataId", "type": "uint256"}, {"internalType": "string", "name": "conclusion", "type": "string"}, {"internalType": "string", "name": "comments", "type": "string"}], "name": "submitVerification", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "transactionCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "transactions", "outputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "address", "name": "buyer", "type": "address"}, {"internalType": "address", "name": "seller", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "uint256", "name": "transactionTime", "type": "uint256"}, {"internalType": "string", "name": "status", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "verificationRecordCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "verificationRecords", "outputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "address", "name": "verifier", "type": "address"}, {"internalType": "address", "name": "enterprise", "type": "address"}, {"internalType": "uint256", "name": "emissionDataId", "type": "uint256"}, {"internalType": "string", "name": "conclusion", "type": "string"}, {"internalType": "string", "name": "comments", "type": "string"}, {"internalType": "uint256", "name": "verificationTime", "type": "uint256"}], "stateMutability": "view", "type": "function"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"compilationTarget": {"blockchain/contracts/CarbonEmission.sol": "CarbonEmission"}, "evmVersion": "cancun", "libraries": {}, "metadata": {"bytecodeHash": "ipfs"}, "optimizer": {"enabled": false, "runs": 200}, "remappings": []}, "sources": {"blockchain/contracts/CarbonEmission.sol": {"keccak256": "0x8672b3910d7da2a34972de63637e327e3562338ae4027fcb2066368211cea090", "license": "MIT", "urls": ["bzz-raw://7d28b520dc33589fadaf0cb1c60c151dd1895ee9667fb498469f65f8500e8a34", "dweb:/ipfs/Qma2iAffHEAxXLA42QFvu88Y2gEBqhnXaJS87GXsR2eq7n"]}}, "version": 1}