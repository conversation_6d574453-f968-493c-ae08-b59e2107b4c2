import Vue from 'vue'
import VueRouter from 'vue-router'
import Home from '../views/Home.vue'
import Login from '../views/Login.vue'
import Dashboard from '../views/Dashboard.vue'
import EmissionData from '../views/EmissionData.vue'
import EmissionDetail from '../views/EmissionDetail.vue'
import VerificationList from '../views/VerificationList.vue'
import VerificationDetail from '../views/VerificationDetail.vue'
import TransactionList from '../views/TransactionList.vue'
import TransactionDetail from '../views/TransactionDetail.vue'
import PenaltyList from '../views/PenaltyList.vue'
import PenaltyDetail from '../views/PenaltyDetail.vue'
import ReportList from '../views/ReportList.vue'
import ReportDetail from '../views/ReportDetail.vue'
import UserManagement from '../views/UserManagement.vue'
// 已废弃: import BlockchainConfig from '../views/BlockchainConfig.vue'
import store from '../store'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/login',
    name: 'Login',
    component: Login
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: { requiresAuth: true }
  },
  {
    path: '/emissions',
    name: 'EmissionData',
    component: EmissionData,
    meta: { requiresAuth: true }
  },
  {
    path: '/emissions/:id',
    name: 'EmissionDetail',
    component: EmissionDetail,
    meta: { requiresAuth: true }
  },
  {
    path: '/verifications',
    name: 'VerificationList',
    component: VerificationList,
    meta: { requiresAuth: true }
  },
  {
    path: '/verifications/:id',
    name: 'VerificationDetail',
    component: VerificationDetail,
    meta: { requiresAuth: true }
  },
  {
    path: '/transactions',
    name: 'TransactionList',
    component: TransactionList,
    meta: { requiresAuth: true }
  },
  {
    path: '/transactions/:id',
    name: 'TransactionDetail',
    component: TransactionDetail,
    meta: { requiresAuth: true }
  },
  {
    path: '/penalties',
    name: 'PenaltyList',
    component: PenaltyList,
    meta: { requiresAuth: true }
  },
  {
    path: '/penalties/:id',
    name: 'PenaltyDetail',
    component: PenaltyDetail,
    meta: { requiresAuth: true }
  },
  {
    path: '/reports',
    name: 'ReportList',
    component: ReportList,
    meta: { requiresAuth: true }
  },
  {
    path: '/reports/:id',
    name: 'ReportDetail',
    component: ReportDetail,
    meta: { requiresAuth: true }
  },
  {
    path: '/users',
    name: 'UserManagement',
    component: UserManagement,
    meta: { requiresAuth: true, requiresAdmin: true }
  },
  // 已废弃的区块链配置路由，使用后端路由 /admin/blockchain/config 替代
  // {
  //   path: '/blockchain/config',
  //   name: 'BlockchainConfig',
  //   component: BlockchainConfig,
  //   meta: { requiresAuth: true, requiresAdmin: true }
  // }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

router.beforeEach((to, from, next) => {
  const isLoggedIn = store.getters.isLoggedIn
  const userRole = store.getters.userRole

  if (to.matched.some(record => record.meta.requiresAuth)) {
    if (!isLoggedIn) {
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
    } else if (to.matched.some(record => record.meta.requiresAdmin) && userRole !== 'admin') {
      next({ path: '/dashboard' })
    } else {
      next()
    }
  } else {
    next()
  }
})

export default router
