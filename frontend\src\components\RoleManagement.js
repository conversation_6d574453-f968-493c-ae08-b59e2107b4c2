import React, { useState, useEffect } from 'react';
import axios from 'axios';
import '../styles/RoleManagement.css';

function RoleManagement() {
  const [roles, setRoles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    permissions: {
      dashboard: false,
      dataManagement: false,
      reportManagement: false,
      userManagement: false,
      roleManagement: false,
      systemSettings: false
    }
  });

  useEffect(() => {
    fetchRoles();
  }, []);

  const fetchRoles = async () => {
    try {
      const response = await axios.get('/api/roles', {
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      setRoles(response.data);
      setLoading(false);
    } catch (err) {
      setError('获取角色列表失败');
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await axios.post('/api/roles', formData, {
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      fetchRoles();
      setFormData({
        name: '',
        description: '',
        permissions: {
          dashboard: false,
          dataManagement: false,
          reportManagement: false,
          userManagement: false,
          roleManagement: false,
          systemSettings: false
        }
      });
    } catch (err) {
      setError('创建角色失败');
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handlePermissionChange = (permission) => {
    setFormData(prev => ({
      ...prev,
      permissions: {
        ...prev.permissions,
        [permission]: !prev.permissions[permission]
      }
    }));
  };

  const handleDelete = async (roleId) => {
    if (window.confirm('确定要删除这个角色吗？')) {
      try {
        await axios.delete(`/api/roles/${roleId}`, {
          headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
        });
        fetchRoles();
      } catch (err) {
        setError('删除角色失败');
      }
    }
  };

  const handleEdit = async (roleId, updatedPermissions) => {
    try {
      await axios.patch(`/api/roles/${roleId}`, 
        { permissions: updatedPermissions },
        { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } }
      );
      fetchRoles();
    } catch (err) {
      setError('更新角色权限失败');
    }
  };

  if (loading) return <div className="loading">加载中...</div>;

  return (
    <div className="role-management">
      <h2>角色管理</h2>
      {error && <div className="alert alert-danger">{error}</div>}
      
      <div className="role-form">
        <h3>创建新角色</h3>
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="name">角色名称</label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
              className="form-control"
            />
          </div>
          <div className="form-group">
            <label htmlFor="description">角色描述</label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              required
              className="form-control"
              rows="3"
            />
          </div>
          <div className="form-group">
            <label>权限设置</label>
            <div className="permissions-grid">
              <label className="permission-item">
                <input
                  type="checkbox"
                  checked={formData.permissions.dashboard}
                  onChange={() => handlePermissionChange('dashboard')}
                />
                仪表盘
              </label>
              <label className="permission-item">
                <input
                  type="checkbox"
                  checked={formData.permissions.dataManagement}
                  onChange={() => handlePermissionChange('dataManagement')}
                />
                数据管理
              </label>
              <label className="permission-item">
                <input
                  type="checkbox"
                  checked={formData.permissions.reportManagement}
                  onChange={() => handlePermissionChange('reportManagement')}
                />
                报告管理
              </label>
              <label className="permission-item">
                <input
                  type="checkbox"
                  checked={formData.permissions.userManagement}
                  onChange={() => handlePermissionChange('userManagement')}
                />
                用户管理
              </label>
              <label className="permission-item">
                <input
                  type="checkbox"
                  checked={formData.permissions.roleManagement}
                  onChange={() => handlePermissionChange('roleManagement')}
                />
                角色管理
              </label>
              <label className="permission-item">
                <input
                  type="checkbox"
                  checked={formData.permissions.systemSettings}
                  onChange={() => handlePermissionChange('systemSettings')}
                />
                系统设置
              </label>
            </div>
          </div>
          <button type="submit" className="btn btn-primary">
            创建角色
          </button>
        </form>
      </div>

      <div className="role-list">
        <h3>角色列表</h3>
        <div className="table-responsive">
          <table className="table">
            <thead>
              <tr>
                <th>角色名称</th>
                <th>描述</th>
                <th>权限</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              {roles.map(role => (
                <tr key={role.id}>
                  <td>{role.name}</td>
                  <td>{role.description}</td>
                  <td>
                    <div className="permissions-list">
                      {Object.entries(role.permissions).map(([key, value]) => (
                        value && (
                          <span key={key} className="permission-tag">
                            {key === 'dashboard' ? '仪表盘' :
                             key === 'dataManagement' ? '数据管理' :
                             key === 'reportManagement' ? '报告管理' :
                             key === 'userManagement' ? '用户管理' :
                             key === 'roleManagement' ? '角色管理' :
                             '系统设置'}
                          </span>
                        )
                      ))}
                    </div>
                  </td>
                  <td>
                    <div className="action-buttons">
                      <button
                        onClick={() => handleDelete(role.id)}
                        className="btn btn-danger"
                      >
                        删除
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default RoleManagement; 