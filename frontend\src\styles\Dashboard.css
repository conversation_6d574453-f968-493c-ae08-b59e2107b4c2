.dashboard {
  display: flex;
  min-height: 100vh;
  padding: 20px;
}

.dashboard h2 {
  margin-bottom: 30px;
  color: #333;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.users {
  background-color: #667eea;
}

.stat-icon.data {
  background-color: #10b981;
}

.stat-icon.system {
  background-color: #f59e0b;
}

.stat-content {
  flex: 1;
}

.stat-content h3 {
  margin: 0 0 15px 0;
  color: #444;
  font-size: 18px;
}

.stat-numbers {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.progress {
  height: 100%;
  background-color: #667eea;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  right: 0;
  top: -20px;
  font-size: 12px;
  color: #666;
}

.recent-activities {
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.recent-activities h3 {
  margin: 0 0 20px 0;
  color: #444;
  font-size: 18px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 10px;
  border-radius: 5px;
  transition: background-color 0.3s;
}

.activity-item:hover {
  background-color: #f8f9fa;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
  font-size: 18px;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 16px;
  color: #333;
  margin-bottom: 5px;
}

.activity-details {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 14px;
  color: #666;
}

.activity-user {
  color: #667eea;
  font-weight: 500;
}

.activity-time {
  color: #999;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: 18px;
  color: #666;
}

.alert {
  padding: 12px;
  border-radius: 5px;
  margin-bottom: 20px;
}

.alert-danger {
  background-color: #fee2e2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

.sidebar {
  width: 250px;
  background: #1a1a1a;
  color: white;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding-bottom: 20px;
  border-bottom: 1px solid #333;
  margin-bottom: 20px;
}

.sidebar-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: #fff;
}

.user-info {
  padding: 15px 0;
  border-bottom: 1px solid #333;
  margin-bottom: 20px;
}

.username {
  display: block;
  font-size: 1.1rem;
  font-weight: 500;
  margin-bottom: 5px;
}

.role {
  display: block;
  font-size: 0.9rem;
  color: #888;
}

.nav-links {
  list-style: none;
  padding: 0;
  margin: 0;
  flex-grow: 1;
}

.nav-links li {
  margin-bottom: 5px;
}

.nav-links a {
  display: block;
  padding: 12px 15px;
  color: #fff;
  text-decoration: none;
  border-radius: 5px;
  transition: background-color 0.3s;
}

.nav-links a:hover {
  background-color: #333;
}

.nav-links li.active a {
  background-color: #667eea;
}

.logout-btn {
  padding: 12px;
  background-color: #dc2626;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s;
}

.logout-btn:hover {
  background-color: #b91c1c;
}

.main-content {
  flex-grow: 1;
  padding: 30px;
  background-color: #f3f4f6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard {
    flex-direction: column;
    padding: 15px;
  }

  .sidebar {
    width: 100%;
    padding: 15px;
  }

  .main-content {
    padding: 20px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .stat-card {
    padding: 15px;
  }

  .stat-numbers {
    grid-template-columns: 1fr;
  }

  .activity-item {
    padding: 8px;
  }

  .activity-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
} 