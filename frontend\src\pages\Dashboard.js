import React, { useState, useEffect } from 'react';
import { Routes, Route, Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import EmissionData from '../components/EmissionData';
import VerificationTasks from '../components/VerificationTasks';
import TransactionManagement from '../components/TransactionManagement';
import ViolationManagement from '../components/ViolationManagement';
import '../styles/Dashboard.css';

function Dashboard() {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('emission');

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  return (
    <div className="dashboard">
      <nav className="sidebar">
        <div className="sidebar-header">
          <h2>碳管理系统</h2>
        </div>
        <div className="user-info">
          <span className="username">{user?.username}</span>
          <span className="role">{user?.role === 'admin' ? '管理员' : '用户'}</span>
        </div>
        <ul className="nav-links">
          <li className={activeTab === 'emission' ? 'active' : ''}>
            <Link to="/dashboard/emission" onClick={() => setActiveTab('emission')}>
              排放数据
            </Link>
          </li>
          <li className={activeTab === 'verification' ? 'active' : ''}>
            <Link to="/dashboard/verification" onClick={() => setActiveTab('verification')}>
              核查任务
            </Link>
          </li>
          <li className={activeTab === 'transaction' ? 'active' : ''}>
            <Link to="/dashboard/transaction" onClick={() => setActiveTab('transaction')}>
              交易管理
            </Link>
          </li>
          {user?.role === 'admin' && (
            <li className={activeTab === 'violation' ? 'active' : ''}>
              <Link to="/dashboard/violation" onClick={() => setActiveTab('violation')}>
                违规管理
              </Link>
            </li>
          )}
        </ul>
        <button className="logout-btn" onClick={handleLogout}>
          退出登录
        </button>
      </nav>
      <main className="main-content">
        <Routes>
          <Route path="emission" element={<EmissionData />} />
          <Route path="verification" element={<VerificationTasks />} />
          <Route path="transaction" element={<TransactionManagement />} />
          {user?.role === 'admin' && (
            <Route path="violation" element={<ViolationManagement />} />
          )}
        </Routes>
      </main>
    </div>
  );
}

export default Dashboard; 