"""
用户认证工具
提供用户登录验证功能
"""

import pymysql
from werkzeug.security import check_password_hash
from datetime import datetime

# 数据库连接信息
DB_HOST = '***********'
DB_PORT = 3306
DB_USER = 'wuhong'
DB_PASSWORD = 'D7mH8rZ7a7Z2kJa8'
DB_NAME = 'ces'

def get_db_connection():
    """获取数据库连接"""
    try:
        conn = pymysql.connect(
            host=DB_HOST,
            port=DB_PORT,
            user=DB_USER,
            password=DB_PASSWORD,
            database=DB_NAME,
            charset='utf8mb4'
        )
        return conn
    except Exception as e:
        print(f"数据库连接失败: {str(e)}")
        return None

def authenticate_user(username, password):
    """验证用户登录

    Args:
        username: 用户名
        password: 密码

    Returns:
        user_info: 用户信息字典，如果验证失败则返回None
    """
    conn = get_db_connection()
    if not conn:
        print("数据库连接失败")
        return None

    try:
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        # 查询用户
        cursor.execute(
            """
            SELECT id, username, email, password_hash, role, company_name,
                   credit_code, blockchain_address
            FROM user
            WHERE username = %s
            """,
            (username,)
        )

        user = cursor.fetchone()

        if not user:
            print(f"用户不存在: {username}")
            return None

        # 验证密码
        if check_password_hash(user['password_hash'], password):
            print(f"密码验证成功: {username}")

            # 更新最后登录时间
            cursor.execute(
                """
                UPDATE user
                SET last_login = %s
                WHERE id = %s
                """,
                (datetime.now(), user['id'])
            )

            conn.commit()

            # 返回用户信息
            return {
                'id': user['id'],
                'username': user['username'],
                'role': user['role'],
                'company_name': user['company_name'],
                'email': user['email'],
                'credit_code': user['credit_code'],
                'blockchain_address': user['blockchain_address']
            }
        else:
            print(f"密码验证失败: {username}")
            return None
    except Exception as e:
        print(f"用户验证过程中发生错误: {str(e)}")
        return None
    finally:
        cursor.close()
        conn.close()

def get_user_by_id(user_id):
    """根据用户ID获取用户信息

    Args:
        user_id: 用户ID

    Returns:
        user_info: 用户信息字典，如果查询失败则返回None
    """
    conn = get_db_connection()
    if not conn:
        print("数据库连接失败")
        return None

    try:
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        # 查询用户
        cursor.execute(
            """
            SELECT id, username, email, role, company_name,
                   credit_code, blockchain_address
            FROM user
            WHERE id = %s
            """,
            (user_id,)
        )

        user = cursor.fetchone()

        if not user:
            print(f"用户不存在: ID={user_id}")
            return None

        return user
    except Exception as e:
        print(f"获取用户信息过程中发生错误: {str(e)}")
        return None
    finally:
        cursor.close()
        conn.close()

def get_user_counts():
    """获取用户数量统计

    Returns:
        counts: 用户数量统计字典
    """
    conn = get_db_connection()
    if not conn:
        print("数据库连接失败")
        return {
            'total': 0,
            'admin': 0,
            'enterprise': 0,
            'verifier': 0
        }

    try:
        cursor = conn.cursor()

        # 查询总用户数
        cursor.execute("SELECT COUNT(*) FROM user")
        total_count = cursor.fetchone()[0]

        # 查询管理员数量
        cursor.execute("SELECT COUNT(*) FROM user WHERE role = 'admin'")
        admin_count = cursor.fetchone()[0]

        # 查询企业数量
        cursor.execute("SELECT COUNT(*) FROM user WHERE role = 'enterprise'")
        enterprise_count = cursor.fetchone()[0]

        # 查询核查机构数量
        cursor.execute("SELECT COUNT(*) FROM user WHERE role = 'verifier'")
        verifier_count = cursor.fetchone()[0]

        return {
            'total': total_count,
            'admin': admin_count,
            'enterprise': enterprise_count,
            'verifier': verifier_count
        }
    except Exception as e:
        print(f"获取用户数量统计过程中发生错误: {str(e)}")
        return {
            'total': 0,
            'admin': 0,
            'enterprise': 0,
            'verifier': 0
        }
    finally:
        cursor.close()
        conn.close()

def get_recent_users(limit=5):
    """获取最近登录的用户

    Args:
        limit: 返回的用户数量

    Returns:
        users: 用户列表
    """
    conn = get_db_connection()
    if not conn:
        print("数据库连接失败")
        return []

    try:
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        # 查询最近登录的用户
        cursor.execute(
            """
            SELECT id, username, role, company_name, last_login
            FROM user
            WHERE last_login IS NOT NULL
            ORDER BY last_login DESC
            LIMIT %s
            """,
            (limit,)
        )

        users = cursor.fetchall()
        return users
    except Exception as e:
        print(f"获取最近登录用户过程中发生错误: {str(e)}")
        return []
    finally:
        cursor.close()
        conn.close()

def get_all_users():
    """获取所有用户

    Returns:
        users: 用户列表
    """
    conn = get_db_connection()
    if not conn:
        print("数据库连接失败")
        return []

    try:
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        # 查询所有用户
        cursor.execute(
            """
            SELECT id, username, email, role, company_name,
                   credit_code, blockchain_address, registration_time, last_login
            FROM user
            ORDER BY id
            """
        )

        users = cursor.fetchall()
        return users
    except Exception as e:
        print(f"获取所有用户过程中发生错误: {str(e)}")
        return []
    finally:
        cursor.close()
        conn.close()

# 测试函数
if __name__ == "__main__":
    # 测试用户验证
    user = authenticate_user("admin", "admin123")
    if user:
        print(f"用户验证成功: {user}")
    else:
        print("用户验证失败")

    # 测试获取用户数量统计
    counts = get_user_counts()
    print(f"用户数量统计: {counts}")

    # 测试获取最近登录的用户
    recent_users = get_recent_users()
    print(f"最近登录的用户: {recent_users}")

    # 测试获取所有用户
    all_users = get_all_users()
    print(f"所有用户: {all_users}")
