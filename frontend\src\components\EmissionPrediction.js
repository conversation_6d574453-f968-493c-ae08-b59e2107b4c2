import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useAuth } from '../contexts/AuthContext';
import { Line, Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import '../styles/EmissionPrediction.css';

// 注册Chart.js组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

function EmissionPrediction() {
  const [enterprises, setEnterprises] = useState([]);
  const [selectedEnterprise, setSelectedEnterprise] = useState('');
  const [months, setMonths] = useState(12);
  const [trendData, setTrendData] = useState(null);
  const [models, setModels] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const { user } = useAuth();

  useEffect(() => {
    fetchEnterprises();
    fetchModels();
  }, []);

  useEffect(() => {
    if (selectedEnterprise) {
      fetchTrendData();
    }
  }, [selectedEnterprise, months]);

  const fetchEnterprises = async () => {
    try {
      const response = await axios.get('/api/auth/enterprises');
      setEnterprises(response.data);
      
      // 如果用户是企业，自动选择自己
      if (user?.role === 'enterprise') {
        setSelectedEnterprise(user.id);
      } else if (response.data.length > 0) {
        setSelectedEnterprise(response.data[0].id);
      }
    } catch (err) {
      setError('获取企业列表失败: ' + (err.response?.data?.error || err.message));
    }
  };

  const fetchModels = async () => {
    try {
      const response = await axios.get('/api/prediction/models');
      setModels(response.data);
    } catch (err) {
      setError('获取预测模型失败: ' + (err.response?.data?.error || err.message));
    }
  };

  const fetchTrendData = async () => {
    setLoading(true);
    setError('');
    
    try {
      const response = await axios.get('/api/prediction/trend', {
        params: {
          enterprise_id: selectedEnterprise,
          months: months
        }
      });
      
      setTrendData(response.data);
    } catch (err) {
      setError('获取趋势数据失败: ' + (err.response?.data?.error || err.message));
    } finally {
      setLoading(false);
    }
  };

  const trainModel = async () => {
    setLoading(true);
    setError('');
    setSuccess('');
    
    try {
      const response = await axios.post('/api/prediction/train', {
        name: '碳排放预测模型',
        description: '基于历史排放数据的预测模型'
      });
      
      setSuccess('模型训练成功！');
      fetchModels();
    } catch (err) {
      setError('模型训练失败: ' + (err.response?.data?.error || err.message));
    } finally {
      setLoading(false);
    }
  };

  const handleEnterpriseChange = (e) => {
    setSelectedEnterprise(e.target.value);
  };

  const handleMonthsChange = (e) => {
    setMonths(parseInt(e.target.value));
  };

  const prepareChartData = () => {
    if (!trendData) return null;
    
    const historical = trendData.historical || [];
    const future = trendData.future || [];
    
    const labels = [...historical.map(item => item.date), ...future.map(item => item.date)];
    
    return {
      labels,
      datasets: [
        {
          label: '历史排放量',
          data: [...historical.map(item => item.emission_amount), ...Array(future.length).fill(null)],
          borderColor: 'rgb(75, 192, 192)',
          backgroundColor: 'rgba(75, 192, 192, 0.5)',
          tension: 0.3
        },
        {
          label: '预测排放量',
          data: [...Array(historical.length).fill(null), ...future.map(item => item.emission_amount)],
          borderColor: 'rgb(255, 99, 132)',
          backgroundColor: 'rgba(255, 99, 132, 0.5)',
          borderDash: [5, 5],
          tension: 0.3
        }
      ]
    };
  };

  const prepareMonthlyChartData = () => {
    if (!trendData || !trendData.historical) return null;
    
    const historical = trendData.historical;
    
    return {
      labels: historical.map(item => item.date),
      datasets: [
        {
          label: '月度排放量',
          data: historical.map(item => item.emission_amount),
          backgroundColor: 'rgba(54, 162, 235, 0.5)',
          borderColor: 'rgb(54, 162, 235)',
          borderWidth: 1
        }
      ]
    };
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: '碳排放趋势预测',
        font: {
          size: 16
        }
      },
      tooltip: {
        mode: 'index',
        intersect: false,
      }
    },
    scales: {
      y: {
        title: {
          display: true,
          text: '排放量 (吨CO₂e)'
        },
        beginAtZero: true
      },
      x: {
        title: {
          display: true,
          text: '月份'
        }
      }
    }
  };

  const barChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: '月度碳排放量',
        font: {
          size: 16
        }
      }
    },
    scales: {
      y: {
        title: {
          display: true,
          text: '排放量 (吨CO₂e)'
        },
        beginAtZero: true
      },
      x: {
        title: {
          display: true,
          text: '月份'
        }
      }
    }
  };

  return (
    <div className="emission-prediction">
      <h2>碳排放预测分析</h2>
      
      {error && <div className="error-message">{error}</div>}
      {success && <div className="success-message">{success}</div>}
      
      <div className="prediction-controls">
        <div className="control-group">
          <label>选择企业</label>
          <select 
            value={selectedEnterprise} 
            onChange={handleEnterpriseChange}
            disabled={user?.role === 'enterprise'}
          >
            <option value="">选择企业</option>
            {enterprises.map(enterprise => (
              <option key={enterprise.id} value={enterprise.id}>
                {enterprise.company_name}
              </option>
            ))}
          </select>
        </div>
        
        <div className="control-group">
          <label>历史数据月数</label>
          <select value={months} onChange={handleMonthsChange}>
            <option value="6">6个月</option>
            <option value="12">12个月</option>
            <option value="24">24个月</option>
            <option value="36">36个月</option>
          </select>
        </div>
        
        <button 
          className="refresh-btn"
          onClick={fetchTrendData}
          disabled={loading || !selectedEnterprise}
        >
          刷新数据
        </button>
        
        {user?.role === 'admin' && (
          <button 
            className="train-btn"
            onClick={trainModel}
            disabled={loading}
          >
            训练模型
          </button>
        )}
      </div>
      
      {loading ? (
        <div className="loading">加载中...</div>
      ) : (
        <div className="prediction-charts">
          {trendData ? (
            <>
              <div className="chart-container">
                <Line data={prepareChartData()} options={chartOptions} />
              </div>
              
              <div className="chart-container">
                <Bar data={prepareMonthlyChartData()} options={barChartOptions} />
              </div>
              
              <div className="prediction-summary">
                <h3>预测摘要</h3>
                <div className="summary-cards">
                  <div className="summary-card">
                    <div className="summary-value">
                      {trendData.historical.length > 0 
                        ? trendData.historical.reduce((sum, item) => sum + item.emission_amount, 0).toFixed(2)
                        : '0.00'}
                    </div>
                    <div className="summary-label">历史总排放量 (吨CO₂e)</div>
                  </div>
                  
                  <div className="summary-card">
                    <div className="summary-value">
                      {trendData.future.length > 0 
                        ? trendData.future.reduce((sum, item) => sum + item.emission_amount, 0).toFixed(2)
                        : '0.00'}
                    </div>
                    <div className="summary-label">预测总排放量 (吨CO₂e)</div>
                  </div>
                  
                  <div className="summary-card">
                    <div className="summary-value">
                      {trendData.historical.length > 0 
                        ? (trendData.historical.reduce((sum, item) => sum + item.emission_amount, 0) / 
                           trendData.historical.length).toFixed(2)
                        : '0.00'}
                    </div>
                    <div className="summary-label">历史月均排放量 (吨CO₂e)</div>
                  </div>
                  
                  <div className="summary-card">
                    <div className="summary-value">
                      {trendData.future.length > 0 
                        ? (trendData.future.reduce((sum, item) => sum + item.emission_amount, 0) / 
                           trendData.future.length).toFixed(2)
                        : '0.00'}
                    </div>
                    <div className="summary-label">预测月均排放量 (吨CO₂e)</div>
                  </div>
                </div>
              </div>
            </>
          ) : (
            <div className="no-data">暂无数据，请选择企业并刷新</div>
          )}
        </div>
      )}
      
      {user?.role === 'admin' && (
        <div className="model-info">
          <h3>预测模型信息</h3>
          
          {models.length > 0 ? (
            <table>
              <thead>
                <tr>
                  <th>ID</th>
                  <th>模型名称</th>
                  <th>创建时间</th>
                  <th>最后训练时间</th>
                  <th>R²值</th>
                  <th>RMSE</th>
                </tr>
              </thead>
              <tbody>
                {models.map(model => (
                  <tr key={model.id}>
                    <td>{model.id}</td>
                    <td>{model.name}</td>
                    <td>{new Date(model.created_at).toLocaleString()}</td>
                    <td>{model.last_trained ? new Date(model.last_trained).toLocaleString() : '未训练'}</td>
                    <td>{model.metrics?.r2 ? model.metrics.r2.toFixed(4) : 'N/A'}</td>
                    <td>{model.metrics?.rmse ? model.metrics.rmse.toFixed(4) : 'N/A'}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            <div className="no-data">暂无模型，请先训练模型</div>
          )}
        </div>
      )}
    </div>
  );
}

export default EmissionPrediction;
