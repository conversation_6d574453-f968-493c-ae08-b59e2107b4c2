import React, { useState, useEffect } from 'react';
import axios from 'axios';
import '../styles/SystemSettings.css';

function SystemSettings() {
  const [settings, setSettings] = useState({
    system: {
      siteName: '',
      siteDescription: '',
      logo: '',
      theme: 'light',
      language: 'zh-CN',
      timezone: 'Asia/Shanghai'
    },
    storage: {
      maxFileSize: 10,
      allowedTypes: ['image', 'document', 'video', 'audio'],
      storagePath: '/uploads',
      backupEnabled: true,
      backupFrequency: 'daily',
      backupTime: '00:00',
      retentionDays: 30
    },
    security: {
      passwordMinLength: 8,
      requireSpecialChars: true,
      requireNumbers: true,
      requireUppercase: true,
      sessionTimeout: 30,
      maxLoginAttempts: 5,
      lockoutDuration: 30,
      twoFactorEnabled: false
    },
    notification: {
      emailEnabled: true,
      smsEnabled: false,
      emailServer: {
        host: '',
        port: 587,
        username: '',
        password: '',
        fromAddress: ''
      },
      smsProvider: {
        provider: '',
        apiKey: '',
        apiSecret: ''
      }
    }
  });

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [activeTab, setActiveTab] = useState('system');

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const response = await axios.get('/api/settings', {
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      setSettings(response.data);
      setLoading(false);
    } catch (err) {
      setError('获取系统设置失败');
      setLoading(false);
    }
  };

  const handleChange = (section, field, value) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  const handleNestedChange = (section, parentField, field, value) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [parentField]: {
          ...prev[section][parentField],
          [field]: value
        }
      }
    }));
  };

  const handleSubmit = async (section) => {
    try {
      await axios.put(`/api/settings/${section}`, settings[section], {
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      setSuccess(`${section}设置已更新`);
      setTimeout(() => setSuccess(''), 3000);
    } catch (err) {
      setError(`更新${section}设置失败`);
      setTimeout(() => setError(''), 3000);
    }
  };

  const handleLogoUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    const formData = new FormData();
    formData.append('logo', file);

    try {
      const response = await axios.post('/api/settings/logo', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
      setSettings(prev => ({
        ...prev,
        system: {
          ...prev.system,
          logo: response.data.logoUrl
        }
      }));
      setSuccess('Logo已更新');
      setTimeout(() => setSuccess(''), 3000);
    } catch (err) {
      setError('上传Logo失败');
      setTimeout(() => setError(''), 3000);
    }
  };

  if (loading) return <div className="loading">加载中...</div>;

  return (
    <div className="system-settings">
      <h2>系统设置</h2>

      {error && <div className="alert alert-danger">{error}</div>}
      {success && <div className="alert alert-success">{success}</div>}

      <div className="settings-tabs">
        <button
          className={`tab-btn ${activeTab === 'system' ? 'active' : ''}`}
          onClick={() => setActiveTab('system')}
        >
          基本设置
        </button>
        <button
          className={`tab-btn ${activeTab === 'storage' ? 'active' : ''}`}
          onClick={() => setActiveTab('storage')}
        >
          存储设置
        </button>
        <button
          className={`tab-btn ${activeTab === 'security' ? 'active' : ''}`}
          onClick={() => setActiveTab('security')}
        >
          安全设置
        </button>
        <button
          className={`tab-btn ${activeTab === 'notification' ? 'active' : ''}`}
          onClick={() => setActiveTab('notification')}
        >
          通知设置
        </button>
      </div>

      <div className="settings-content">
        {activeTab === 'system' && (
          <div className="settings-section">
            <h3>基本设置</h3>
            <div className="form-group">
              <label htmlFor="siteName">站点名称</label>
              <input
                type="text"
                id="siteName"
                value={settings.system.siteName}
                onChange={(e) => handleChange('system', 'siteName', e.target.value)}
                className="form-control"
              />
            </div>
            <div className="form-group">
              <label htmlFor="siteDescription">站点描述</label>
              <textarea
                id="siteDescription"
                value={settings.system.siteDescription}
                onChange={(e) => handleChange('system', 'siteDescription', e.target.value)}
                className="form-control"
                rows="3"
              />
            </div>
            <div className="form-group">
              <label htmlFor="logo">站点Logo</label>
              <div className="logo-upload">
                {settings.system.logo && (
                  <img
                    src={settings.system.logo}
                    alt="Site Logo"
                    className="logo-preview"
                  />
                )}
                <input
                  type="file"
                  id="logo"
                  accept="image/*"
                  onChange={handleLogoUpload}
                  className="form-control"
                />
              </div>
            </div>
            <div className="form-group">
              <label htmlFor="theme">主题</label>
              <select
                id="theme"
                value={settings.system.theme}
                onChange={(e) => handleChange('system', 'theme', e.target.value)}
                className="form-control"
              >
                <option value="light">浅色</option>
                <option value="dark">深色</option>
                <option value="auto">跟随系统</option>
              </select>
            </div>
            <div className="form-group">
              <label htmlFor="language">语言</label>
              <select
                id="language"
                value={settings.system.language}
                onChange={(e) => handleChange('system', 'language', e.target.value)}
                className="form-control"
              >
                <option value="zh-CN">简体中文</option>
                <option value="en-US">English</option>
              </select>
            </div>
            <div className="form-group">
              <label htmlFor="timezone">时区</label>
              <select
                id="timezone"
                value={settings.system.timezone}
                onChange={(e) => handleChange('system', 'timezone', e.target.value)}
                className="form-control"
              >
                <option value="Asia/Shanghai">中国标准时间 (UTC+8)</option>
                <option value="America/New_York">美国东部时间 (UTC-5)</option>
                <option value="Europe/London">格林威治标准时间 (UTC+0)</option>
              </select>
            </div>
            <button
              className="btn btn-primary"
              onClick={() => handleSubmit('system')}
            >
              保存基本设置
            </button>
          </div>
        )}

        {activeTab === 'storage' && (
          <div className="settings-section">
            <h3>存储设置</h3>
            <div className="form-group">
              <label htmlFor="maxFileSize">最大文件大小 (MB)</label>
              <input
                type="number"
                id="maxFileSize"
                value={settings.storage.maxFileSize}
                onChange={(e) => handleChange('storage', 'maxFileSize', parseInt(e.target.value))}
                className="form-control"
                min="1"
                max="1000"
              />
            </div>
            <div className="form-group">
              <label>允许的文件类型</label>
              <div className="checkbox-group">
                {['image', 'document', 'video', 'audio'].map(type => (
                  <label key={type} className="checkbox-label">
                    <input
                      type="checkbox"
                      checked={settings.storage.allowedTypes.includes(type)}
                      onChange={(e) => {
                        const newTypes = e.target.checked
                          ? [...settings.storage.allowedTypes, type]
                          : settings.storage.allowedTypes.filter(t => t !== type);
                        handleChange('storage', 'allowedTypes', newTypes);
                      }}
                    />
                    {type === 'image' ? '图片' :
                     type === 'document' ? '文档' :
                     type === 'video' ? '视频' : '音频'}
                  </label>
                ))}
              </div>
            </div>
            <div className="form-group">
              <label htmlFor="storagePath">存储路径</label>
              <input
                type="text"
                id="storagePath"
                value={settings.storage.storagePath}
                onChange={(e) => handleChange('storage', 'storagePath', e.target.value)}
                className="form-control"
              />
            </div>
            <div className="form-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={settings.storage.backupEnabled}
                  onChange={(e) => handleChange('storage', 'backupEnabled', e.target.checked)}
                />
                启用自动备份
              </label>
            </div>
            {settings.storage.backupEnabled && (
              <>
                <div className="form-group">
                  <label htmlFor="backupFrequency">备份频率</label>
                  <select
                    id="backupFrequency"
                    value={settings.storage.backupFrequency}
                    onChange={(e) => handleChange('storage', 'backupFrequency', e.target.value)}
                    className="form-control"
                  >
                    <option value="daily">每天</option>
                    <option value="weekly">每周</option>
                    <option value="monthly">每月</option>
                  </select>
                </div>
                <div className="form-group">
                  <label htmlFor="backupTime">备份时间</label>
                  <input
                    type="time"
                    id="backupTime"
                    value={settings.storage.backupTime}
                    onChange={(e) => handleChange('storage', 'backupTime', e.target.value)}
                    className="form-control"
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="retentionDays">保留天数</label>
                  <input
                    type="number"
                    id="retentionDays"
                    value={settings.storage.retentionDays}
                    onChange={(e) => handleChange('storage', 'retentionDays', parseInt(e.target.value))}
                    className="form-control"
                    min="1"
                    max="365"
                  />
                </div>
              </>
            )}
            <button
              className="btn btn-primary"
              onClick={() => handleSubmit('storage')}
            >
              保存存储设置
            </button>
          </div>
        )}

        {activeTab === 'security' && (
          <div className="settings-section">
            <h3>安全设置</h3>
            <div className="form-group">
              <label htmlFor="passwordMinLength">密码最小长度</label>
              <input
                type="number"
                id="passwordMinLength"
                value={settings.security.passwordMinLength}
                onChange={(e) => handleChange('security', 'passwordMinLength', parseInt(e.target.value))}
                className="form-control"
                min="6"
                max="32"
              />
            </div>
            <div className="form-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={settings.security.requireSpecialChars}
                  onChange={(e) => handleChange('security', 'requireSpecialChars', e.target.checked)}
                />
                要求包含特殊字符
              </label>
            </div>
            <div className="form-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={settings.security.requireNumbers}
                  onChange={(e) => handleChange('security', 'requireNumbers', e.target.checked)}
                />
                要求包含数字
              </label>
            </div>
            <div className="form-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={settings.security.requireUppercase}
                  onChange={(e) => handleChange('security', 'requireUppercase', e.target.checked)}
                />
                要求包含大写字母
              </label>
            </div>
            <div className="form-group">
              <label htmlFor="sessionTimeout">会话超时时间 (分钟)</label>
              <input
                type="number"
                id="sessionTimeout"
                value={settings.security.sessionTimeout}
                onChange={(e) => handleChange('security', 'sessionTimeout', parseInt(e.target.value))}
                className="form-control"
                min="5"
                max="1440"
              />
            </div>
            <div className="form-group">
              <label htmlFor="maxLoginAttempts">最大登录尝试次数</label>
              <input
                type="number"
                id="maxLoginAttempts"
                value={settings.security.maxLoginAttempts}
                onChange={(e) => handleChange('security', 'maxLoginAttempts', parseInt(e.target.value))}
                className="form-control"
                min="3"
                max="10"
              />
            </div>
            <div className="form-group">
              <label htmlFor="lockoutDuration">锁定时间 (分钟)</label>
              <input
                type="number"
                id="lockoutDuration"
                value={settings.security.lockoutDuration}
                onChange={(e) => handleChange('security', 'lockoutDuration', parseInt(e.target.value))}
                className="form-control"
                min="5"
                max="1440"
              />
            </div>
            <div className="form-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={settings.security.twoFactorEnabled}
                  onChange={(e) => handleChange('security', 'twoFactorEnabled', e.target.checked)}
                />
                启用双因素认证
              </label>
            </div>
            <button
              className="btn btn-primary"
              onClick={() => handleSubmit('security')}
            >
              保存安全设置
            </button>
          </div>
        )}

        {activeTab === 'notification' && (
          <div className="settings-section">
            <h3>通知设置</h3>
            <div className="form-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={settings.notification.emailEnabled}
                  onChange={(e) => handleChange('notification', 'emailEnabled', e.target.checked)}
                />
                启用邮件通知
              </label>
            </div>
            {settings.notification.emailEnabled && (
              <div className="nested-settings">
                <div className="form-group">
                  <label htmlFor="emailHost">SMTP服务器</label>
                  <input
                    type="text"
                    id="emailHost"
                    value={settings.notification.emailServer.host}
                    onChange={(e) => handleNestedChange('notification', 'emailServer', 'host', e.target.value)}
                    className="form-control"
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="emailPort">SMTP端口</label>
                  <input
                    type="number"
                    id="emailPort"
                    value={settings.notification.emailServer.port}
                    onChange={(e) => handleNestedChange('notification', 'emailServer', 'port', parseInt(e.target.value))}
                    className="form-control"
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="emailUsername">用户名</label>
                  <input
                    type="text"
                    id="emailUsername"
                    value={settings.notification.emailServer.username}
                    onChange={(e) => handleNestedChange('notification', 'emailServer', 'username', e.target.value)}
                    className="form-control"
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="emailPassword">密码</label>
                  <input
                    type="password"
                    id="emailPassword"
                    value={settings.notification.emailServer.password}
                    onChange={(e) => handleNestedChange('notification', 'emailServer', 'password', e.target.value)}
                    className="form-control"
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="emailFrom">发件人地址</label>
                  <input
                    type="email"
                    id="emailFrom"
                    value={settings.notification.emailServer.fromAddress}
                    onChange={(e) => handleNestedChange('notification', 'emailServer', 'fromAddress', e.target.value)}
                    className="form-control"
                  />
                </div>
              </div>
            )}
            <div className="form-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={settings.notification.smsEnabled}
                  onChange={(e) => handleChange('notification', 'smsEnabled', e.target.checked)}
                />
                启用短信通知
              </label>
            </div>
            {settings.notification.smsEnabled && (
              <div className="nested-settings">
                <div className="form-group">
                  <label htmlFor="smsProvider">短信服务商</label>
                  <select
                    id="smsProvider"
                    value={settings.notification.smsProvider.provider}
                    onChange={(e) => handleNestedChange('notification', 'smsProvider', 'provider', e.target.value)}
                    className="form-control"
                  >
                    <option value="aliyun">阿里云</option>
                    <option value="tencent">腾讯云</option>
                    <option value="huawei">华为云</option>
                  </select>
                </div>
                <div className="form-group">
                  <label htmlFor="smsApiKey">API Key</label>
                  <input
                    type="text"
                    id="smsApiKey"
                    value={settings.notification.smsProvider.apiKey}
                    onChange={(e) => handleNestedChange('notification', 'smsProvider', 'apiKey', e.target.value)}
                    className="form-control"
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="smsApiSecret">API Secret</label>
                  <input
                    type="password"
                    id="smsApiSecret"
                    value={settings.notification.smsProvider.apiSecret}
                    onChange={(e) => handleNestedChange('notification', 'smsProvider', 'apiSecret', e.target.value)}
                    className="form-control"
                  />
                </div>
              </div>
            )}
            <button
              className="btn btn-primary"
              onClick={() => handleSubmit('notification')}
            >
              保存通知设置
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

export default SystemSettings; 