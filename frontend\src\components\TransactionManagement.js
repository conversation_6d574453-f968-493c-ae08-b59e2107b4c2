import React, { useState, useEffect } from 'react';
import axios from 'axios';
import '../styles/TransactionManagement.css';

function TransactionManagement() {
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [formData, setFormData] = useState({
    type: 'buy',
    amount: '',
    price: '',
    counterparty: '',
    description: ''
  });

  useEffect(() => {
    fetchTransactions();
  }, []);

  const fetchTransactions = async () => {
    try {
      const response = await axios.get('/api/transactions', {
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      setTransactions(response.data);
      setLoading(false);
    } catch (err) {
      setError('获取交易记录失败');
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await axios.post('/api/transactions', formData, {
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      fetchTransactions();
      setFormData({
        type: 'buy',
        amount: '',
        price: '',
        counterparty: '',
        description: ''
      });
    } catch (err) {
      setError('创建交易失败');
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleStatusChange = async (transactionId, newStatus) => {
    try {
      await axios.patch(`/api/transactions/${transactionId}`, 
        { status: newStatus },
        { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } }
      );
      fetchTransactions();
    } catch (err) {
      setError('更新交易状态失败');
    }
  };

  if (loading) return <div className="loading">加载中...</div>;

  return (
    <div className="transaction-management">
      <h2>交易管理</h2>
      {error && <div className="alert alert-danger">{error}</div>}
      
      <div className="transaction-form">
        <h3>创建新交易</h3>
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="type">交易类型</label>
            <select
              id="type"
              name="type"
              value={formData.type}
              onChange={handleChange}
              required
              className="form-control"
            >
              <option value="buy">购买</option>
              <option value="sell">出售</option>
            </select>
          </div>
          <div className="form-group">
            <label htmlFor="amount">交易数量 (tCO2e)</label>
            <input
              type="number"
              id="amount"
              name="amount"
              value={formData.amount}
              onChange={handleChange}
              required
              className="form-control"
              step="0.01"
              min="0"
            />
          </div>
          <div className="form-group">
            <label htmlFor="price">单价 (元/tCO2e)</label>
            <input
              type="number"
              id="price"
              name="price"
              value={formData.price}
              onChange={handleChange}
              required
              className="form-control"
              step="0.01"
              min="0"
            />
          </div>
          <div className="form-group">
            <label htmlFor="counterparty">交易方</label>
            <input
              type="text"
              id="counterparty"
              name="counterparty"
              value={formData.counterparty}
              onChange={handleChange}
              required
              className="form-control"
            />
          </div>
          <div className="form-group">
            <label htmlFor="description">交易描述</label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              className="form-control"
              rows="3"
            />
          </div>
          <button type="submit" className="btn btn-primary">
            创建交易
          </button>
        </form>
      </div>

      <div className="transaction-list">
        <h3>交易记录</h3>
        <div className="table-responsive">
          <table className="table">
            <thead>
              <tr>
                <th>交易类型</th>
                <th>数量 (tCO2e)</th>
                <th>单价 (元/tCO2e)</th>
                <th>总价 (元)</th>
                <th>交易方</th>
                <th>描述</th>
                <th>状态</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              {transactions.map(transaction => (
                <tr key={transaction.id}>
                  <td>
                    <span className={`type ${transaction.type}`}>
                      {transaction.type === 'buy' ? '购买' : '出售'}
                    </span>
                  </td>
                  <td>{transaction.amount}</td>
                  <td>{transaction.price}</td>
                  <td>{(transaction.amount * transaction.price).toFixed(2)}</td>
                  <td>{transaction.counterparty}</td>
                  <td>{transaction.description}</td>
                  <td>
                    <span className={`status ${transaction.status}`}>
                      {transaction.status === 'pending' ? '待确认' :
                       transaction.status === 'completed' ? '已完成' :
                       transaction.status === 'cancelled' ? '已取消' : '处理中'}
                    </span>
                  </td>
                  <td>
                    <select
                      value={transaction.status}
                      onChange={(e) => handleStatusChange(transaction.id, e.target.value)}
                      className="status-select"
                    >
                      <option value="pending">待确认</option>
                      <option value="processing">处理中</option>
                      <option value="completed">已完成</option>
                      <option value="cancelled">已取消</option>
                    </select>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default TransactionManagement; 