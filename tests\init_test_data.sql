-- 初始化测试数据脚本
-- 为碳排放核查系统创建测试用户和测试数据

-- 使用数据库
USE ces;

-- 清空现有数据（谨慎使用，仅在测试环境中执行）
SET FOREIGN_KEY_CHECKS = 0;
TRUNCATE TABLE users;
TRUNCATE TABLE enterprises;
TRUNCATE TABLE verifiers;
TRUNCATE TABLE emission_data;
TRUNCATE TABLE verifications;
TRUNCATE TABLE transactions;
TRUNCATE TABLE penalties;
TRUNCATE TABLE activities;
SET FOREIGN_KEY_CHECKS = 1;

-- 创建测试用户
-- 密码均为 'password123' 的哈希值
INSERT INTO users (username, password, email, role, created_at, updated_at) VALUES
('admin', 'pbkdf2:sha256:150000$KDoXoFDQ$d37a5b0c7e635d0c2b8a9b1c6be6cd214a38a5c99c8b0db9be3d4d37b61d232c', '<EMAIL>', 'admin', NOW(), NOW()),
('enterprise1', 'pbkdf2:sha256:150000$KDoXoFDQ$d37a5b0c7e635d0c2b8a9b1c6be6cd214a38a5c99c8b0db9be3d4d37b61d232c', '<EMAIL>', 'enterprise', NOW(), NOW()),
('enterprise2', 'pbkdf2:sha256:150000$KDoXoFDQ$d37a5b0c7e635d0c2b8a9b1c6be6cd214a38a5c99c8b0db9be3d4d37b61d232c', '<EMAIL>', 'enterprise', NOW(), NOW()),
('enterprise3', 'pbkdf2:sha256:150000$KDoXoFDQ$d37a5b0c7e635d0c2b8a9b1c6be6cd214a38a5c99c8b0db9be3d4d37b61d232c', '<EMAIL>', 'enterprise', NOW(), NOW()),
('verifier1', 'pbkdf2:sha256:150000$KDoXoFDQ$d37a5b0c7e635d0c2b8a9b1c6be6cd214a38a5c99c8b0db9be3d4d37b61d232c', '<EMAIL>', 'verifier', NOW(), NOW()),
('verifier2', 'pbkdf2:sha256:150000$KDoXoFDQ$d37a5b0c7e635d0c2b8a9b1c6be6cd214a38a5c99c8b0db9be3d4d37b61d232c', '<EMAIL>', 'verifier', NOW(), NOW());

-- 创建企业信息
INSERT INTO enterprises (user_id, name, industry, address, contact_person, contact_phone, carbon_quota, created_at, updated_at) VALUES
(2, '绿色能源有限公司', '能源', '北京市海淀区中关村大街1号', '张三', '13800138001', 10000.0, NOW(), NOW()),
(3, '蓝天制造股份公司', '制造业', '上海市浦东新区张江高科技园区', '李四', '13800138002', 8000.0, NOW(), NOW()),
(4, '未来科技集团', '科技', '深圳市南山区科技园', '王五', '13800138003', 5000.0, NOW(), NOW());

-- 创建核查机构信息
INSERT INTO verifiers (user_id, name, qualification, address, contact_person, contact_phone, created_at, updated_at) VALUES
(5, '环保核查中心', '国家认证A级核查机构', '北京市朝阳区建国路', '赵六', '13900139001', NOW(), NOW()),
(6, '绿色认证联盟', '国际认证核查机构', '上海市静安区南京西路', '钱七', '13900139002', NOW(), NOW());

-- 创建排放数据
INSERT INTO emission_data (enterprise_id, emission_source, emission_amount, calculation_method, emission_time, submission_time, status, notes, proof_file, blockchain_hash, blockchain_block, created_at, updated_at) VALUES
-- 企业1的排放数据
(2, '工厂A', 1200.5, '直接测量法', '2025-01-10 08:00:00', '2025-01-11 10:30:00', 'pending', '一季度常规排放', 'proof_files/enterprise1_emission1.pdf', NULL, NULL, NOW(), NOW()),
(2, '工厂B', 800.3, '间接计算法', '2025-02-15 09:00:00', '2025-02-16 14:20:00', 'pending', '二季度常规排放', 'proof_files/enterprise1_emission2.pdf', NULL, NULL, NOW(), NOW()),
(2, '工厂C', 1500.8, '直接测量法', '2025-03-20 10:00:00', '2025-03-21 11:45:00', 'pending', '三季度常规排放', 'proof_files/enterprise1_emission3.pdf', NULL, NULL, NOW(), NOW()),

-- 企业2的排放数据
(3, '生产线A', 950.2, '直接测量法', '2025-01-12 08:30:00', '2025-01-13 09:15:00', 'pending', '一季度常规排放', 'proof_files/enterprise2_emission1.pdf', NULL, NULL, NOW(), NOW()),
(3, '生产线B', 1100.7, '间接计算法', '2025-02-18 09:30:00', '2025-02-19 10:40:00', 'pending', '二季度常规排放', 'proof_files/enterprise2_emission2.pdf', NULL, NULL, NOW(), NOW()),

-- 企业3的排放数据
(4, '研发中心', 600.1, '直接测量法', '2025-01-15 08:45:00', '2025-01-16 10:20:00', 'pending', '一季度常规排放', 'proof_files/enterprise3_emission1.pdf', NULL, NULL, NOW(), NOW()),
(4, '数据中心', 750.4, '间接计算法', '2025-02-20 09:15:00', '2025-02-21 11:30:00', 'pending', '二季度常规排放', 'proof_files/enterprise3_emission2.pdf', NULL, NULL, NOW(), NOW());

-- 创建核查记录
INSERT INTO verifications (verifier_id, emission_data_id, conclusion, comments, verification_time, blockchain_hash, blockchain_block, created_at, updated_at) VALUES
-- 核查机构1的核查记录
(5, 1, 'approved', '数据符合标准，排放量在允许范围内', '2025-01-20 14:30:00', NULL, NULL, NOW(), NOW()),
(5, 3, 'rejected', '数据不完整，需要补充更多证明材料', '2025-03-25 15:45:00', NULL, NULL, NOW(), NOW()),
(5, 5, 'approved', '数据准确，计算方法正确', '2025-02-25 13:20:00', NULL, NULL, NOW(), NOW()),

-- 核查机构2的核查记录
(6, 2, 'approved', '排放数据准确，符合行业标准', '2025-02-25 10:15:00', NULL, NULL, NOW(), NOW()),
(6, 4, 'pending', '正在核查中，需要额外信息', '2025-01-25 11:30:00', NULL, NULL, NOW(), NOW()),
(6, 6, 'approved', '数据完整，排放量符合预期', '2025-01-30 09:45:00', NULL, NULL, NOW(), NOW());

-- 创建交易记录
INSERT INTO transactions (buyer_id, seller_id, amount, price, transaction_time, status, notes, blockchain_hash, blockchain_block, created_at, updated_at) VALUES
-- 企业间的交易
(2, 3, 500.0, 20.5, '2025-02-01 10:00:00', 'pending', '碳配额交易', NULL, NULL, NOW(), NOW()),
(3, 4, 300.0, 22.0, '2025-02-10 11:30:00', 'completed', '碳配额交易', NULL, NULL, NOW(), NOW()),
(4, 2, 200.0, 21.0, '2025-03-05 09:45:00', 'cancelled', '碳配额交易', NULL, NULL, NOW(), NOW()),
(2, 4, 400.0, 19.5, '2025-03-15 14:20:00', 'pending', '碳配额交易', NULL, NULL, NOW(), NOW());

-- 创建惩罚记录
INSERT INTO penalties (enterprise_id, amount, reason, penalty_time, status, blockchain_hash, blockchain_block, created_at, updated_at) VALUES
(2, 5000.0, '排放数据造假', '2025-04-01 09:00:00', 'pending', NULL, NULL, NOW(), NOW()),
(3, 3000.0, '未按时提交排放数据', '2025-04-05 10:30:00', 'completed', NULL, NULL, NOW(), NOW()),
(4, 2000.0, '超额排放', '2025-04-10 11:15:00', 'pending', NULL, NULL, NOW(), NOW());

-- 创建活动记录
INSERT INTO activities (user_id, activity_type, description, created_at) VALUES
(1, 'login', '管理员登录系统', NOW()),
(2, 'data_submission', '企业提交排放数据', NOW()),
(5, 'verification', '核查机构提交核查结果', NOW()),
(3, 'transaction', '企业发起碳交易', NOW()),
(1, 'penalty', '管理员创建惩罚记录', NOW()),
(4, 'login', '企业登录系统', NOW()),
(6, 'verification', '核查机构提交核查结果', NOW());

-- 提交事务
COMMIT;

-- 显示创建的测试数据数量
SELECT 'users' AS table_name, COUNT(*) AS record_count FROM users
UNION ALL
SELECT 'enterprises', COUNT(*) FROM enterprises
UNION ALL
SELECT 'verifiers', COUNT(*) FROM verifiers
UNION ALL
SELECT 'emission_data', COUNT(*) FROM emission_data
UNION ALL
SELECT 'verifications', COUNT(*) FROM verifications
UNION ALL
SELECT 'transactions', COUNT(*) FROM transactions
UNION ALL
SELECT 'penalties', COUNT(*) FROM penalties
UNION ALL
SELECT 'activities', COUNT(*) FROM activities;
