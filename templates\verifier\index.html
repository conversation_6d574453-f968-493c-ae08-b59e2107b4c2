<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放核查系统 - 核查机构仪表板</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container">
            <a class="navbar-brand" href="/"><i class="fas fa-leaf me-2"></i>碳排放核查系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/verifier"><i class="fas fa-tachometer-alt me-1"></i>仪表板</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/verifier/verifications"><i class="fas fa-check-double me-1"></i>核查管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/verifier/enterprises"><i class="fas fa-building me-1"></i>企业管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/verifier/reports"><i class="fas fa-file-alt me-1"></i>报告管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/verifier/blockchain/config"><i class="fas fa-link me-1"></i>区块链配置</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>{{ current_user.get('username', '核查机构') }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="/verifier/profile"><i class="fas fa-id-card me-1"></i>机构资料</a></li>
                            <li><a class="dropdown-item" href="/verifier/settings"><i class="fas fa-cog me-1"></i>账户设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-1"></i>退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mt-4">
        <!-- 消息提示 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- 错误信息 -->
        {% if error %}
            <div class="alert alert-danger">
                <h4 class="alert-heading">发生错误!</h4>
                <p>{{ error }}</p>
            </div>
        {% endif %}

        <!-- 仪表板标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-tachometer-alt me-2"></i>核查机构仪表板</h1>
            <div>
                <span class="badge bg-primary">当前时间: <span id="current-time"></span></span>
            </div>
        </div>

        <!-- 机构信息 -->
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0"><i class="fas fa-building me-2"></i>机构信息</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>机构名称:</strong> {{ current_user.get('company_name', '未知机构') }}</p>
                        <p><strong>用户名:</strong> {{ current_user.get('username', '未知用户') }}</p>
                        <p><strong>角色:</strong> <span class="badge bg-info">核查机构</span></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>账户状态:</strong> <span class="badge bg-success">正常</span></p>
                        <p><strong>登录时间:</strong> {{ current_user.get('login_time', '未知') }}</p>
                        <p><strong>区块链地址:</strong> <span class="text-muted">{{ user.blockchain_address if user and user.blockchain_address else '未配置' }}</span></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">核查总数</h6>
                                <h2 class="card-text">{{ statistics.total_verifications|default(0) }}</h2>
                            </div>
                            <i class="fas fa-check-double fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">待核查</h6>
                                <h2 class="card-text">{{ statistics.pending_verifications|default(0) }}</h2>
                            </div>
                            <i class="fas fa-hourglass-half fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">已通过</h6>
                                <h2 class="card-text">{{ statistics.approved_verifications|default(0) }}</h2>
                            </div>
                            <i class="fas fa-check-circle fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">已拒绝</h6>
                                <h2 class="card-text">{{ statistics.rejected_verifications|default(0) }}</h2>
                            </div>
                            <i class="fas fa-times-circle fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 待核查数据 -->
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0"><i class="fas fa-clipboard-list me-2"></i>待核查数据</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>企业名称</th>
                                <th>排放源</th>
                                <th>排放量</th>
                                <th>提交日期</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if pending_verifications %}
                                {% for verification in pending_verifications %}
                                    <tr>
                                        <td>{{ verification.id }}</td>
                                        <td>{{ verification.enterprise_name }}</td>
                                        <td>{{ verification.source }}</td>
                                        <td>{{ verification.amount }} {{ verification.unit }}</td>
                                        <td>{{ verification.submission_date }}</td>
                                        <td><span class="badge bg-warning">{{ verification.status }}</span></td>
                                        <td>
                                            <a href="/verifier/verifications/{{ verification.id }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i> 查看
                                            </a>
                                            <a href="/verifier/verifications/{{ verification.id }}/verify" class="btn btn-sm btn-success">
                                                <i class="fas fa-check"></i> 核查
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="7" class="text-center">暂无待核查数据</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer bg-light">
                <a href="/verifier/verifications" class="btn btn-primary">查看全部核查记录</a>
            </div>
        </div>

        <!-- 最近完成的核查 -->
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0"><i class="fas fa-history me-2"></i>最近完成的核查</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>企业名称</th>
                                <th>排放源</th>
                                <th>排放量</th>
                                <th>核查日期</th>
                                <th>状态</th>
                                <th>备注</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if completed_verifications %}
                                {% for verification in completed_verifications %}
                                    <tr>
                                        <td>{{ verification.id }}</td>
                                        <td>{{ verification.enterprise_name }}</td>
                                        <td>{{ verification.source }}</td>
                                        <td>{{ verification.amount }} {{ verification.unit }}</td>
                                        <td>{{ verification.verification_date }}</td>
                                        <td>
                                            {% if verification.status == '已通过' %}
                                                <span class="badge bg-success">{{ verification.status }}</span>
                                            {% elif verification.status == '已拒绝' %}
                                                <span class="badge bg-danger">{{ verification.status }}</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ verification.status }}</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ verification.comments }}</td>
                                    </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="7" class="text-center">暂无已完成的核查记录</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0"><i class="fas fa-bolt me-2"></i>快速操作</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <a href="/verifier/verifications" class="btn btn-primary w-100">
                                    <i class="fas fa-clipboard-list me-2"></i>查看待核查数据
                                </a>
                            </div>
                            <div class="col-md-4 mb-3">
                                <a href="/verifier/reports/generate" class="btn btn-success w-100">
                                    <i class="fas fa-file-alt me-2"></i>生成核查报告
                                </a>
                            </div>
                            <div class="col-md-4 mb-3">
                                <a href="/verifier/enterprises" class="btn btn-info w-100">
                                    <i class="fas fa-building me-2"></i>管理企业
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">© 2025 碳排放核查系统 | 基于区块链技术</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // 显示当前时间
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleString('zh-CN');
        }
        updateTime();
        setInterval(updateTime, 1000);
    </script>
</body>
</html>
