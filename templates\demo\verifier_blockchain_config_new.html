{% extends 'base.html' %}

{% block title %}碳排放核查系统 - 核查机构区块链配置（演示）{% endblock %}

{% block head %}
<style>
    .alert {
        padding: 15px;
        border-radius: 10px;
        margin-bottom: 20px;
        box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    }
    .alert-info {
        background: linear-gradient(to right, #d1ecf1, #bee5eb);
        border-left: 5px solid #0c5460;
        color: #0c5460;
    }
    .status-box {
        background: linear-gradient(to right, #f1f8e9, #dcedc8);
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
        box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    }
    .status-item {
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }
    .status-item:last-child {
        border-bottom: none;
    }
    .status-label {
        font-weight: bold;
        color: #2c3e50;
    }
    .status-value {
        color: #27ae60;
    }
    .status-value.error {
        color: #e74c3c;
    }
    .badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        display: inline-block;
    }
    .bg-success {
        background: linear-gradient(to right, #2ecc71, #27ae60) !important;
    }
    .bg-primary {
        background: linear-gradient(to right, #3498db, #2980b9) !important;
    }
    .bg-info {
        background: linear-gradient(to right, #00bcd4, #0097a7) !important;
    }
</style>
{% endblock %}

{% block content %}

    <div class="container">
        <h1>区块链配置</h1>

        <div class="alert alert-info">
            <div style="display: flex; align-items: center;">
                <i class="fas fa-info-circle fa-2x" style="margin-right: 15px;"></i>
                <div>
                    <h4 style="margin-top: 0;">区块链配置说明</h4>
                    <p>本页面用于配置核查机构的区块链连接信息，以便将核查结果上传到区块链。请按照以下步骤操作：</p>
                    <ol>
                        <li>确保 Ganache 已启动并运行在 <code>http://127.0.0.1:8545</code>（默认地址）</li>
                        <li>输入您的核查机构区块链地址和私钥</li>
                        <li>输入已部署的智能合约地址</li>
                        <li>点击"保存配置"按钮，保存配置信息</li>
                    </ol>
                    <p style="margin-bottom: 0;">完成以上步骤后，系统将能够将您的核查结果上传到区块链。</p>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-title">区块链状态</div>
            <div class="status-box">
                <div class="status-item">
                    <span class="status-label">连接状态:</span>
                    <span class="status-value">已连接</span>
                </div>
                <div class="status-item">
                    <span class="status-label">网络ID:</span>
                    <span class="status-value">1337 (Ganache)</span>
                </div>
                <div class="status-item">
                    <span class="status-label">当前区块:</span>
                    <span class="status-value">12345</span>
                </div>
                <div class="status-item">
                    <span class="status-label">智能合约:</span>
                    <span class="status-value">已部署 (0x5FbDB2315678afecb367f032d93F642f64180aa3)</span>
                </div>
                <div class="status-item">
                    <span class="status-label">核查机构地址:</span>
                    <span class="status-value">******************************************</span>
                </div>
            </div>
            <button class="btn btn-primary" id="testConnectionBtn">测试连接</button>
        </div>

        <div class="card">
            <div class="card-title">区块链配置</div>
            <form id="blockchainConfigForm">
                <div class="form-group">
                    <label for="ethereum_node_url">以太坊节点URL</label>
                    <input type="text" id="ethereum_node_url" name="ethereum_node_url" value="http://127.0.0.1:8545" required>
                    <small>例如: http://127.0.0.1:8545 (本地Ganache)</small>
                </div>
                <div class="form-group">
                    <label for="verifier_address">核查机构区块链地址</label>
                    <input type="text" id="verifier_address" name="verifier_address" value="******************************************" required>
                </div>
                <div class="form-group">
                    <label for="private_key">核查机构私钥</label>
                    <input type="password" id="private_key" name="private_key" value="0x5de4111afa1a4b94908f83103eb1f1706367c2e68ca870fc3fb9a804cdab365a" required>
                    <small>注意: 私钥仅用于签名交易，不会被发送到区块链网络</small>
                </div>
                <div class="form-group">
                    <label for="contract_address">智能合约地址</label>
                    <input type="text" id="contract_address" name="contract_address" value="0x5FbDB2315678afecb367f032d93F642f64180aa3">
                    <small>已部署的碳排放智能合约地址</small>
                </div>
                <div class="form-group">
                    <label for="simulation_mode">模拟模式</label>
                    <select id="simulation_mode" name="simulation_mode">
                        <option value="true" selected>启用 (无需真实区块链)</option>
                        <option value="false">禁用 (使用真实区块链)</option>
                    </select>
                </div>
                <button type="button" class="btn btn-primary" id="saveConfigBtn">保存配置</button>
            </form>
        </div>

        <div class="card">
            <div class="card-title">最近核查记录上链情况</div>
            <table>
                <thead>
                    <tr>
                        <th>交易哈希</th>
                        <th>核查ID</th>
                        <th>企业</th>
                        <th>区块号</th>
                        <th>时间</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code>0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef</code></td>
                        <td>1001</td>
                        <td>北京碳排放科技有限公司</td>
                        <td>12345</td>
                        <td>2025-04-01 10:30:45</td>
                        <td><span class="badge bg-success">成功</span></td>
                    </tr>
                    <tr>
                        <td><code>0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890</code></td>
                        <td>1002</td>
                        <td>上海绿色能源有限公司</td>
                        <td>12350</td>
                        <td>2025-04-02 14:20:15</td>
                        <td><span class="badge bg-success">成功</span></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 测试连接按钮点击事件
            document.getElementById('testConnectionBtn').addEventListener('click', function() {
                alert('连接成功!\n网络ID: 1337\n当前区块: 12345');
            });

            // 保存配置按钮点击事件
            document.getElementById('saveConfigBtn').addEventListener('click', function() {
                alert('配置已保存');
            });
        });
    </script>
{% endblock %}
