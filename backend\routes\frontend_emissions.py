"""
排放数据前端路由
"""

from flask import Blueprint, render_template, redirect, request, flash, session, url_for, current_app
import os
import uuid
from datetime import datetime
from werkzeug.utils import secure_filename
from backend import db
from backend.models.emission import Emission
from backend.models.verification import Verification

emissions_frontend_bp = Blueprint('emissions_frontend', __name__)

def check_auth(required_role=None):
    """检查用户是否已登录并具有正确的角色
    
    Args:
        required_role: 需要的角色，如果为None则只检查是否登录
        
    Returns:
        None: 如果用户已登录并具有正确的角色
        redirect: 如果用户未登录或角色不正确，重定向到登录页面
    """
    # 检查用户是否已登录
    if 'user_id' not in session or not session.get('logged_in', False):
        flash('请先登录', 'warning')
        return redirect('/login')
    
    # 如果指定了角色，检查用户角色
    if required_role and session.get('role') != required_role:
        flash('您没有权限访问该页面', 'danger')
        return redirect('/login')
    
    return None

@emissions_frontend_bp.route('/enterprise/emissions/<int:emission_id>/edit', methods=['GET', 'POST'])
def enterprise_emission_edit(emission_id):
    """企业编辑排放数据页面"""
    # 检查认证
    auth_result = check_auth(required_role='enterprise')
    if auth_result:
        return auth_result

    # 查询排放数据
    emission = Emission.query.get(emission_id)
    if not emission:
        flash('排放数据不存在', 'danger')
        return redirect(url_for('frontend.enterprise_emissions'))
    
    # 检查权限
    if emission.enterprise_id != session.get('user_id'):
        flash('无权编辑此排放数据', 'danger')
        return redirect(url_for('frontend.enterprise_emissions'))
    
    # 检查状态
    if emission.status not in ['draft', 'rejected']:
        flash('只能编辑草稿或被拒绝的排放数据', 'warning')
        return redirect(url_for('frontend.enterprise_emission_detail', emission_id=emission.id))
    
    if request.method == 'POST':
        # 允许的文件扩展名
        ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'jpeg', 'png'}
        
        def allowed_file(filename):
            """检查文件扩展名是否允许"""
            return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS
        
        # 处理文件上传
        if 'proof_file' in request.files:
            file = request.files['proof_file']
            if file and file.filename and allowed_file(file.filename):
                # 生成安全的文件名
                filename = secure_filename(file.filename)
                # 添加唯一标识符，避免文件名冲突
                unique_filename = f"{uuid.uuid4().hex}_{filename}"
                # 确保上传目录存在
                upload_folder = os.path.join(current_app.static_folder, 'uploads')
                os.makedirs(upload_folder, exist_ok=True)
                # 保存文件
                file_path = os.path.join(upload_folder, unique_filename)
                file.save(file_path)
                # 存储相对路径
                emission.proof_file_path = f"uploads/{unique_filename}"
        
        # 更新排放数据
        try:
            emission.emission_source = request.form['emission_source']
            emission.calculation_method = request.form['calculation_method']
            emission.emission_amount = float(request.form['emission_amount'])
            emission.emission_unit = request.form['emission_unit']
            emission.emission_period_start = datetime.strptime(request.form['emission_period_start'], '%Y-%m-%d')
            emission.emission_period_end = datetime.strptime(request.form['emission_period_end'], '%Y-%m-%d')
            emission.description = request.form.get('description', '')
            
            # 如果是提交操作，更新状态
            if request.form.get('action') == 'submit':
                emission.status = 'submitted'
            
            db.session.commit()
            
            flash('排放数据已成功更新', 'success')
            return redirect(url_for('frontend.enterprise_emission_detail', emission_id=emission.id))
        except Exception as e:
            flash(f'更新排放数据失败: {str(e)}', 'danger')

    return render_template('enterprise/emission_edit.html',
                          emission=emission,
                          current_user=session)

@emissions_frontend_bp.route('/enterprise/emissions/<int:emission_id>/submit')
def enterprise_emission_submit(emission_id):
    """企业提交排放数据"""
    # 检查认证
    auth_result = check_auth(required_role='enterprise')
    if auth_result:
        return auth_result

    # 查询排放数据
    emission = Emission.query.get(emission_id)
    if not emission:
        flash('排放数据不存在', 'danger')
        return redirect(url_for('frontend.enterprise_emissions'))
    
    # 检查权限
    if emission.enterprise_id != session.get('user_id'):
        flash('无权提交此排放数据', 'danger')
        return redirect(url_for('frontend.enterprise_emissions'))
    
    # 检查状态
    if emission.status not in ['draft', 'rejected']:
        flash('只能提交草稿或被拒绝的排放数据', 'warning')
        return redirect(url_for('frontend.enterprise_emission_detail', emission_id=emission.id))
    
    # 更新状态
    try:
        emission.status = 'submitted'
        db.session.commit()
        flash('排放数据已成功提交', 'success')
    except Exception as e:
        flash(f'提交排放数据失败: {str(e)}', 'danger')
    
    return redirect(url_for('frontend.enterprise_emissions'))

@emissions_frontend_bp.route('/enterprise/emissions/<int:emission_id>/withdraw', methods=['POST'])
def enterprise_emission_withdraw(emission_id):
    """企业撤回排放数据"""
    # 检查认证
    auth_result = check_auth(required_role='enterprise')
    if auth_result:
        return redirect(url_for('frontend.enterprise_emissions'))
    
    # 查询排放数据
    emission = Emission.query.get(emission_id)
    if not emission:
        return {'success': False, 'message': '排放数据不存在'}, 404
    
    # 检查权限
    if emission.enterprise_id != session.get('user_id'):
        return {'success': False, 'message': '无权撤回此排放数据'}, 403
    
    # 检查状态
    if emission.status != 'submitted':
        return {'success': False, 'message': '只能撤回已提交的排放数据'}, 400
    
    # 更新状态
    try:
        emission.status = 'draft'
        db.session.commit()
        return {'success': True, 'message': '排放数据已撤回'}
    except Exception as e:
        db.session.rollback()
        return {'success': False, 'message': f'撤回排放数据失败: {str(e)}'}, 500

@emissions_frontend_bp.route('/enterprise/emissions/<int:emission_id>/delete', methods=['POST'])
def enterprise_emission_delete(emission_id):
    """企业删除排放数据"""
    # 检查认证
    auth_result = check_auth(required_role='enterprise')
    if auth_result:
        return auth_result
    
    # 查询排放数据
    emission = Emission.query.get(emission_id)
    if not emission:
        flash('排放数据不存在', 'danger')
        return redirect(url_for('frontend.enterprise_emissions'))
    
    # 检查权限
    if emission.enterprise_id != session.get('user_id'):
        flash('无权删除此排放数据', 'danger')
        return redirect(url_for('frontend.enterprise_emissions'))
    
    # 检查状态
    if emission.status not in ['draft', 'rejected']:
        flash('只能删除草稿或被拒绝的排放数据', 'warning')
        return redirect(url_for('frontend.enterprise_emission_detail', emission_id=emission.id))
    
    # 删除排放数据
    try:
        # 如果有证明文件，删除文件
        if emission.proof_file_path:
            file_path = os.path.join(current_app.static_folder, emission.proof_file_path)
            if os.path.exists(file_path):
                os.remove(file_path)
        
        db.session.delete(emission)
        db.session.commit()
        flash('排放数据已成功删除', 'success')
    except Exception as e:
        flash(f'删除排放数据失败: {str(e)}', 'danger')
    
    return redirect(url_for('frontend.enterprise_emissions'))
