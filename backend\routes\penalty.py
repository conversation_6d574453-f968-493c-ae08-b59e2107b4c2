"""
惩罚管理相关路由
"""

import hashlib
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity

from backend import db
from backend.models.user import User
from backend.models.penalty import Penalty
from backend.models.activity import Activity
from backend.utils.auth import admin_required

penalty_bp = Blueprint('penalty', __name__)

@penalty_bp.route('', methods=['GET'])
@jwt_required()
def get_penalties():
    """获取惩罚记录列表"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)

    if not user:
        return jsonify({'error': '用户不存在'}), 404

    # 根据用户角色过滤惩罚记录
    if user.role == 'admin':
        # 管理员可以查看所有惩罚记录
        penalties = Penalty.query.all()
    elif user.role == 'enterprise':
        # 企业用户只能查看自己的惩罚记录
        penalties = Penalty.query.filter_by(enterprise_id=user.id).all()
    else:
        # 核查机构不能查看惩罚记录
        return jsonify({'error': '无权查看惩罚记录'}), 403

    return jsonify({
        'penalties': [penalty.to_dict() for penalty in penalties]
    }), 200

@penalty_bp.route('/<int:penalty_id>', methods=['GET'])
@jwt_required()
def get_penalty(penalty_id):
    """获取惩罚记录详情"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)

    if not user:
        return jsonify({'error': '用户不存在'}), 404

    penalty = Penalty.query.get(penalty_id)
    if not penalty:
        return jsonify({'error': '惩罚记录不存在'}), 404

    # 检查权限
    if user.role != 'admin' and penalty.enterprise_id != current_user_id:
        return jsonify({'error': '无权查看此惩罚记录'}), 403

    # 从区块链获取惩罚记录
    blockchain_data = None
    if penalty.blockchain_hash:
        try:
            blockchain_data = current_app.blockchain_client.get_penalty(penalty_id)
        except Exception as e:
            print(f"从区块链获取惩罚记录失败: {str(e)}")

    result = penalty.to_dict()
    if blockchain_data:
        result['blockchain_data'] = blockchain_data

    return jsonify({
        'penalty': result
    }), 200

@penalty_bp.route('', methods=['POST'])
@admin_required
def create_penalty():
    """创建惩罚记录"""
    data = request.get_json()

    # 验证请求数据
    if not data or not data.get('enterprise_id') or not data.get('amount') or not data.get('reason'):
        return jsonify({'error': '请求数据不完整'}), 400

    # 验证企业是否存在
    enterprise = User.query.get(data['enterprise_id'])
    if not enterprise or enterprise.role != 'enterprise':
        return jsonify({'error': '企业不存在'}), 404

    # 创建惩罚记录
    penalty = Penalty(
        enterprise_id=data['enterprise_id'],
        amount=data['amount'],
        reason=data['reason'],
        penalty_time=datetime.now(),
        status='pending'
    )

    db.session.add(penalty)
    db.session.commit()

    # 生成哈希值
    hash_value = hashlib.sha256(f"{penalty.id}_{penalty.enterprise_id}_{penalty.amount}_{penalty.penalty_time.isoformat()}".encode()).hexdigest()

    # 提交到区块链
    blockchain_result = current_app.blockchain_client.record_penalty(
        penalty.id,
        penalty.enterprise_id,
        penalty.amount,
        penalty.reason,
        penalty.penalty_time.isoformat(),
        hash_value
    )

    # 更新区块链信息
    if blockchain_result['success']:
        penalty.blockchain_hash = blockchain_result['tx_hash']
        penalty.blockchain_block = blockchain_result['block_number']
        db.session.commit()

    # 记录活动
    activity = Activity(
        user_id=get_jwt_identity(),
        activity_type='create_penalty',
        description=f'管理员创建了惩罚记录，ID: {penalty.id}，企业: {enterprise.company_name}'
    )
    db.session.add(activity)
    db.session.commit()

    return jsonify({
        'message': '惩罚记录创建成功',
        'penalty': penalty.to_dict()
    }), 201

@penalty_bp.route('/<int:penalty_id>', methods=['PATCH'])
@admin_required
def update_penalty_status(penalty_id):
    """更新惩罚记录状态"""
    data = request.get_json()

    # 验证请求数据
    if not data or not data.get('status'):
        return jsonify({'error': '请求数据不完整'}), 400

    # 验证状态值
    if data['status'] not in ['pending', 'paid', 'disputed']:
        return jsonify({'error': '无效的状态值'}), 400

    # 获取惩罚记录
    penalty = Penalty.query.get(penalty_id)
    if not penalty:
        return jsonify({'error': '惩罚记录不存在'}), 404

    # 更新状态
    penalty.status = data['status']
    db.session.commit()

    # 记录活动
    activity = Activity(
        user_id=get_jwt_identity(),
        activity_type='update_penalty_status',
        description=f'管理员更新了惩罚记录状态，ID: {penalty.id}，状态: {penalty.status}'
    )
    db.session.add(activity)
    db.session.commit()

    return jsonify({
        'message': '惩罚记录状态更新成功',
        'penalty': penalty.to_dict()
    }), 200
