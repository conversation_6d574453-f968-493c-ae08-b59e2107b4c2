<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放核查系统 - 交易管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <style>
        .gradient-custom {
            background: linear-gradient(135deg, #43a047 0%, #1de9b6 100%);
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            border-radius: 15px 15px 0 0;
            background: linear-gradient(135deg, #43a047 0%, #1de9b6 100%);
            color: white;
        }
        .btn-success {
            background: linear-gradient(135deg, #43a047 0%, #1de9b6 100%);
            border: none;
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #388e3c 0%, #00bfa5 100%);
        }
        .transaction-card {
            transition: all 0.3s ease;
        }
        .transaction-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container">
            <a class="navbar-brand" href="/"><i class="fas fa-leaf me-2"></i>碳排放核查系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise"><i class="fas fa-tachometer-alt me-1"></i>仪表板</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise/emissions"><i class="fas fa-cloud me-1"></i>排放管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise/quotas"><i class="fas fa-chart-pie me-1"></i>配额管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/enterprise/transactions"><i class="fas fa-exchange-alt me-1"></i>交易管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise/reports"><i class="fas fa-file-alt me-1"></i>报告管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise/blockchain/config"><i class="fas fa-link me-1"></i>区块链配置</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>{{ current_user.get('username', '企业用户') }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="/enterprise/profile"><i class="fas fa-id-card me-1"></i>企业资料</a></li>
                            <li><a class="dropdown-item" href="/enterprise/settings"><i class="fas fa-cog me-1"></i>账户设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-1"></i>退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mt-4">
        <!-- 消息提示 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-exchange-alt me-2"></i>交易管理</h1>
            <div>
                <a href="/enterprise/transactions/create" class="btn btn-success">
                    <i class="fas fa-plus me-1"></i>新建交易
                </a>
            </div>
        </div>

        <!-- 交易统计 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">总交易量</h5>
                        <p class="display-5 text-success">{{ statistics.total_amount|default(0) }}</p>
                        <p class="text-muted">tCO2e</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">总交易金额</h5>
                        <p class="display-5 text-primary">¥ {{ statistics.total_value|default(0) }}</p>
                        <p class="text-muted">人民币</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">平均价格</h5>
                        <p class="display-5 text-info">¥ {{ statistics.avg_price|default(0) }}</p>
                        <p class="text-muted">每吨CO2当量</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">待处理交易</h5>
                        <p class="display-5 text-warning">{{ statistics.pending_transactions|default(0) }}</p>
                        <p class="text-muted">笔</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 交易筛选 -->
        <div class="card mb-4">
            <div class="card-header gradient-custom">
                <h5 class="card-title mb-0"><i class="fas fa-filter me-2"></i>筛选交易</h5>
            </div>
            <div class="card-body">
                <form method="get" action="/enterprise/transactions" class="row g-3">
                    <div class="col-md-3">
                        <label for="transaction_type" class="form-label">交易类型</label>
                        <select class="form-select" id="transaction_type" name="transaction_type">
                            <option value="">全部类型</option>
                            <option value="buy">买入</option>
                            <option value="sell">卖出</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="date_from" class="form-label">开始日期</label>
                        <input type="date" class="form-control" id="date_from" name="date_from">
                    </div>
                    <div class="col-md-3">
                        <label for="date_to" class="form-label">结束日期</label>
                        <input type="date" class="form-control" id="date_to" name="date_to">
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">状态</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">全部状态</option>
                            <option value="pending">待确认</option>
                            <option value="completed">已完成</option>
                            <option value="cancelled">已取消</option>
                        </select>
                    </div>
                    <div class="col-12 d-flex justify-content-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>搜索
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 待处理交易 -->
        <div class="card mb-4">
            <div class="card-header gradient-custom">
                <h5 class="card-title mb-0"><i class="fas fa-clock me-2"></i>待处理交易</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>类型</th>
                                <th>交易方</th>
                                <th>数量 (tCO2e)</th>
                                <th>单价 (¥)</th>
                                <th>总价 (¥)</th>
                                <th>日期</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if pending_transactions %}
                                {% for transaction in pending_transactions %}
                                    <tr>
                                        <td>{{ transaction.id }}</td>
                                        <td>
                                            {% if transaction.buyer_id == current_user.user_id %}
                                                <span class="badge bg-success">买入</span>
                                            {% else %}
                                                <span class="badge bg-warning">卖出</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if transaction.buyer_id == current_user.user_id %}
                                                {{ transaction.seller_name }}
                                            {% else %}
                                                {{ transaction.buyer_name }}
                                            {% endif %}
                                        </td>
                                        <td>{{ transaction.amount }}</td>
                                        <td>{{ transaction.price }}</td>
                                        <td>{{ transaction.total }}</td>
                                        <td>{{ transaction.transaction_date }}</td>
                                        <td>
                                            <span class="badge bg-warning">待确认</span>
                                        </td>
                                        <td>
                                            {% if transaction.seller_id == current_user.user_id %}
                                                <button type="button" class="btn btn-sm btn-success confirm-btn" data-id="{{ transaction.id }}">
                                                    <i class="fas fa-check me-1"></i>确认
                                                </button>
                                            {% endif %}
                                            <button type="button" class="btn btn-sm btn-danger cancel-btn" data-id="{{ transaction.id }}">
                                                <i class="fas fa-times me-1"></i>取消
                                            </button>
                                        </td>
                                    </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="9" class="text-center">暂无待处理交易</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 历史交易 -->
        <div class="card">
            <div class="card-header gradient-custom">
                <h5 class="card-title mb-0"><i class="fas fa-history me-2"></i>历史交易</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>类型</th>
                                <th>交易方</th>
                                <th>数量 (tCO2e)</th>
                                <th>单价 (¥)</th>
                                <th>总价 (¥)</th>
                                <th>日期</th>
                                <th>状态</th>
                                <th>区块链哈希</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if completed_transactions %}
                                {% for transaction in completed_transactions %}
                                    <tr>
                                        <td>{{ transaction.id }}</td>
                                        <td>
                                            {% if transaction.buyer_id == current_user.user_id %}
                                                <span class="badge bg-success">买入</span>
                                            {% else %}
                                                <span class="badge bg-warning">卖出</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if transaction.buyer_id == current_user.user_id %}
                                                {{ transaction.seller_name }}
                                            {% else %}
                                                {{ transaction.buyer_name }}
                                            {% endif %}
                                        </td>
                                        <td>{{ transaction.amount }}</td>
                                        <td>{{ transaction.price }}</td>
                                        <td>{{ transaction.total }}</td>
                                        <td>{{ transaction.transaction_date }}</td>
                                        <td>
                                            {% if transaction.status == 'completed' %}
                                                <span class="badge bg-success">已完成</span>
                                            {% elif transaction.status == 'cancelled' %}
                                                <span class="badge bg-secondary">已取消</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if transaction.blockchain_hash %}
                                                <a href="#" class="view-hash" data-hash="{{ transaction.blockchain_hash }}" title="{{ transaction.blockchain_hash }}">
                                                    {{ transaction.blockchain_hash[:8] }}...{{ transaction.blockchain_hash[-8:] }}
                                                </a>
                                            {% else %}
                                                <span class="text-muted">无</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="9" class="text-center">暂无历史交易</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1" aria-disabled="true">上一页</a>
                        </li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                        <li class="page-item">
                            <a class="page-link" href="#">下一页</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- 区块链哈希模态框 -->
    <div class="modal fade" id="hashModal" tabindex="-1" aria-labelledby="hashModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="hashModalLabel">区块链交易哈希</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="hashValue" class="form-label">交易哈希值</label>
                        <input type="text" class="form-control" id="hashValue" readonly>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>此交易已记录在区块链上，不可篡改。
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" id="copyHashBtn">
                        <i class="fas fa-copy me-1"></i>复制哈希
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">© 2025 碳排放核查系统 | 基于区块链技术</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/api.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 确认交易按钮点击事件
            document.querySelectorAll('.confirm-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const transactionId = this.getAttribute('data-id');
                    
                    if (confirm('确定要确认此交易吗？此操作将不可撤销。')) {
                        API.confirmTransaction(transactionId)
                            .then(response => {
                                if (response.success) {
                                    alert('交易已确认');
                                    location.reload();
                                } else {
                                    alert('确认交易失败: ' + response.message);
                                }
                            })
                            .catch(error => {
                                alert('确认交易失败: ' + error.message);
                            });
                    }
                });
            });

            // 取消交易按钮点击事件
            document.querySelectorAll('.cancel-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const transactionId = this.getAttribute('data-id');
                    
                    if (confirm('确定要取消此交易吗？')) {
                        API.cancelTransaction(transactionId)
                            .then(response => {
                                if (response.success) {
                                    alert('交易已取消');
                                    location.reload();
                                } else {
                                    alert('取消交易失败: ' + response.message);
                                }
                            })
                            .catch(error => {
                                alert('取消交易失败: ' + error.message);
                            });
                    }
                });
            });

            // 查看区块链哈希按钮点击事件
            document.querySelectorAll('.view-hash').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const hash = this.getAttribute('data-hash');
                    document.getElementById('hashValue').value = hash;
                    
                    const hashModal = new bootstrap.Modal(document.getElementById('hashModal'));
                    hashModal.show();
                });
            });

            // 复制哈希按钮点击事件
            document.getElementById('copyHashBtn').addEventListener('click', function() {
                const hashValue = document.getElementById('hashValue');
                hashValue.select();
                document.execCommand('copy');
                alert('哈希值已复制到剪贴板');
            });
        });
    </script>
</body>
</html>
