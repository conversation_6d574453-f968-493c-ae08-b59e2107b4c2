<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ enterprise.company_name }} - 碳排放报告</title>
    <style>
        body {
            font-family: 'SimHei', 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            background: linear-gradient(to right, #1B5E20, #4CAF50);
            color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        .header h1 {
            color: white;
            margin-bottom: 10px;
        }
        .header p {
            color: rgba(255,255,255,0.9);
            margin: 5px 0;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #2E7D32;
            border-bottom: 1px solid #A5D6A7;
            padding-bottom: 10px;
        }
        .summary-box {
            background-color: #E8F5E9;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            border-left: 5px solid #4CAF50;
        }
        .summary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        .summary-label {
            font-weight: bold;
        }
        .chart-container {
            margin: 20px 0;
            text-align: center;
        }
        .chart-container img {
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        th {
            background: linear-gradient(to right, #2E7D32, #4CAF50);
            color: white;
            font-weight: bold;
        }
        tr:last-child td {
            border-bottom: none;
        }
        tr:nth-child(even) {
            background-color: rgba(0,0,0,0.02);
        }
        tr:hover {
            background-color: rgba(76, 175, 80, 0.05);
        }
        .footer {
            text-align: center;
            margin-top: 50px;
            padding: 20px;
            background: linear-gradient(to right, #1B5E20, #4CAF50);
            color: white;
            font-size: 14px;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        .prediction-section {
            background-color: #E8F5E9;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            border-left: 5px solid #4CAF50;
        }
        .prediction-section h2 {
            color: #2E7D32;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ enterprise.company_name }} - 碳排放报告</h1>
            <p>报告期间: {{ period_start }} 至 {{ period_end }}</p>
            <p>生成日期: {{ report_date }}</p>
        </div>

        <div class="section">
            <h2>摘要</h2>
            <div class="summary-box">
                <div class="summary-item">
                    <span class="summary-label">企业名称:</span>
                    <span>{{ enterprise.company_name }}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">统一社会信用代码:</span>
                    <span>{{ enterprise.credit_code }}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">行业类别:</span>
                    <span>{{ enterprise.industry }}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">报告期总排放量:</span>
                    <span>{{ total_emission|round(2) }} 吨CO₂e</span>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>排放趋势</h2>
            <div class="chart-container">
                <img src="data:image/png;base64,{{ trend_chart }}" alt="排放趋势图">
            </div>
            <p>上图展示了报告期内的月度碳排放趋势。</p>
        </div>

        <div class="section">
            <h2>排放源分析</h2>
            <div class="chart-container">
                <img src="data:image/png;base64,{{ source_chart }}" alt="排放源占比图">
            </div>
            <table>
                <thead>
                    <tr>
                        <th>排放源</th>
                        <th>排放量 (吨CO₂e)</th>
                        <th>占比 (%)</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in source_summary %}
                    <tr>
                        <td>{{ item.emission_source }}</td>
                        <td>{{ item.total|round(2) }}</td>
                        <td>{{ (item.total / total_emission * 100)|round(2) }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        {% if has_prediction %}
        <div class="section prediction-section">
            <h2>排放预测</h2>
            <div class="chart-container">
                <img src="data:image/png;base64,{{ prediction_chart }}" alt="排放预测图">
            </div>
            <p>上图展示了基于历史数据的未来6个月碳排放预测趋势。</p>
            <p>注意: 预测结果仅供参考，实际排放量可能受多种因素影响而有所变化。</p>
        </div>
        {% endif %}

        <div class="section">
            <h2>排放数据明细</h2>
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>排放源</th>
                        <th>排放量 (吨CO₂e)</th>
                        <th>排放期间</th>
                        <th>计算方法</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in emission_data %}
                    <tr>
                        <td>{{ item.id }}</td>
                        <td>{{ item.emission_source }}</td>
                        <td>{{ item.emission_amount|round(2) }}</td>
                        <td>{{ item.emission_period_start }} 至 {{ item.emission_period_end }}</td>
                        <td>{{ item.calculation_method }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>建议与措施</h2>
            <p>基于本报告的分析结果，我们建议采取以下措施来减少碳排放：</p>
            <ol>
                <li>优化能源结构，增加可再生能源使用比例</li>
                <li>提高能源利用效率，减少能源浪费</li>
                <li>加强碳资产管理，积极参与碳交易市场</li>
                <li>推进低碳技术研发与应用，实现生产过程减排</li>
                <li>建立完善的碳排放监测与管理体系</li>
            </ol>
        </div>

        <div class="footer">
            <p>本报告由碳排放管理系统自动生成</p>
            <p>© {{ report_date.split('-')[0] }} 碳排放管理系统 版权所有</p>
        </div>
    </div>
</body>
</html>
