"""
碳计算记录模型
"""

import json
from datetime import datetime
from backend import db
from backend.models.user import User

class CarbonCalculation(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    enterprise_id = db.Column(db.Integer, db.<PERSON><PERSON>('user.id'), nullable=False)
    calculation_time = db.Column(db.DateTime, default=datetime.utcnow)
    input_data = db.Column(db.Text)  # 存储为JSON字符串
    result_total = db.Column(db.Float, nullable=False)
    result_breakdown = db.Column(db.Text)  # 存储为JSON字符串

    enterprise = db.relationship('User', backref=db.backref('carbon_calculations', lazy=True))

    def set_input_data(self, data):
        self.input_data = json.dumps(data)

    def get_input_data(self):
        return json.loads(self.input_data) if self.input_data else {}

    def set_result_breakdown(self, data):
        self.result_breakdown = json.dumps(data)

    def get_result_breakdown(self):
        return json.loads(self.result_breakdown) if self.result_breakdown else {}

    def to_dict(self):
        return {
            'id': self.id,
            'enterprise_id': self.enterprise_id,
            'enterprise_name': self.enterprise.company_name if self.enterprise else None,
            'calculation_time': self.calculation_time.isoformat() if self.calculation_time else None,
            'input_data': self.get_input_data(),
            'result_total': self.result_total,
            'result_breakdown': self.get_result_breakdown()
        }
