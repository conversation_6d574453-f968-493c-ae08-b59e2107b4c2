"""
简化版智能合约部署脚本
使用Web3.py直接部署合约，不需要编译
"""

import os
import json
from web3 import Web3
from dotenv import load_dotenv
import sys

# 加载.env文件
load_dotenv()

def deploy_contract():
    """部署智能合约"""
    # 连接到以太坊节点
    ethereum_node_url = os.environ.get('ETHEREUM_NODE_URL', 'http://127.0.0.1:8545')
    print(f"连接到以太坊节点: {ethereum_node_url}")
    
    web3 = Web3(Web3.HTTPProvider(ethereum_node_url))
    
    # 检查连接
    if not web3.is_connected():
        print("无法连接到以太坊节点，请确保Ganache正在运行")
        return False
    
    print(f"成功连接到以太坊节点，当前区块号: {web3.eth.block_number}")
    
    # 加载合约ABI和字节码
    contract_path = os.path.join(os.path.dirname(__file__), 'contracts', 'artifacts', 'CarbonEmission_metadata.json')
    bin_path = os.path.join(os.path.dirname(__file__), 'contracts', 'artifacts', 'CarbonEmission.bin')
    
    print(f"尝试加载合约ABI: {contract_path}")
    
    try:
        with open(contract_path, 'r') as f:
            contract_json = json.load(f)
            contract_abi = contract_json['output']['abi']
        
        with open(bin_path, 'r') as f:
            contract_bytecode = f.read().strip()
            
        print("成功加载合约ABI和字节码")
    except Exception as e:
        print(f"加载合约ABI或字节码失败: {str(e)}")
        return False
    
    # 获取部署账户
    admin_address = os.environ.get('ADMIN_ADDRESS')
    admin_private_key = os.environ.get('ADMIN_PRIVATE_KEY')
    
    if not admin_address:
        # 使用Ganache的第一个账户
        admin_address = web3.eth.accounts[0]
        print(f"未设置ADMIN_ADDRESS环境变量，使用Ganache的第一个账户: {admin_address}")
    
    # 创建合约实例
    CarbonEmission = web3.eth.contract(abi=contract_abi, bytecode=contract_bytecode)
    
    # 部署合约
    print("部署合约...")
    try:
        # 构建交易
        transaction = {
            'from': admin_address,
            'gas': 3000000,
            'gasPrice': web3.to_wei('50', 'gwei'),
            'nonce': web3.eth.get_transaction_count(admin_address)
        }
        
        # 部署合约
        tx_hash = CarbonEmission.constructor().transact(transaction)
        
        # 等待交易被确认
        tx_receipt = web3.eth.wait_for_transaction_receipt(tx_hash)
        
        # 获取合约地址
        contract_address = tx_receipt.contractAddress
        
        print(f"合约已部署，地址: {contract_address}")
        print(f"管理员已在合约构造函数中自动注册: {admin_address}")
        
        # 更新.env文件
        env_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), '.env')
        
        # 读取现有.env文件内容
        env_content = ""
        if os.path.exists(env_path):
            with open(env_path, 'r') as f:
                env_content = f.read()
        
        # 更新CONTRACT_ADDRESS
        if 'CONTRACT_ADDRESS=' in env_content:
            env_content = '\n'.join([line if not line.startswith('CONTRACT_ADDRESS=') else f'CONTRACT_ADDRESS={contract_address}' for line in env_content.split('\n')])
        else:
            env_content += f'\nCONTRACT_ADDRESS={contract_address}'
        
        # 写入.env文件
        with open(env_path, 'w') as f:
            f.write(env_content)
        
        print(f".env文件已更新，CONTRACT_ADDRESS={contract_address}")
        
        # 注册企业和核查机构
        contract = web3.eth.contract(address=contract_address, abi=contract_abi)
        
        # 注册企业
        enterprise_addresses = [
            os.environ.get('ENTERPRISE_1_ADDRESS'),
            os.environ.get('ENTERPRISE_2_ADDRESS'),
            os.environ.get('ENTERPRISE_3_ADDRESS')
        ]
        
        for i, addr in enumerate(enterprise_addresses):
            if addr and addr != '******************************************':
                print(f"注册企业 {i+1}: {addr}")
                try:
                    tx_hash = contract.functions.registerEnterprise(
                        web3.to_checksum_address(addr)
                    ).transact({
                        'from': admin_address,
                        'gas': 200000,
                        'gasPrice': web3.to_wei('50', 'gwei'),
                        'nonce': web3.eth.get_transaction_count(admin_address)
                    })
                    web3.eth.wait_for_transaction_receipt(tx_hash)
                    print(f"企业 {i+1} 注册成功")
                except Exception as e:
                    print(f"企业 {i+1} 注册失败: {str(e)}")
        
        # 注册核查机构
        verifier_addresses = [
            os.environ.get('VERIFIER_1_ADDRESS'),
            os.environ.get('VERIFIER_2_ADDRESS')
        ]
        
        for i, addr in enumerate(verifier_addresses):
            if addr and addr != '******************************************':
                print(f"注册核查机构 {i+1}: {addr}")
                try:
                    tx_hash = contract.functions.registerVerifier(
                        web3.to_checksum_address(addr)
                    ).transact({
                        'from': admin_address,
                        'gas': 200000,
                        'gasPrice': web3.to_wei('50', 'gwei'),
                        'nonce': web3.eth.get_transaction_count(admin_address)
                    })
                    web3.eth.wait_for_transaction_receipt(tx_hash)
                    print(f"核查机构 {i+1} 注册成功")
                except Exception as e:
                    print(f"核查机构 {i+1} 注册失败: {str(e)}")
        
        return True
    except Exception as e:
        print(f"部署合约失败: {str(e)}")
        return False

if __name__ == '__main__':
    success = deploy_contract()
    sys.exit(0 if success else 1)
