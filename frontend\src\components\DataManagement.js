import React, { useState, useEffect } from 'react';
import axios from 'axios';
import '../styles/DataManagement.css';

function DataManagement() {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortField, setSortField] = useState('name');
  const [sortDirection, setSortDirection] = useState('asc');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedItems, setSelectedItems] = useState([]);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [uploadFile, setUploadFile] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);

  const itemsPerPage = 10;

  useEffect(() => {
    fetchData();
  }, [currentPage, sortField, sortDirection, selectedCategory, searchTerm]);

  const fetchData = async () => {
    try {
      const response = await axios.get('/api/data', {
        params: {
          page: currentPage,
          limit: itemsPerPage,
          sort: sortField,
          direction: sortDirection,
          category: selectedCategory,
          search: searchTerm
        },
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      setData(response.data.items);
      setTotalPages(Math.ceil(response.data.total / itemsPerPage));
      setLoading(false);
    } catch (err) {
      setError('获取数据失败');
      setLoading(false);
    }
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const handleCategoryChange = (e) => {
    setSelectedCategory(e.target.value);
    setCurrentPage(1);
  };

  const handleSort = (field) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleSelectItem = (id) => {
    setSelectedItems(prev =>
      prev.includes(id)
        ? prev.filter(itemId => itemId !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    setSelectedItems(prev =>
      prev.length === data.length
        ? []
        : data.map(item => item.id)
    );
  };

  const handleDelete = async () => {
    if (!window.confirm('确定要删除选中的数据吗？')) return;

    try {
      await axios.delete('/api/data', {
        data: { ids: selectedItems },
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      setSelectedItems([]);
      fetchData();
    } catch (err) {
      setError('删除数据失败');
    }
  };

  const handleDownload = async () => {
    try {
      const response = await axios.get('/api/data/download', {
        params: { ids: selectedItems },
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` },
        responseType: 'blob'
      });

      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'data.zip');
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (err) {
      setError('下载数据失败');
    }
  };

  const handleUpload = async () => {
    if (!uploadFile) return;

    const formData = new FormData();
    formData.append('file', uploadFile);

    try {
      await axios.post('/api/data/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${localStorage.getItem('token')}`
        },
        onUploadProgress: (progressEvent) => {
          const progress = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          setUploadProgress(progress);
        }
      });
      setShowUploadModal(false);
      setUploadFile(null);
      setUploadProgress(0);
      fetchData();
    } catch (err) {
      setError('上传数据失败');
    }
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (loading) return <div className="loading">加载中...</div>;

  return (
    <div className="data-management">
      <div className="data-header">
        <h2>数据管理</h2>
        <div className="data-actions">
          <button
            className="btn btn-primary"
            onClick={() => setShowUploadModal(true)}
          >
            上传数据
          </button>
          <button
            className="btn btn-secondary"
            onClick={handleDownload}
            disabled={selectedItems.length === 0}
          >
            下载选中
          </button>
          <button
            className="btn btn-danger"
            onClick={handleDelete}
            disabled={selectedItems.length === 0}
          >
            删除选中
          </button>
        </div>
      </div>

      {error && <div className="alert alert-danger">{error}</div>}

      <div className="data-filters">
        <div className="search-box">
          <input
            type="text"
            placeholder="搜索数据..."
            value={searchTerm}
            onChange={handleSearch}
            className="form-control"
          />
        </div>
        <div className="filter-box">
          <select
            value={selectedCategory}
            onChange={handleCategoryChange}
            className="form-control"
          >
            <option value="all">所有类别</option>
            <option value="document">文档</option>
            <option value="image">图片</option>
            <option value="video">视频</option>
            <option value="audio">音频</option>
            <option value="other">其他</option>
          </select>
        </div>
      </div>

      <div className="data-table">
        <table>
          <thead>
            <tr>
              <th>
                <input
                  type="checkbox"
                  checked={selectedItems.length === data.length}
                  onChange={handleSelectAll}
                />
              </th>
              <th onClick={() => handleSort('name')}>
                名称
                {sortField === 'name' && (
                  <span className="sort-icon">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
              <th onClick={() => handleSort('category')}>
                类别
                {sortField === 'category' && (
                  <span className="sort-icon">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
              <th onClick={() => handleSort('size')}>
                大小
                {sortField === 'size' && (
                  <span className="sort-icon">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
              <th onClick={() => handleSort('uploadTime')}>
                上传时间
                {sortField === 'uploadTime' && (
                  <span className="sort-icon">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            {data.map(item => (
              <tr key={item.id}>
                <td>
                  <input
                    type="checkbox"
                    checked={selectedItems.includes(item.id)}
                    onChange={() => handleSelectItem(item.id)}
                  />
                </td>
                <td>{item.name}</td>
                <td>
                  <span className={`category-tag ${item.category}`}>
                    {item.category === 'document' ? '文档' :
                     item.category === 'image' ? '图片' :
                     item.category === 'video' ? '视频' :
                     item.category === 'audio' ? '音频' : '其他'}
                  </span>
                </td>
                <td>{formatFileSize(item.size)}</td>
                <td>{formatDate(item.uploadTime)}</td>
                <td>
                  <div className="action-buttons">
                    <button
                      className="btn btn-sm btn-secondary"
                      onClick={() => window.open(item.downloadUrl)}
                    >
                      下载
                    </button>
                    <button
                      className="btn btn-sm btn-danger"
                      onClick={() => handleDelete([item.id])}
                    >
                      删除
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="pagination">
        <button
          className="btn btn-sm"
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          上一页
        </button>
        <span className="page-info">
          第 {currentPage} 页，共 {totalPages} 页
        </span>
        <button
          className="btn btn-sm"
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          下一页
        </button>
      </div>

      {showUploadModal && (
        <div className="modal">
          <div className="modal-content">
            <h3>上传数据</h3>
            <div className="upload-form">
              <input
                type="file"
                onChange={(e) => setUploadFile(e.target.files[0])}
                className="form-control"
              />
              {uploadProgress > 0 && (
                <div className="progress-bar">
                  <div
                    className="progress"
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                  <span className="progress-text">{uploadProgress}%</span>
                </div>
              )}
              <div className="modal-actions">
                <button
                  className="btn btn-primary"
                  onClick={handleUpload}
                  disabled={!uploadFile}
                >
                  上传
                </button>
                <button
                  className="btn btn-secondary"
                  onClick={() => {
                    setShowUploadModal(false);
                    setUploadFile(null);
                    setUploadProgress(0);
                  }}
                >
                  取消
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default DataManagement; 