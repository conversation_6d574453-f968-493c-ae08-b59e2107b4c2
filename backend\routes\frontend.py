"""
前端路由
"""

from flask import Blueprint, render_template, redirect, request, flash, session, url_for, current_app
import os
import uuid
from datetime import datetime
from werkzeug.utils import secure_filename
from web3 import Web3

frontend_bp = Blueprint('frontend', __name__)

def check_auth(required_role=None):
    """检查用户是否已登录并具有正确的角色

    Args:
        required_role: 需要的角色，如果为None则只检查是否登录

    Returns:
        None: 如果用户已登录并具有正确的角色
        redirect: 如果用户未登录或角色不正确，重定向到登录页面
    """
    # 检查用户是否已登录
    if 'user_id' not in session or not session.get('logged_in', False):
        flash('请先登录', 'warning')
        return redirect('/login')

    # 如果指定了角色，检查用户角色
    if required_role and session.get('role') != required_role:
        flash('您没有权限访问该页面', 'danger')
        return redirect('/login')

    return None

@frontend_bp.route('/')
def index():
    """首页"""
    return render_template('index.html')

@frontend_bp.route('/logout')
def logout():
    """退出登录"""
    print(f"用户退出登录: ID={session.get('user_id')}, 用户名={session.get('username')}")
    session.clear()
    flash('您已成功退出登录', 'success')
    return redirect('/login')

@frontend_bp.route('/login', methods=['GET', 'POST'])
def login():
    """登录页面"""
    error_message = None

    # 测试用户
    TEST_USERS = {
        'admin': {
            'password': 'admin123',
            'role': 'admin',
            'company_name': '系统管理员',
            'id': 1
        },
        'enterprise1': {
            'password': 'enterprise123',
            'role': 'enterprise',
            'company_name': '北京碳排放科技有限公司',
            'id': 2
        },
        'verifier1': {
            'password': 'verifier123',
            'role': 'verifier',
            'company_name': '北京碳核查认证中心',
            'id': 5
        }
    }

    # 如果用户已登录，根据角色重定向到相应页面
    if 'user_id' in session:
        role = session.get('role')
        if role == 'admin':
            return redirect('/admin')
        elif role == 'enterprise':
            return redirect('/enterprise')
        elif role == 'verifier':
            return redirect('/verifier')

    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        print(f"尝试登录: 用户名={username}, 密码={'*' * len(password)}")

        # 验证用户名和密码
        if username in TEST_USERS and TEST_USERS[username]['password'] == password:
            # 登录成功
            session.clear()
            session['user_id'] = TEST_USERS[username]['id']
            session['username'] = username
            session['role'] = TEST_USERS[username]['role']
            session['company_name'] = TEST_USERS[username]['company_name']
            session['logged_in'] = True

            print(f"登录成功: 用户名={username}, 角色={TEST_USERS[username]['role']}")

            # 根据用户角色重定向到相应页面
            if TEST_USERS[username]['role'] == 'admin':
                flash('登录成功，欢迎管理员!', 'success')
                return redirect('/admin')
            elif TEST_USERS[username]['role'] == 'enterprise':
                flash('登录成功，欢迎企业用户!', 'success')
                return redirect('/enterprise')
            elif TEST_USERS[username]['role'] == 'verifier':
                flash('登录成功，欢迎核查机构!', 'success')
                return redirect('/verifier')
        else:
            # 登录失败
            error_message = "用户名或密码不正确"
            flash('用户名或密码不正确', 'danger')
            print(f"登录失败: 用户名={username}")

    return render_template('login.html', error_message=error_message)

@frontend_bp.route('/register')
def register():
    """注册页面"""
    return render_template('register.html')

# 已经有一个logout函数，这个是重复的

@frontend_bp.route('/admin')
def admin():
    """管理员页面"""
    # 检查认证
    auth_result = check_auth(required_role='admin')
    if auth_result:
        return auth_result

    print(f"管理员访问仪表板: 用户ID={session.get('user_id')}, 用户名={session.get('username')}")

    # 导入用户认证模块
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
    from user_auth import get_user_counts, get_recent_users

    # 获取用户统计数据
    counts = get_user_counts()
    user_count = counts['total']
    admin_count = counts['admin']
    enterprise_count = counts['enterprise']
    verifier_count = counts['verifier']

    # 获取最近登录的用户
    recent_users = get_recent_users(5)

    # 系统状态
    system_status = {
        'database_connected': True,
        'blockchain_connected': False,
        'event_listener_running': False
    }

    return render_template('admin/index.html',
                          user_count=user_count,
                          admin_count=admin_count,
                          enterprise_count=enterprise_count,
                          verifier_count=verifier_count,
                          recent_users=recent_users,
                          system_status=system_status,
                          current_user=session)

@frontend_bp.route('/admin/blockchain/config', methods=['GET', 'POST'])
def admin_blockchain_config():
    """管理员区块链配置页面"""
    # 检查认证
    auth_result = check_auth(required_role='admin')
    if auth_result:
        return auth_result

    # 从环境变量或配置文件中获取区块链配置
    ethereum_node_url = os.environ.get('ETHEREUM_NODE_URL', 'http://127.0.0.1:8545')
    contract_address = os.environ.get('CONTRACT_ADDRESS', '')
    admin_address = os.environ.get('ADMIN_ADDRESS', '')
    admin_private_key = os.environ.get('ADMIN_PRIVATE_KEY', '')

    # 获取Ganache账户
    ganache_accounts = None
    success_message = None
    error_message = None

    try:
        # 连接到以太坊节点
        web3 = Web3(Web3.HTTPProvider(ethereum_node_url))
        if web3.is_connected():
            # 获取账户列表
            accounts = web3.eth.accounts
            ganache_accounts = []
            for account in accounts:
                balance = web3.eth.get_balance(account)
                ganache_accounts.append({
                    'address': account,
                    'balance': web3.from_wei(balance, 'ether')
                })

            print(f"成功连接到以太坊节点，获取到 {len(ganache_accounts)} 个账户")
        else:
            error_message = "无法连接到以太坊节点，请确保Ganache正在运行"
    except Exception as e:
        error_message = f"连接以太坊节点时发生错误: {str(e)}"

    # 处理表单提交
    if request.method == 'POST':
        try:
            # 获取表单数据
            ethereum_node_url = request.form.get('ethereum_node_url')
            contract_address = request.form.get('contract_address')
            admin_address = request.form.get('admin_address')
            admin_private_key = request.form.get('admin_private_key')

            # 更新.env文件
            env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), '.env')

            # 读取现有.env文件内容
            env_content = ""
            if os.path.exists(env_path):
                with open(env_path, 'r') as f:
                    env_content = f.read()

            # 更新配置
            env_vars = {
                'ETHEREUM_NODE_URL': ethereum_node_url,
                'CONTRACT_ADDRESS': contract_address,
                'ADMIN_ADDRESS': admin_address,
                'ADMIN_PRIVATE_KEY': admin_private_key
            }

            for key, value in env_vars.items():
                if f'{key}=' in env_content:
                    env_content = '\n'.join([line if not line.startswith(f'{key}=') else f'{key}={value}' for line in env_content.split('\n')])
                else:
                    env_content += f'\n{key}={value}'

            # 写入.env文件
            with open(env_path, 'w') as f:
                f.write(env_content)

            success_message = "区块链配置已成功保存"

            # 重新加载环境变量
            os.environ['ETHEREUM_NODE_URL'] = ethereum_node_url
            os.environ['CONTRACT_ADDRESS'] = contract_address
            os.environ['ADMIN_ADDRESS'] = admin_address
            os.environ['ADMIN_PRIVATE_KEY'] = admin_private_key
        except Exception as e:
            error_message = f"保存配置时发生错误: {str(e)}"

    config = {
        'ETHEREUM_NODE_URL': ethereum_node_url,
        'CONTRACT_ADDRESS': contract_address,
        'ADMIN_ADDRESS': admin_address,
        'ADMIN_PRIVATE_KEY': admin_private_key,
        'ENTERPRISE_1_ADDRESS': os.environ.get('ENTERPRISE_1_ADDRESS', ''),
        'ENTERPRISE_1_KEY': os.environ.get('ENTERPRISE_1_KEY', ''),
        'ENTERPRISE_2_ADDRESS': os.environ.get('ENTERPRISE_2_ADDRESS', ''),
        'ENTERPRISE_2_KEY': os.environ.get('ENTERPRISE_2_KEY', ''),
        'ENTERPRISE_3_ADDRESS': os.environ.get('ENTERPRISE_3_ADDRESS', ''),
        'ENTERPRISE_3_KEY': os.environ.get('ENTERPRISE_3_KEY', ''),
        'VERIFIER_1_ADDRESS': os.environ.get('VERIFIER_1_ADDRESS', ''),
        'VERIFIER_1_KEY': os.environ.get('VERIFIER_1_KEY', ''),
        'VERIFIER_2_ADDRESS': os.environ.get('VERIFIER_2_ADDRESS', ''),
        'VERIFIER_2_KEY': os.environ.get('VERIFIER_2_KEY', '')
    }

    return render_template('admin/blockchain_config.html',
                          config=config,
                          success_message=success_message,
                          error_message=error_message,
                          ganache_accounts=ganache_accounts,
                          current_user=session)

@frontend_bp.route('/enterprise')
def enterprise():
    """企业页面"""
    # 检查认证
    auth_result = check_auth(required_role='enterprise')
    if auth_result:
        return auth_result

    print(f"企业访问仪表板: 用户ID={session.get('user_id')}, 用户名={session.get('username')}")

    # 导入用户认证模块
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
    from user_auth import get_user_by_id

    # 获取用户信息
    user = get_user_by_id(session.get('user_id'))
    if not user:
        # 如果无法从数据库获取用户信息，使用会话中的信息
        user = {
            'id': session.get('user_id'),
            'username': session.get('username'),
            'company_name': session.get('company_name'),
            'blockchain_address': '******************************************'
        }

    # 排放数据
    emission_data = [
        {
            'id': 1,
            'source': '电力消耗',
            'amount': 1200,
            'unit': 'tCO2e',
            'date': '2025-01-15',
            'status': '已核查'
        },
        {
            'id': 2,
            'source': '燃料燃烧',
            'amount': 800,
            'unit': 'tCO2e',
            'date': '2025-02-20',
            'status': '待核查'
        },
        {
            'id': 3,
            'source': '工业过程',
            'amount': 500,
            'unit': 'tCO2e',
            'date': '2025-03-10',
            'status': '已核查'
        }
    ]

    # 交易数据
    transaction_data = [
        {
            'id': 1,
            'type': '购买',
            'amount': 200,
            'unit': 'tCO2e',
            'price': 50,
            'total': 10000,
            'date': '2025-02-05',
            'status': '已完成'
        },
        {
            'id': 2,
            'type': '出售',
            'amount': 100,
            'unit': 'tCO2e',
            'price': 55,
            'total': 5500,
            'date': '2025-03-15',
            'status': '已完成'
        }
    ]

    # 配额数据
    quota_data = {
        'total': 2000,
        'used': 1500,
        'remaining': 500,
        'year': 2025
    }

    return render_template('enterprise/index.html',
                          user=user,
                          emission_data=emission_data,
                          transaction_data=transaction_data,
                          quota_data=quota_data,
                          current_user=session)

@frontend_bp.route('/verifier')
def verifier():
    """核查机构页面"""
    # 检查认证
    auth_result = check_auth(required_role='verifier')
    if auth_result:
        return auth_result

    print(f"核查机构访问仪表板: 用户ID={session.get('user_id')}, 用户名={session.get('username')}")

    # 导入用户认证模块
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
    from user_auth import get_user_by_id

    # 获取用户信息
    user = get_user_by_id(session.get('user_id'))
    if not user:
        # 如果无法从数据库获取用户信息，使用会话中的信息
        user = {
            'id': session.get('user_id'),
            'username': session.get('username'),
            'company_name': session.get('company_name'),
            'blockchain_address': '0x78731D3Ca6b7E34aC0F824c42a7cC18A495cabaB'
        }

    # 待核查的排放数据
    pending_verifications = [
        {
            'id': 1,
            'enterprise_name': '北京碳排放科技有限公司',
            'source': '电力消耗',
            'amount': 1200,
            'unit': 'tCO2e',
            'submission_date': '2025-01-15',
            'status': '待核查'
        },
        {
            'id': 2,
            'enterprise_name': '上海绿色能源有限公司',
            'source': '燃料燃烧',
            'amount': 800,
            'unit': 'tCO2e',
            'submission_date': '2025-02-20',
            'status': '待核查'
        }
    ]

    # 已完成的核查记录
    completed_verifications = [
        {
            'id': 3,
            'enterprise_name': '北京碳排放科技有限公司',
            'source': '工业过程',
            'amount': 500,
            'unit': 'tCO2e',
            'verification_date': '2025-03-10',
            'status': '已通过',
            'comments': '数据准确，计算方法正确'
        },
        {
            'id': 4,
            'enterprise_name': '上海绿色能源有限公司',
            'source': '废弃物处理',
            'amount': 300,
            'unit': 'tCO2e',
            'verification_date': '2025-03-15',
            'status': '已拒绝',
            'comments': '数据不完整，需要补充证明材料'
        }
    ]

    # 统计数据
    statistics = {
        'total_verifications': 10,
        'pending_verifications': 2,
        'approved_verifications': 7,
        'rejected_verifications': 1
    }

    return render_template('verifier/index.html',
                          user=user,
                          pending_verifications=pending_verifications,
                          completed_verifications=completed_verifications,
                          statistics=statistics,
                          current_user=session)

@frontend_bp.route('/demo')
def demo():
    """演示页面"""
    return render_template('demo/index.html')

@frontend_bp.route('/demo/admin')
def demo_admin():
    """管理员演示页面"""
    return render_template('demo/admin.html')

@frontend_bp.route('/demo/enterprise')
def demo_enterprise():
    """企业演示页面"""
    return render_template('demo/enterprise.html')

@frontend_bp.route('/demo/verifier')
def demo_verifier():
    """核查机构演示页面"""
    return render_template('demo/verifier.html')

@frontend_bp.route('/demo/admin/settings')
def demo_admin_settings():
    """管理员设置演示页面"""
    return render_template('demo/admin_settings.html')

@frontend_bp.route('/demo/admin/blockchain/config')
def demo_admin_blockchain_config():
    """管理员区块链配置演示页面"""
    # 模拟Ganache账户
    ganache_accounts = [
        {
            'address': '******************************************',
            'balance': 100.0
        },
        {
            'address': '******************************************',
            'balance': 100.0
        },
        {
            'address': '******************************************',
            'balance': 100.0
        },
        {
            'address': '******************************************',
            'balance': 100.0
        },
        {
            'address': '0x78731D3Ca6b7E34aC0F824c42a7cC18A495cabaB',
            'balance': 100.0
        },
        {
            'address': '0x617F2E2fD72FD9D5503197092aC168c91465E7f2',
            'balance': 100.0
        },
        {
            'address': '0x17F6AD8Ef982297579C203069C1DbfFE4348c372',
            'balance': 100.0
        },
        {
            'address': '******************************************',
            'balance': 100.0
        },
        {
            'address': '******************************************',
            'balance': 100.0
        },
        {
            'address': '******************************************',
            'balance': 100.0
        }
    ]

    return render_template('demo/admin_blockchain_config.html',
                          config={
                              'ETHEREUM_NODE_URL': 'http://127.0.0.1:8545',
                              'CONTRACT_ADDRESS': '******************************************',
                              'ADMIN_ADDRESS': '******************************************',
                              'ADMIN_PRIVATE_KEY': '0x0000000000000000000000000000000000000000000000000000000000000000',
                              'ENTERPRISE_1_ADDRESS': '******************************************',
                              'ENTERPRISE_1_KEY': '0x0000000000000000000000000000000000000000000000000000000000000000',
                              'ENTERPRISE_2_ADDRESS': '******************************************',
                              'ENTERPRISE_2_KEY': '0x0000000000000000000000000000000000000000000000000000000000000000',
                              'ENTERPRISE_3_ADDRESS': '******************************************',
                              'ENTERPRISE_3_KEY': '0x0000000000000000000000000000000000000000000000000000000000000000',
                              'VERIFIER_1_ADDRESS': '0x78731D3Ca6b7E34aC0F824c42a7cC18A495cabaB',
                              'VERIFIER_1_KEY': '0x0000000000000000000000000000000000000000000000000000000000000000',
                              'VERIFIER_2_ADDRESS': '0x617F2E2fD72FD9D5503197092aC168c91465E7f2',
                              'VERIFIER_2_KEY': '0x0000000000000000000000000000000000000000000000000000000000000000'
                          },
                          success_message="区块链配置已成功加载，智能合约已部署",
                          error_message=None,
                          ganache_accounts=ganache_accounts,
                          current_user={'username': 'admin', 'role': 'admin'})

@frontend_bp.route('/demo/admin/users')
def demo_admin_users():
    """管理员用户管理演示页面"""
    return render_template('demo/admin_users.html')

@frontend_bp.route('/demo/admin/quotas')
def demo_admin_quotas():
    """管理员配额管理演示页面"""
    return render_template('demo/admin_quotas.html')

@frontend_bp.route('/demo/admin/penalties')
def demo_admin_penalties():
    """管理员惩罚管理演示页面"""
    return render_template('demo/admin_penalties.html')

@frontend_bp.route('/demo/admin/reports')
def demo_admin_reports():
    """管理员报告管理演示页面"""
    return render_template('demo/admin_reports.html')

@frontend_bp.route('/demo/enterprise/emissions')
def demo_enterprise_emissions():
    """企业排放管理演示页面"""
    return render_template('demo/enterprise_emissions.html')

@frontend_bp.route('/demo/enterprise/transactions')
def demo_enterprise_transactions():
    """企业交易管理演示页面"""
    return render_template('demo/enterprise_transactions.html')

@frontend_bp.route('/demo/enterprise/reports')
def demo_enterprise_reports():
    """企业报告管理演示页面"""
    return render_template('demo/enterprise_reports.html')

@frontend_bp.route('/demo/enterprise/calculator')
def demo_enterprise_calculator():
    """企业碳计算器演示页面"""
    return render_template('demo/enterprise_calculator.html')

@frontend_bp.route('/demo/verifier/verifications')
def demo_verifier_verifications():
    """核查机构核查管理演示页面"""
    return render_template('demo/verifier_verifications.html')

@frontend_bp.route('/demo/verifier/reports')
def demo_verifier_reports():
    """核查机构报告管理演示页面"""
    return render_template('demo/verifier_reports.html')

# 添加管理员功能路由
@frontend_bp.route('/admin/users')
def admin_users():
    """管理员用户管理页面"""
    # 检查认证
    auth_result = check_auth(required_role='admin')
    if auth_result:
        return auth_result

    # 导入用户认证模块
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
    from user_auth import get_all_users

    # 获取所有用户
    users = get_all_users()

    return render_template('admin/users.html',
                          users=users,
                          current_user=session)

@frontend_bp.route('/admin/users/add')
def admin_users_add():
    """管理员添加用户页面"""
    # 检查认证
    auth_result = check_auth(required_role='admin')
    if auth_result:
        return auth_result

    return render_template('admin/user_add.html',
                          current_user=session)

@frontend_bp.route('/admin/quotas')
def admin_quotas():
    """管理员配额管理页面"""
    # 检查认证
    auth_result = check_auth(required_role='admin')
    if auth_result:
        return auth_result

    # 模拟配额数据
    quotas = [
        {
            'id': 1,
            'enterprise_name': '北京碳排放科技有限公司',
            'total': 2000,
            'used': 1500,
            'remaining': 500,
            'year': 2025
        },
        {
            'id': 2,
            'enterprise_name': '上海绿色能源有限公司',
            'total': 3000,
            'used': 2000,
            'remaining': 1000,
            'year': 2025
        },
        {
            'id': 3,
            'enterprise_name': '广州环保科技有限公司',
            'total': 1500,
            'used': 1000,
            'remaining': 500,
            'year': 2025
        }
    ]

    return render_template('admin/quotas.html',
                          quotas=quotas,
                          current_user=session)

@frontend_bp.route('/admin/quotas/allocate')
def admin_quotas_allocate():
    """管理员分配配额页面"""
    # 检查认证
    auth_result = check_auth(required_role='admin')
    if auth_result:
        return auth_result

    return render_template('admin/quota_allocate.html',
                          current_user=session)

@frontend_bp.route('/admin/penalties')
def admin_penalties():
    """管理员惩罚管理页面"""
    # 检查认证
    auth_result = check_auth(required_role='admin')
    if auth_result:
        return auth_result

    # 模拟惩罚数据
    penalties = [
        {
            'id': 1,
            'enterprise_name': '北京碳排放科技有限公司',
            'penalty_type': '超额排放',
            'amount': 10000,
            'issue_date': '2025-04-15',
            'status': '已处理'
        },
        {
            'id': 2,
            'enterprise_name': '上海绿色能源有限公司',
            'penalty_type': '数据造假',
            'amount': 50000,
            'issue_date': '2025-04-20',
            'status': '待处理'
        }
    ]

    return render_template('admin/penalties.html',
                          penalties=penalties,
                          current_user=session)

@frontend_bp.route('/admin/reports')
def admin_reports():
    """管理员报告管理页面"""
    # 检查认证
    auth_result = check_auth(required_role='admin')
    if auth_result:
        return auth_result

    # 模拟报告数据
    reports = [
        {
            'id': 1,
            'title': '2025年第一季度碳排放报告',
            'type': '季度报告',
            'generation_date': '2025-04-10',
            'status': '已发布'
        },
        {
            'id': 2,
            'title': '2025年企业碳排放情况汇总',
            'type': '年度报告',
            'generation_date': '2025-04-15',
            'status': '草稿'
        }
    ]

    return render_template('admin/reports.html',
                          reports=reports,
                          current_user=session)

@frontend_bp.route('/admin/reports/generate')
def admin_reports_generate():
    """管理员生成报告页面"""
    # 检查认证
    auth_result = check_auth(required_role='admin')
    if auth_result:
        return auth_result

    return render_template('admin/report_generate.html',
                          current_user=session)

@frontend_bp.route('/admin/settings')
def admin_settings():
    """管理员设置页面"""
    # 检查认证
    auth_result = check_auth(required_role='admin')
    if auth_result:
        return auth_result

    return render_template('admin/settings.html',
                          current_user=session)

@frontend_bp.route('/admin/profile')
def admin_profile():
    """管理员个人资料页面"""
    # 检查认证
    auth_result = check_auth(required_role='admin')
    if auth_result:
        return auth_result

    # 导入用户认证模块
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
    from user_auth import get_user_by_id

    # 获取用户信息
    user = get_user_by_id(session.get('user_id'))
    if not user:
        # 如果无法从数据库获取用户信息，使用会话中的信息
        user = {
            'id': session.get('user_id'),
            'username': session.get('username'),
            'company_name': session.get('company_name'),
            'email': '<EMAIL>',
            'blockchain_address': '******************************************'
        }

    return render_template('admin/profile.html',
                          user=user,
                          current_user=session)

# 添加企业功能路由
@frontend_bp.route('/enterprise/emissions')
def enterprise_emissions():
    """企业排放管理页面"""
    # 检查认证
    auth_result = check_auth(required_role='enterprise')
    if auth_result:
        return auth_result

    # 获取排放数据
    from backend.models.emission import Emission

    # 获取查询参数
    source = request.args.get('source')
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    status = request.args.get('status')

    # 构建查询
    query = Emission.query.filter_by(enterprise_id=session.get('user_id'))

    # 应用筛选条件
    if source:
        query = query.filter_by(emission_source=source)
    if date_from:
        query = query.filter(Emission.emission_period_start >= date_from)
    if date_to:
        query = query.filter(Emission.emission_period_end <= date_to)
    if status:
        query = query.filter_by(status=status)

    # 执行查询
    emissions_data = query.order_by(Emission.created_at.desc()).all()

    # 转换为前端需要的格式
    emissions = []
    for emission in emissions_data:
        emissions.append({
            'id': emission.id,
            'source': emission.emission_source,
            'amount': emission.emission_amount,
            'unit': emission.emission_unit,
            'date': emission.emission_period_start.strftime('%Y-%m-%d') if emission.emission_period_start else '',
            'status': emission.status
        })

    # 获取统计数据
    stats = {
        'total': 0,
        'verified': 0,
        'pending': 0
    }

    # 计算统计数据
    for emission in emissions_data:
        if emission.emission_unit == 'tCO2e':
            if emission.status == 'verified':
                stats['verified'] += emission.emission_amount
                stats['total'] += emission.emission_amount
            elif emission.status in ['submitted', 'pending']:
                stats['pending'] += emission.emission_amount
                stats['total'] += emission.emission_amount

    return render_template('enterprise/emissions.html',
                          emissions=emissions,
                          stats=stats,
                          current_user=session)

@frontend_bp.route('/enterprise/emissions/<int:emission_id>')
def enterprise_emission_detail(emission_id):
    """企业排放数据详情页面"""
    # 检查认证
    auth_result = check_auth(required_role='enterprise')
    if auth_result:
        return auth_result

    # 获取排放数据
    from backend.models.emission import Emission
    from backend.models.verification import Verification

    # 查询排放数据
    emission = Emission.query.get(emission_id)
    if not emission:
        flash('排放数据不存在', 'danger')
        return redirect(url_for('frontend.enterprise_emissions'))

    # 检查权限
    if emission.enterprise_id != session.get('user_id'):
        flash('无权访问此排放数据', 'danger')
        return redirect(url_for('frontend.enterprise_emissions'))

    # 获取核查信息
    verification = Verification.query.filter_by(emission_id=emission.id).first()

    # 构建时间线
    timeline = []

    # 创建时间
    timeline.append({
        'time': emission.created_at.strftime('%Y-%m-%d %H:%M:%S'),
        'title': '创建排放数据',
        'description': f'排放数据已创建，状态为"{emission.status}"'
    })

    # 提交时间
    if emission.status in ['submitted', 'pending', 'verified', 'rejected']:
        timeline.append({
            'time': emission.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
            'title': '提交排放数据',
            'description': '排放数据已提交，等待核查'
        })

    # 核查时间
    if verification:
        timeline.append({
            'time': verification.verification_time.strftime('%Y-%m-%d %H:%M:%S') if verification.verification_time else '未知',
            'title': '核查完成',
            'description': f'核查结论: {"通过" if verification.conclusion == "approved" else "不通过"}'
        })

    return render_template('enterprise/emission_detail.html',
                          emission=emission,
                          verification=verification,
                          timeline=timeline,
                          current_user=session)

@frontend_bp.route('/enterprise/emissions/add', methods=['GET', 'POST'])
def enterprise_emissions_add():
    """企业添加排放数据页面"""
    # 检查认证
    auth_result = check_auth(required_role='enterprise')
    if auth_result:
        return auth_result

    if request.method == 'POST':
        from backend.models.emission import Emission

        # 允许的文件扩展名
        ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'jpeg', 'png'}

        def allowed_file(filename):
            """检查文件扩展名是否允许"""
            return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

        # 处理文件上传
        proof_file_path = None
        if 'proof_file' in request.files:
            file = request.files['proof_file']
            if file and file.filename and allowed_file(file.filename):
                # 生成安全的文件名
                filename = secure_filename(file.filename)
                # 添加唯一标识符，避免文件名冲突
                unique_filename = f"{uuid.uuid4().hex}_{filename}"
                # 确保上传目录存在
                upload_folder = os.path.join(current_app.static_folder, 'uploads')
                os.makedirs(upload_folder, exist_ok=True)
                # 保存文件
                file_path = os.path.join(upload_folder, unique_filename)
                file.save(file_path)
                # 存储相对路径
                proof_file_path = f"uploads/{unique_filename}"

        # 创建新的排放数据
        try:
            emission = Emission(
                enterprise_id=session.get('user_id'),
                emission_source=request.form['emission_source'],
                calculation_method=request.form['calculation_method'],
                emission_amount=float(request.form['emission_amount']),
                emission_unit=request.form['emission_unit'],
                emission_period_start=datetime.strptime(request.form['emission_period_start'], '%Y-%m-%d'),
                emission_period_end=datetime.strptime(request.form['emission_period_end'], '%Y-%m-%d'),
                status='draft',  # 初始状态为草稿
                description=request.form.get('description', ''),
                proof_file_path=proof_file_path
            )

            # 如果是提交操作，更新状态
            if request.form.get('action') == 'submit':
                emission.status = 'submitted'

            from backend import db
            db.session.add(emission)
            db.session.commit()

            flash('排放数据已成功创建', 'success')
            return redirect(url_for('frontend.enterprise_emissions'))
        except Exception as e:
            flash(f'创建排放数据失败: {str(e)}', 'danger')

    return render_template('enterprise/emission_add.html',
                          current_user=session)

@frontend_bp.route('/enterprise/transactions')
def enterprise_transactions():
    """企业交易管理页面"""
    # 检查认证
    auth_result = check_auth(required_role='enterprise')
    if auth_result:
        return auth_result

    # 模拟交易数据
    transactions = [
        {
            'id': 1,
            'type': '购买',
            'amount': 200,
            'unit': 'tCO2e',
            'price': 50,
            'total': 10000,
            'date': '2025-02-05',
            'status': '已完成'
        },
        {
            'id': 2,
            'type': '出售',
            'amount': 100,
            'unit': 'tCO2e',
            'price': 55,
            'total': 5500,
            'date': '2025-03-15',
            'status': '已完成'
        }
    ]

    return render_template('enterprise/transactions.html',
                          transactions=transactions,
                          current_user=session)

@frontend_bp.route('/enterprise/transactions/new')
def enterprise_transactions_new():
    """企业新建交易页面"""
    # 检查认证
    auth_result = check_auth(required_role='enterprise')
    if auth_result:
        return auth_result

    return render_template('enterprise/transaction_new.html',
                          current_user=session)

@frontend_bp.route('/enterprise/reports')
def enterprise_reports():
    """企业报告管理页面"""
    # 检查认证
    auth_result = check_auth(required_role='enterprise')
    if auth_result:
        return auth_result

    # 模拟报告数据
    reports = [
        {
            'id': 1,
            'title': '2025年第一季度碳排放报告',
            'type': '季度报告',
            'generation_date': '2025-04-10',
            'status': '已提交'
        },
        {
            'id': 2,
            'title': '2025年企业碳排放情况汇总',
            'type': '年度报告',
            'generation_date': '2025-04-15',
            'status': '草稿'
        }
    ]

    return render_template('enterprise/reports.html',
                          reports=reports,
                          current_user=session)

@frontend_bp.route('/enterprise/reports/generate')
def enterprise_reports_generate():
    """企业生成报告页面"""
    # 检查认证
    auth_result = check_auth(required_role='enterprise')
    if auth_result:
        return auth_result

    return render_template('enterprise/report_generate.html',
                          current_user=session)

@frontend_bp.route('/enterprise/calculator')
def enterprise_calculator():
    """企业碳计算器页面"""
    # 检查认证
    auth_result = check_auth(required_role='enterprise')
    if auth_result:
        return auth_result

    return render_template('enterprise/calculator.html',
                          current_user=session)

@frontend_bp.route('/enterprise/profile')
def enterprise_profile():
    """企业个人资料页面"""
    # 检查认证
    auth_result = check_auth(required_role='enterprise')
    if auth_result:
        return auth_result

    # 导入用户认证模块
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
    from user_auth import get_user_by_id

    # 获取用户信息
    user = get_user_by_id(session.get('user_id'))
    if not user:
        # 如果无法从数据库获取用户信息，使用会话中的信息
        user = {
            'id': session.get('user_id'),
            'username': session.get('username'),
            'company_name': session.get('company_name'),
            'email': '<EMAIL>',
            'blockchain_address': '******************************************'
        }

    return render_template('enterprise/profile.html',
                          user=user,
                          current_user=session)

# 添加核查机构功能路由
@frontend_bp.route('/verifier/verifications')
def verifier_verifications():
    """核查机构核查管理页面"""
    # 检查认证
    auth_result = check_auth(required_role='verifier')
    if auth_result:
        return auth_result

    # 待核查的排放数据
    pending_verifications = [
        {
            'id': 1,
            'enterprise_name': '北京碳排放科技有限公司',
            'source': '电力消耗',
            'amount': 1200,
            'unit': 'tCO2e',
            'submission_date': '2025-01-15',
            'status': '待核查'
        },
        {
            'id': 2,
            'enterprise_name': '上海绿色能源有限公司',
            'source': '燃料燃烧',
            'amount': 800,
            'unit': 'tCO2e',
            'submission_date': '2025-02-20',
            'status': '待核查'
        }
    ]

    # 已完成的核查记录
    completed_verifications = [
        {
            'id': 3,
            'enterprise_name': '北京碳排放科技有限公司',
            'source': '工业过程',
            'amount': 500,
            'unit': 'tCO2e',
            'verification_date': '2025-03-10',
            'status': '已通过',
            'comments': '数据准确，计算方法正确'
        },
        {
            'id': 4,
            'enterprise_name': '上海绿色能源有限公司',
            'source': '废弃物处理',
            'amount': 300,
            'unit': 'tCO2e',
            'verification_date': '2025-03-15',
            'status': '已拒绝',
            'comments': '数据不完整，需要补充证明材料'
        }
    ]

    return render_template('verifier/verifications.html',
                          pending_verifications=pending_verifications,
                          completed_verifications=completed_verifications,
                          current_user=session)

@frontend_bp.route('/verifier/verification/<int:verification_id>')
def verifier_verification_detail(verification_id):
    """核查机构核查详情页面"""
    # 检查认证
    auth_result = check_auth(required_role='verifier')
    if auth_result:
        return auth_result

    # 模拟核查详情数据
    verification = {
        'id': verification_id,
        'enterprise_name': '北京碳排放科技有限公司',
        'source': '电力消耗',
        'amount': 1200,
        'unit': 'tCO2e',
        'submission_date': '2025-01-15',
        'status': '待核查',
        'calculation_method': '基于电力消耗量和排放因子计算',
        'emission_period_start': '2025-01-01',
        'emission_period_end': '2025-01-31',
        'proof_file_path': '/uploads/proof_1.pdf'
    }

    return render_template('verifier/verification_detail.html',
                          verification=verification,
                          current_user=session)

@frontend_bp.route('/verifier/reports')
def verifier_reports():
    """核查机构报告管理页面"""
    # 检查认证
    auth_result = check_auth(required_role='verifier')
    if auth_result:
        return auth_result

    # 模拟报告数据
    reports = [
        {
            'id': 1,
            'title': '北京碳排放科技有限公司核查报告',
            'enterprise_name': '北京碳排放科技有限公司',
            'generation_date': '2025-03-15',
            'status': '已发布'
        },
        {
            'id': 2,
            'title': '上海绿色能源有限公司核查报告',
            'enterprise_name': '上海绿色能源有限公司',
            'generation_date': '2025-03-20',
            'status': '草稿'
        }
    ]

    return render_template('verifier/reports.html',
                          reports=reports,
                          current_user=session)

@frontend_bp.route('/verifier/reports/generate')
def verifier_reports_generate():
    """核查机构生成报告页面"""
    # 检查认证
    auth_result = check_auth(required_role='verifier')
    if auth_result:
        return auth_result

    return render_template('verifier/report_generate.html',
                          current_user=session)

@frontend_bp.route('/verifier/enterprises')
def verifier_enterprises():
    """核查机构企业管理页面"""
    # 检查认证
    auth_result = check_auth(required_role='verifier')
    if auth_result:
        return auth_result

    # 模拟企业数据
    enterprises = [
        {
            'id': 1,
            'name': '北京碳排放科技有限公司',
            'credit_code': '91110000123456789A',
            'address': '北京市朝阳区',
            'contact_name': '张三',
            'contact_phone': '13800000001'
        },
        {
            'id': 2,
            'name': '上海绿色能源有限公司',
            'credit_code': '91310000123456789B',
            'address': '上海市浦东新区',
            'contact_name': '李四',
            'contact_phone': '13800000002'
        },
        {
            'id': 3,
            'name': '广州环保科技有限公司',
            'credit_code': '91440000123456789C',
            'address': '广州市天河区',
            'contact_name': '王五',
            'contact_phone': '13800000003'
        }
    ]

    return render_template('verifier/enterprises.html',
                          enterprises=enterprises,
                          current_user=session)

@frontend_bp.route('/verifier/profile')
def verifier_profile():
    """核查机构个人资料页面"""
    # 检查认证
    auth_result = check_auth(required_role='verifier')
    if auth_result:
        return auth_result

    # 导入用户认证模块
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
    from user_auth import get_user_by_id

    # 获取用户信息
    user = get_user_by_id(session.get('user_id'))
    if not user:
        # 如果无法从数据库获取用户信息，使用会话中的信息
        user = {
            'id': session.get('user_id'),
            'username': session.get('username'),
            'company_name': session.get('company_name'),
            'email': '<EMAIL>',
            'blockchain_address': '0x78731D3Ca6b7E34aC0F824c42a7cC18A495cabaB'
        }

    return render_template('verifier/profile.html',
                          user=user,
                          current_user=session)
