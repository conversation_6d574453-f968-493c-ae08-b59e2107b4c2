# 碳排放管理系统演示指南

本文档提供了碳排放管理系统的演示指南，帮助您顺利进行系统演示。

## 演示准备

### 环境要求

- Python 3.8+
- MySQL 5.7+
- 浏览器（Chrome、Firefox等）

### 演示前准备

1. 确保数据库已正确配置
   - 检查`.env`文件中的数据库连接信息是否正确
   - 数据库名：ces
   - 用户名：wuhong
   - 密码：D7mH8rZ7a7Z2kJa8
   - 主机：***********
   - 端口：3306

2. 安装依赖
   ```bash
   pip install -r requirements.txt
   ```

3. 初始化数据库（如果尚未初始化）
   ```bash
   python init_db.py
   ```

4. 生成演示数据（如果尚未生成）
   ```bash
   python generate_demo_data.py
   ```

## 演示流程

### 1. 启动系统

使用一键启动脚本启动系统：

```bash
python start.py
```

系统将自动：
- 检查数据库连接和初始化状态
- 检查是否需要生成演示数据
- 启动演示服务器
- 打开浏览器访问系统

### 2. 系统登录

系统提供了以下演示账号：

- **管理员**：admin / admin123
- **企业用户**：enterprise1 / password123
- **核查机构**：verifier1 / password123

在演示中，我们将主要使用企业用户账号进行演示。

### 3. 企业用户功能演示

#### 3.1 仪表板

首先展示企业用户的仪表板，包括：
- 总排放量统计
- 当前配额统计
- 核查通过率
- 排放数据趋势图
- 最近排放数据列表
- 最近交易记录列表

重点说明：
- 仪表板提供了企业碳排放的整体情况
- 趋势图可以帮助企业了解排放变化
- 数据列表可以快速查看最新记录

#### 3.2 排放数据管理

点击导航栏的"排放数据"，展示排放数据管理功能：
- 查看所有排放数据记录
- 点击"提交新数据"按钮，展示数据提交表单
- 填写表单并提交（可以不实际提交）
- 展示数据详情页面

重点说明：
- 企业可以提交各种排放源的排放数据
- 系统支持多种计算方法
- 提交的数据需要经过核查机构核查
- 核查通过的数据将记录到区块链，确保不可篡改

#### 3.3 碳交易功能

点击导航栏的"碳交易"，展示碳交易功能：
- 查看交易历史记录
- 点击"创建交易"按钮，展示交易创建表单
- 填写表单并提交（可以不实际提交）
- 展示交易详情页面

重点说明：
- 企业可以出售多余的碳配额
- 企业可以购买其他企业的碳配额
- 交易完成后，系统自动更新双方的配额
- 交易记录保存在区块链上，确保交易透明和不可篡改

#### 3.4 碳计算器

点击导航栏的"碳计算器"，展示碳计算器功能：
- 展示计算表单，包括各种排放源的输入字段
- 填写表单并计算（可以不实际计算）
- 展示计算结果，包括总排放量和各排放源的明细

重点说明：
- 碳计算器可以帮助企业快速估算碳排放量
- 支持多种排放源的计算
- 计算结果可以保存为排放数据记录

#### 3.5 预测分析

点击导航栏的"预测分析"，展示预测分析功能：
- 展示历史排放数据趋势图
- 展示预测结果图表
- 展示预测模型的详细信息

重点说明：
- 系统基于历史数据预测未来排放趋势
- 预测结果可以帮助企业制定减排计划
- 支持多种预测模型

#### 3.6 报告生成

点击导航栏的"报告生成"，展示报告生成功能：
- 展示报告类型选择和参数设置表单
- 选择"排放报告"类型，设置报告期间
- 点击"生成报告"按钮（可以不实际生成）
- 展示报告预览页面

重点说明：
- 系统可以自动生成多种类型的报告
- 报告包含详细的数据分析和图表
- 报告可以导出为PDF或打印

### 4. 核查机构功能演示

退出登录，使用核查机构账号（verifier1 / password123）登录系统。

#### 4.1 核查任务

点击导航栏的"核查任务"，展示核查任务功能：
- 查看待核查的排放数据列表
- 点击某条记录，展示核查表单
- 填写核查意见并提交（可以不实际提交）

重点说明：
- 核查机构负责审核企业提交的排放数据
- 核查结果将影响企业的合规状态
- 核查记录保存在区块链上，确保核查过程透明和不可篡改

### 5. 管理员功能演示

退出登录，使用管理员账号（admin / admin123）登录系统。

#### 5.1 用户管理

点击导航栏的"用户管理"，展示用户管理功能：
- 查看所有用户列表
- 点击"添加用户"按钮，展示用户创建表单
- 点击某个用户，展示用户详情和编辑选项

重点说明：
- 管理员可以管理所有用户
- 可以添加、编辑、删除用户
- 可以设置用户角色和权限

#### 5.2 系统监控

点击导航栏的"系统监控"，展示系统监控功能：
- 查看系统整体统计数据
- 查看活动日志
- 查看系统配置

重点说明：
- 管理员可以监控整个系统的运行状态
- 可以查看所有用户的活动记录
- 可以配置系统参数

## 演示重点

在演示过程中，重点强调以下几点：

1. **系统的完整性**：系统包含用户管理、排放数据管理、核查、交易、计算器、预测分析、报告生成等完整功能。

2. **区块链集成**：关键数据（排放数据、核查记录、交易记录）保存在区块链上，确保数据不可篡改和透明性。

3. **数据可视化**：系统提供丰富的图表和可视化功能，帮助用户直观地了解数据。

4. **预测分析**：系统使用机器学习算法预测未来排放趋势，帮助企业制定减排计划。

5. **自动化报告**：系统可以自动生成各类报告，提高工作效率。

## 常见问题解答

### Q1: 系统如何确保数据的安全性？
A1: 系统采用多层次的安全措施，包括用户认证、权限控制、数据加密和区块链存储等，确保数据的安全性和完整性。

### Q2: 系统如何支持不同类型的排放源计算？
A2: 系统内置了多种排放源的排放因子和计算方法，用户可以根据实际情况选择合适的计算方法。

### Q3: 系统的预测模型基于什么算法？
A3: 系统支持多种预测算法，包括线性回归、时间序列分析等，可以根据数据特点自动选择最合适的算法。

### Q4: 系统如何与区块链集成？
A4: 系统使用以太坊智能合约记录关键数据，通过Web3.js与区块链网络交互，确保数据的不可篡改性和透明性。

### Q5: 系统的扩展性如何？
A5: 系统采用模块化设计，可以方便地添加新功能和扩展现有功能，满足不同用户的需求。
