"""
仪表板相关路由
"""

from flask import Blueprint, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from sqlalchemy import func
from datetime import datetime, timedelta

from backend import db
from backend.models.user import User
from backend.models.emission import EmissionData
from backend.models.verification import Verification
from backend.models.transaction import Transaction
from backend.models.carbon_quota import CarbonQuota

dashboard_bp = Blueprint('dashboard', __name__)

@dashboard_bp.route('', methods=['GET'])
@jwt_required()
def get_dashboard():
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    if not user:
        return jsonify({'error': '用户不存在'}), 404
    
    # 根据用户角色返回不同的仪表板数据
    if user.role == 'admin':
        return get_admin_dashboard()
    elif user.role == 'enterprise':
        return get_enterprise_dashboard(user.id)
    elif user.role == 'verifier':
        return get_verifier_dashboard(user.id)
    else:
        return jsonify({'error': '无效的用户角色'}), 400

def get_admin_dashboard():
    # 用户统计
    total_users = User.query.count()
    enterprise_count = User.query.filter_by(role='enterprise').count()
    verifier_count = User.query.filter_by(role='verifier').count()
    
    # 排放数据统计
    total_emissions = EmissionData.query.count()
    pending_emissions = EmissionData.query.filter_by(status='pending').count()
    verified_emissions = EmissionData.query.filter_by(status='verified').count()
    rejected_emissions = EmissionData.query.filter_by(status='rejected').count()
    
    # 交易统计
    total_transactions = Transaction.query.count()
    completed_transactions = Transaction.query.filter_by(status='completed').count()
    pending_transactions = Transaction.query.filter_by(status='pending').count()
    
    # 最近一周的排放数据提交数量
    one_week_ago = datetime.now() - timedelta(days=7)
    recent_emissions = db.session.query(
        func.date(EmissionData.submission_time).label('date'),
        func.count(EmissionData.id).label('count')
    ).filter(EmissionData.submission_time >= one_week_ago).group_by('date').all()
    
    recent_emissions_data = {str(item.date): item.count for item in recent_emissions}
    
    return jsonify({
        'user_stats': {
            'total_users': total_users,
            'enterprise_count': enterprise_count,
            'verifier_count': verifier_count
        },
        'emission_stats': {
            'total_emissions': total_emissions,
            'pending_emissions': pending_emissions,
            'verified_emissions': verified_emissions,
            'rejected_emissions': rejected_emissions
        },
        'transaction_stats': {
            'total_transactions': total_transactions,
            'completed_transactions': completed_transactions,
            'pending_transactions': pending_transactions
        },
        'recent_emissions': recent_emissions_data
    }), 200

def get_enterprise_dashboard(enterprise_id):
    # 排放数据统计
    total_emissions = EmissionData.query.filter_by(enterprise_id=enterprise_id).count()
    pending_emissions = EmissionData.query.filter_by(enterprise_id=enterprise_id, status='pending').count()
    verified_emissions = EmissionData.query.filter_by(enterprise_id=enterprise_id, status='verified').count()
    rejected_emissions = EmissionData.query.filter_by(enterprise_id=enterprise_id, status='rejected').count()
    
    # 交易统计
    sales = Transaction.query.filter_by(seller_id=enterprise_id).count()
    purchases = Transaction.query.filter_by(buyer_id=enterprise_id).count()
    
    # 配额信息
    current_year = datetime.now().year
    quota = CarbonQuota.query.filter_by(enterprise_id=enterprise_id, year=current_year).first()
    
    # 最近的排放数据
    recent_emissions = EmissionData.query.filter_by(enterprise_id=enterprise_id).order_by(EmissionData.submission_time.desc()).limit(5).all()
    
    return jsonify({
        'emission_stats': {
            'total_emissions': total_emissions,
            'pending_emissions': pending_emissions,
            'verified_emissions': verified_emissions,
            'rejected_emissions': rejected_emissions
        },
        'transaction_stats': {
            'sales': sales,
            'purchases': purchases
        },
        'quota': quota.to_dict() if quota else None,
        'recent_emissions': [emission.to_dict() for emission in recent_emissions]
    }), 200

def get_verifier_dashboard(verifier_id):
    # 核查统计
    total_verifications = Verification.query.filter_by(verifier_id=verifier_id).count()
    approved_verifications = Verification.query.filter_by(verifier_id=verifier_id, conclusion='approved').count()
    rejected_verifications = Verification.query.filter_by(verifier_id=verifier_id, conclusion='rejected').count()
    
    # 待核查任务
    pending_tasks = EmissionData.query.filter_by(status='pending').count()
    
    # 最近的核查记录
    recent_verifications = Verification.query.filter_by(verifier_id=verifier_id).order_by(Verification.verification_time.desc()).limit(5).all()
    
    return jsonify({
        'verification_stats': {
            'total_verifications': total_verifications,
            'approved_verifications': approved_verifications,
            'rejected_verifications': rejected_verifications,
            'pending_tasks': pending_tasks
        },
        'recent_verifications': [verification.to_dict() for verification in recent_verifications]
    }), 200
