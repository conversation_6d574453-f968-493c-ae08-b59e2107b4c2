.system-settings {
  padding: 20px;
}

.system-settings h2 {
  margin: 0 0 20px 0;
  color: #333;
}

.settings-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.tab-btn {
  padding: 8px 16px;
  border: none;
  background: none;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
  border-radius: 5px;
}

.tab-btn:hover {
  background-color: #f3f4f6;
  color: #333;
}

.tab-btn.active {
  background-color: #667eea;
  color: white;
}

.settings-content {
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.settings-section {
  max-width: 800px;
}

.settings-section h3 {
  margin: 0 0 20px 0;
  color: #444;
  font-size: 18px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #555;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.form-control:focus {
  border-color: #667eea;
  outline: none;
}

textarea.form-control {
  resize: vertical;
  min-height: 100px;
}

.logo-upload {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.logo-preview {
  max-width: 200px;
  max-height: 100px;
  object-fit: contain;
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 5px;
}

.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.nested-settings {
  margin-left: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 5px;
  margin-top: 10px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 5px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn-primary {
  background-color: #667eea;
  color: white;
}

.btn-primary:hover {
  background-color: #5a6fd6;
}

.btn-secondary {
  background-color: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background-color: #4b5563;
}

.alert {
  padding: 12px;
  border-radius: 5px;
  margin-bottom: 20px;
}

.alert-danger {
  background-color: #fee2e2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

.alert-success {
  background-color: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: 18px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .system-settings {
    padding: 15px;
  }

  .settings-tabs {
    flex-direction: column;
    gap: 5px;
  }

  .tab-btn {
    width: 100%;
    text-align: left;
  }

  .settings-content {
    padding: 15px;
  }

  .checkbox-group {
    flex-direction: column;
    gap: 10px;
  }

  .nested-settings {
    margin-left: 10px;
    padding: 10px;
  }
} 