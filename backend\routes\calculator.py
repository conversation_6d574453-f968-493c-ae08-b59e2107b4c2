"""
碳计算器相关路由
"""

from datetime import datetime
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity

from backend import db
from backend.models.user import User
from backend.models.carbon_calculation import CarbonCalculation
from backend.models.activity import Activity

calculator_bp = Blueprint('calculator', __name__)

@calculator_bp.route('/calculate', methods=['POST'])
@jwt_required()
def calculate_carbon():
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    if not user:
        return jsonify({'error': '用户不存在'}), 404
    
    data = request.get_json()
    
    # 使用碳计算器计算排放量
    result = current_app.carbon_calculator.calculate_total_emission(data)
    
    # 保存计算结果
    calculation = CarbonCalculation(
        enterprise_id=current_user_id,
        calculation_time=datetime.now(),
        result_total=result['total_emission']
    )
    calculation.set_input_data(data)
    calculation.set_result_breakdown(result['breakdown'])
    
    db.session.add(calculation)
    db.session.commit()
    
    # 记录活动
    activity = Activity(
        user_id=current_user_id,
        activity_type='carbon_calculation',
        description=f'用户进行了碳排放计算，ID: {calculation.id}'
    )
    db.session.add(activity)
    db.session.commit()
    
    return jsonify({
        'message': '计算成功',
        'result': result,
        'calculation_id': calculation.id
    }), 200

@calculator_bp.route('/history', methods=['GET'])
@jwt_required()
def get_calculation_history():
    current_user_id = get_jwt_identity()
    
    # 获取当前用户的所有计算记录
    calculations = CarbonCalculation.query.filter_by(enterprise_id=current_user_id).order_by(CarbonCalculation.calculation_time.desc()).all()
    
    return jsonify({
        'calculations': [calculation.to_dict() for calculation in calculations]
    }), 200

@calculator_bp.route('/<int:calculation_id>', methods=['GET'])
@jwt_required()
def get_calculation(calculation_id):
    current_user_id = get_jwt_identity()
    
    calculation = CarbonCalculation.query.get(calculation_id)
    if not calculation:
        return jsonify({'error': '计算记录不存在'}), 404
    
    # 检查权限
    if calculation.enterprise_id != current_user_id:
        return jsonify({'error': '无权访问此计算记录'}), 403
    
    return jsonify({
        'calculation': calculation.to_dict()
    }), 200

@calculator_bp.route('/factors', methods=['GET'])
def get_emission_factors():
    # 返回所有排放因子
    return jsonify({
        'emission_factors': current_app.carbon_calculator.EMISSION_FACTORS
    }), 200
