/**
 * 企业排放管理页面脚本
 */

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 加载排放数据列表
    loadEmissionsList();
    
    // 加载排放统计信息
    loadEmissionStatistics();
    
    // 绑定搜索表单提交事件
    const searchForm = document.querySelector('form[action="/enterprise/emissions"]');
    if (searchForm) {
        searchForm.addEventListener('submit', function(event) {
            event.preventDefault();
            loadEmissionsList({
                source: document.getElementById('source').value,
                date_from: document.getElementById('date_from').value,
                date_to: document.getElementById('date_to').value,
                status: document.getElementById('status').value
            });
        });
    }
    
    // 绑定添加排放数据按钮点击事件
    const addEmissionBtn = document.querySelector('a[href="/enterprise/emissions/add"]');
    if (addEmissionBtn) {
        addEmissionBtn.addEventListener('click', function(event) {
            event.preventDefault();
            showAddEmissionModal();
        });
    }
});

/**
 * 加载排放数据列表
 * @param {object} params - 查询参数
 */
function loadEmissionsList(params = {}) {
    // 显示加载中提示
    const tableBody = document.querySelector('table tbody');
    tableBody.innerHTML = '<tr><td colspan="6" class="text-center">加载中...</td></tr>';
    
    // 调用API获取排放数据列表
    API.getEmissions(params)
        .then(response => {
            if (response.success) {
                renderEmissionsList(response.data);
            } else {
                showAlert('danger', response.message || '加载排放数据失败');
            }
        })
        .catch(error => {
            console.error('加载排放数据失败:', error);
            showAlert('danger', '加载排放数据失败: ' + error.message);
        });
}

/**
 * 渲染排放数据列表
 * @param {array} emissions - 排放数据列表
 */
function renderEmissionsList(emissions) {
    const tableBody = document.querySelector('table tbody');
    
    if (!emissions || emissions.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="6" class="text-center">暂无排放数据</td></tr>';
        return;
    }
    
    let html = '';
    emissions.forEach(emission => {
        html += `
            <tr>
                <td>${emission.id}</td>
                <td>${emission.source}</td>
                <td>${emission.amount} ${emission.unit}</td>
                <td>${emission.submission_date}</td>
                <td>
                    ${getStatusBadge(emission.status)}
                </td>
                <td>
                    <div class="btn-group">
                        <a href="/enterprise/emissions/${emission.id}" class="btn btn-sm btn-info view-emission" data-id="${emission.id}">
                            <i class="fas fa-eye"></i>
                        </a>
                        ${emission.status === '待提交' || emission.status === '已拒绝' ? `
                            <a href="/enterprise/emissions/${emission.id}/edit" class="btn btn-sm btn-primary edit-emission" data-id="${emission.id}">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="/enterprise/emissions/${emission.id}/submit" class="btn btn-sm btn-success submit-emission" data-id="${emission.id}">
                                <i class="fas fa-paper-plane"></i>
                            </a>
                            <button type="button" class="btn btn-sm btn-danger delete-emission" data-id="${emission.id}">
                                <i class="fas fa-trash"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `;
    });
    
    tableBody.innerHTML = html;
    
    // 绑定查看排放数据按钮点击事件
    document.querySelectorAll('.view-emission').forEach(btn => {
        btn.addEventListener('click', function(event) {
            event.preventDefault();
            const emissionId = this.getAttribute('data-id');
            viewEmissionDetail(emissionId);
        });
    });
    
    // 绑定编辑排放数据按钮点击事件
    document.querySelectorAll('.edit-emission').forEach(btn => {
        btn.addEventListener('click', function(event) {
            event.preventDefault();
            const emissionId = this.getAttribute('data-id');
            editEmission(emissionId);
        });
    });
    
    // 绑定提交排放数据按钮点击事件
    document.querySelectorAll('.submit-emission').forEach(btn => {
        btn.addEventListener('click', function(event) {
            event.preventDefault();
            const emissionId = this.getAttribute('data-id');
            submitEmission(emissionId);
        });
    });
    
    // 绑定删除排放数据按钮点击事件
    document.querySelectorAll('.delete-emission').forEach(btn => {
        btn.addEventListener('click', function() {
            const emissionId = this.getAttribute('data-id');
            showDeleteConfirmModal(emissionId);
        });
    });
}

/**
 * 获取状态徽章HTML
 * @param {string} status - 状态
 * @returns {string} - 徽章HTML
 */
function getStatusBadge(status) {
    switch (status) {
        case '待提交':
            return '<span class="badge bg-secondary">待提交</span>';
        case '已提交':
            return '<span class="badge bg-primary">已提交</span>';
        case '待核查':
            return '<span class="badge bg-warning">待核查</span>';
        case '已核查':
            return '<span class="badge bg-success">已核查</span>';
        case '已拒绝':
            return '<span class="badge bg-danger">已拒绝</span>';
        default:
            return '<span class="badge bg-secondary">' + status + '</span>';
    }
}

/**
 * 加载排放统计信息
 */
function loadEmissionStatistics() {
    API.getEmissionStatistics()
        .then(response => {
            if (response.success) {
                updateStatisticsCards(response.data);
            } else {
                console.error('加载排放统计信息失败:', response.message);
            }
        })
        .catch(error => {
            console.error('加载排放统计信息失败:', error);
        });
}

/**
 * 更新统计卡片
 * @param {object} statistics - 统计信息
 */
function updateStatisticsCards(statistics) {
    // 更新总排放量
    const totalEmissionCard = document.querySelector('.card.bg-primary .card-text');
    if (totalEmissionCard) {
        totalEmissionCard.textContent = `${statistics.total_emission} tCO2e`;
    }
    
    // 更新已核查排放量
    const verifiedEmissionCard = document.querySelector('.card.bg-success .card-text');
    if (verifiedEmissionCard) {
        verifiedEmissionCard.textContent = `${statistics.verified_emission} tCO2e`;
    }
    
    // 更新待核查排放量
    const pendingEmissionCard = document.querySelector('.card.bg-warning .card-text');
    if (pendingEmissionCard) {
        pendingEmissionCard.textContent = `${statistics.pending_emission} tCO2e`;
    }
}

/**
 * 查看排放数据详情
 * @param {number} emissionId - 排放数据ID
 */
function viewEmissionDetail(emissionId) {
    API.getEmissionDetail(emissionId)
        .then(response => {
            if (response.success) {
                showEmissionDetailModal(response.data);
            } else {
                showAlert('danger', response.message || '获取排放数据详情失败');
            }
        })
        .catch(error => {
            console.error('获取排放数据详情失败:', error);
            showAlert('danger', '获取排放数据详情失败: ' + error.message);
        });
}

/**
 * 显示排放数据详情模态框
 * @param {object} emission - 排放数据
 */
function showEmissionDetailModal(emission) {
    // 创建模态框
    const modalId = 'emissionDetailModal';
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = modalId;
    modal.tabIndex = '-1';
    modal.setAttribute('aria-labelledby', `${modalId}Label`);
    modal.setAttribute('aria-hidden', 'true');
    
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="${modalId}Label">排放数据详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p><strong>ID:</strong> ${emission.id}</p>
                            <p><strong>排放源:</strong> ${emission.source}</p>
                            <p><strong>排放量:</strong> ${emission.amount} ${emission.unit}</p>
                            <p><strong>提交日期:</strong> ${emission.submission_date}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>状态:</strong> ${getStatusBadge(emission.status)}</p>
                            <p><strong>区块链哈希:</strong> ${emission.blockchain_hash || '未提交到区块链'}</p>
                            <p><strong>描述:</strong> ${emission.description || '无'}</p>
                        </div>
                    </div>
                    ${emission.blockchain_data && Object.keys(emission.blockchain_data).length > 0 ? `
                        <div class="card mt-3">
                            <div class="card-header bg-light">
                                <h6 class="card-title mb-0">区块链数据</h6>
                            </div>
                            <div class="card-body">
                                <pre>${JSON.stringify(emission.blockchain_data, null, 2)}</pre>
                            </div>
                        </div>
                    ` : ''}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    `;
    
    // 添加到文档中
    document.body.appendChild(modal);
    
    // 显示模态框
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();
    
    // 模态框关闭后移除
    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

/**
 * 显示添加排放数据模态框
 */
function showAddEmissionModal() {
    // 创建模态框
    const modalId = 'addEmissionModal';
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = modalId;
    modal.tabIndex = '-1';
    modal.setAttribute('aria-labelledby', `${modalId}Label`);
    modal.setAttribute('aria-hidden', 'true');
    
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="${modalId}Label">添加排放数据</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addEmissionForm">
                        <div class="mb-3">
                            <label for="source" class="form-label">排放源</label>
                            <select class="form-select" id="source" name="source" required>
                                <option value="">选择排放源</option>
                                <option value="电力消耗">电力消耗</option>
                                <option value="燃料燃烧">燃料燃烧</option>
                                <option value="工业过程">工业过程</option>
                                <option value="废弃物处理">废弃物处理</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="amount" class="form-label">排放量</label>
                            <input type="number" class="form-control" id="amount" name="amount" step="0.01" min="0" required>
                        </div>
                        <div class="mb-3">
                            <label for="unit" class="form-label">单位</label>
                            <select class="form-select" id="unit" name="unit" required>
                                <option value="tCO2e">tCO2e</option>
                                <option value="kg">kg</option>
                                <option value="吨">吨</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">描述</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveEmissionBtn">保存</button>
                </div>
            </div>
        </div>
    `;
    
    // 添加到文档中
    document.body.appendChild(modal);
    
    // 显示模态框
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();
    
    // 绑定保存按钮点击事件
    document.getElementById('saveEmissionBtn').addEventListener('click', function() {
        const form = document.getElementById('addEmissionForm');
        if (form.checkValidity()) {
            const emissionData = {
                source: document.getElementById('source').value,
                amount: parseFloat(document.getElementById('amount').value),
                unit: document.getElementById('unit').value,
                description: document.getElementById('description').value
            };
            
            addEmission(emissionData, modalInstance);
        } else {
            form.reportValidity();
        }
    });
    
    // 模态框关闭后移除
    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

/**
 * 添加排放数据
 * @param {object} emissionData - 排放数据
 * @param {object} modalInstance - 模态框实例
 */
function addEmission(emissionData, modalInstance) {
    API.addEmission(emissionData)
        .then(response => {
            if (response.success) {
                modalInstance.hide();
                showAlert('success', '排放数据添加成功');
                loadEmissionsList();
                loadEmissionStatistics();
            } else {
                showAlert('danger', response.message || '添加排放数据失败', true);
            }
        })
        .catch(error => {
            console.error('添加排放数据失败:', error);
            showAlert('danger', '添加排放数据失败: ' + error.message, true);
        });
}

/**
 * 编辑排放数据
 * @param {number} emissionId - 排放数据ID
 */
function editEmission(emissionId) {
    API.getEmissionDetail(emissionId)
        .then(response => {
            if (response.success) {
                showEditEmissionModal(response.data);
            } else {
                showAlert('danger', response.message || '获取排放数据详情失败');
            }
        })
        .catch(error => {
            console.error('获取排放数据详情失败:', error);
            showAlert('danger', '获取排放数据详情失败: ' + error.message);
        });
}

/**
 * 显示编辑排放数据模态框
 * @param {object} emission - 排放数据
 */
function showEditEmissionModal(emission) {
    // 创建模态框
    const modalId = 'editEmissionModal';
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = modalId;
    modal.tabIndex = '-1';
    modal.setAttribute('aria-labelledby', `${modalId}Label`);
    modal.setAttribute('aria-hidden', 'true');
    
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="${modalId}Label">编辑排放数据</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editEmissionForm">
                        <div class="mb-3">
                            <label for="edit_source" class="form-label">排放源</label>
                            <select class="form-select" id="edit_source" name="source" required>
                                <option value="">选择排放源</option>
                                <option value="电力消耗" ${emission.source === '电力消耗' ? 'selected' : ''}>电力消耗</option>
                                <option value="燃料燃烧" ${emission.source === '燃料燃烧' ? 'selected' : ''}>燃料燃烧</option>
                                <option value="工业过程" ${emission.source === '工业过程' ? 'selected' : ''}>工业过程</option>
                                <option value="废弃物处理" ${emission.source === '废弃物处理' ? 'selected' : ''}>废弃物处理</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="edit_amount" class="form-label">排放量</label>
                            <input type="number" class="form-control" id="edit_amount" name="amount" step="0.01" min="0" value="${emission.amount}" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_unit" class="form-label">单位</label>
                            <select class="form-select" id="edit_unit" name="unit" required>
                                <option value="tCO2e" ${emission.unit === 'tCO2e' ? 'selected' : ''}>tCO2e</option>
                                <option value="kg" ${emission.unit === 'kg' ? 'selected' : ''}>kg</option>
                                <option value="吨" ${emission.unit === '吨' ? 'selected' : ''}>吨</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="edit_description" class="form-label">描述</label>
                            <textarea class="form-control" id="edit_description" name="description" rows="3">${emission.description || ''}</textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="updateEmissionBtn" data-id="${emission.id}">保存</button>
                </div>
            </div>
        </div>
    `;
    
    // 添加到文档中
    document.body.appendChild(modal);
    
    // 显示模态框
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();
    
    // 绑定保存按钮点击事件
    document.getElementById('updateEmissionBtn').addEventListener('click', function() {
        const form = document.getElementById('editEmissionForm');
        if (form.checkValidity()) {
            const emissionId = this.getAttribute('data-id');
            const emissionData = {
                source: document.getElementById('edit_source').value,
                amount: parseFloat(document.getElementById('edit_amount').value),
                unit: document.getElementById('edit_unit').value,
                description: document.getElementById('edit_description').value
            };
            
            updateEmission(emissionId, emissionData, modalInstance);
        } else {
            form.reportValidity();
        }
    });
    
    // 模态框关闭后移除
    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

/**
 * 更新排放数据
 * @param {number} emissionId - 排放数据ID
 * @param {object} emissionData - 排放数据
 * @param {object} modalInstance - 模态框实例
 */
function updateEmission(emissionId, emissionData, modalInstance) {
    API.updateEmission(emissionId, emissionData)
        .then(response => {
            if (response.success) {
                modalInstance.hide();
                showAlert('success', '排放数据更新成功');
                loadEmissionsList();
                loadEmissionStatistics();
            } else {
                showAlert('danger', response.message || '更新排放数据失败', true);
            }
        })
        .catch(error => {
            console.error('更新排放数据失败:', error);
            showAlert('danger', '更新排放数据失败: ' + error.message, true);
        });
}

/**
 * 提交排放数据到区块链
 * @param {number} emissionId - 排放数据ID
 */
function submitEmission(emissionId) {
    if (confirm('确定要提交此排放数据到区块链吗？提交后将无法修改。')) {
        API.submitEmission(emissionId)
            .then(response => {
                if (response.success) {
                    showAlert('success', '排放数据提交成功');
                    loadEmissionsList();
                } else {
                    showAlert('danger', response.message || '提交排放数据失败');
                }
            })
            .catch(error => {
                console.error('提交排放数据失败:', error);
                showAlert('danger', '提交排放数据失败: ' + error.message);
            });
    }
}

/**
 * 显示删除确认模态框
 * @param {number} emissionId - 排放数据ID
 */
function showDeleteConfirmModal(emissionId) {
    // 创建模态框
    const modalId = 'deleteConfirmModal';
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = modalId;
    modal.tabIndex = '-1';
    modal.setAttribute('aria-labelledby', `${modalId}Label`);
    modal.setAttribute('aria-hidden', 'true');
    
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="${modalId}Label">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>确定要删除这条排放数据吗？此操作不可逆。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn" data-id="${emissionId}">确认删除</button>
                </div>
            </div>
        </div>
    `;
    
    // 添加到文档中
    document.body.appendChild(modal);
    
    // 显示模态框
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();
    
    // 绑定确认删除按钮点击事件
    document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
        const emissionId = this.getAttribute('data-id');
        deleteEmission(emissionId, modalInstance);
    });
    
    // 模态框关闭后移除
    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

/**
 * 删除排放数据
 * @param {number} emissionId - 排放数据ID
 * @param {object} modalInstance - 模态框实例
 */
function deleteEmission(emissionId, modalInstance) {
    API.deleteEmission(emissionId)
        .then(response => {
            if (response.success) {
                modalInstance.hide();
                showAlert('success', '排放数据删除成功');
                loadEmissionsList();
                loadEmissionStatistics();
            } else {
                showAlert('danger', response.message || '删除排放数据失败', true);
            }
        })
        .catch(error => {
            console.error('删除排放数据失败:', error);
            showAlert('danger', '删除排放数据失败: ' + error.message, true);
        });
}

/**
 * 显示提示信息
 * @param {string} type - 提示类型
 * @param {string} message - 提示信息
 * @param {boolean} inModal - 是否在模态框中显示
 */
function showAlert(type, message, inModal = false) {
    const alertContainer = inModal ? 
        document.querySelector('.modal-body') : 
        document.querySelector('.container');
    
    if (!alertContainer) return;
    
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    if (inModal) {
        alertContainer.insertBefore(alertDiv, alertContainer.firstChild);
    } else {
        const pageTitle = document.querySelector('.d-flex.justify-content-between');
        alertContainer.insertBefore(alertDiv, pageTitle.nextSibling);
    }
    
    // 5秒后自动关闭
    setTimeout(() => {
        alertDiv.classList.remove('show');
        setTimeout(() => {
            alertContainer.removeChild(alertDiv);
        }, 150);
    }, 5000);
}
