import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';
import BlockchainInfo from '../../components/BlockchainInfo';
import BlockchainVerification from '../../components/BlockchainVerification';
import '../../styles/PenaltyDetail.css';

/**
 * 惩罚详情页面
 * 展示惩罚记录的详细信息，包括区块链信息
 */
const PenaltyDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [penalty, setPenalty] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [userRole, setUserRole] = useState('');

  useEffect(() => {
    fetchUserInfo();
    fetchPenaltyData();
  }, [id]);

  const fetchUserInfo = async () => {
    try {
      const response = await axios.get('/api/auth/profile', {
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      
      setUserRole(response.data.role);
    } catch (err) {
      console.error('获取用户信息失败:', err);
    }
  };

  const fetchPenaltyData = async () => {
    setLoading(true);
    try {
      const response = await axios.get(`/api/penalties/${id}`, {
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      
      setPenalty(response.data.penalty);
      setError('');
    } catch (err) {
      console.error('获取惩罚记录失败:', err);
      setError('获取惩罚记录失败');
    } finally {
      setLoading(false);
    }
  };

  // 格式化日期
  const formatDate = (dateString) => {
    if (!dateString) return '';
    
    try {
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch (e) {
      return dateString;
    }
  };

  // 获取状态标签和颜色
  const getStatusLabel = (status) => {
    switch (status) {
      case 'pending':
        return { label: '待处理', color: 'orange' };
      case 'paid':
        return { label: '已缴纳', color: 'green' };
      case 'disputed':
        return { label: '有异议', color: 'red' };
      default:
        return { label: status, color: 'gray' };
    }
  };

  // 更新惩罚状态
  const updatePenaltyStatus = async (status) => {
    try {
      await axios.patch(`/api/penalties/${id}`, { status }, {
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      
      fetchPenaltyData();
    } catch (err) {
      console.error('更新惩罚状态失败:', err);
      setError('更新惩罚状态失败');
    }
  };

  if (loading) {
    return (
      <div className="penalty-detail-loading">
        <i className="fas fa-spinner fa-spin"></i>
        <span>加载中...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="penalty-detail-error">
        <i className="fas fa-exclamation-triangle"></i>
        <span>{error}</span>
        <button onClick={() => navigate('/penalties')} className="btn-back">
          返回列表
        </button>
      </div>
    );
  }

  if (!penalty) {
    return (
      <div className="penalty-detail-not-found">
        <i className="fas fa-search"></i>
        <span>未找到惩罚记录</span>
        <button onClick={() => navigate('/penalties')} className="btn-back">
          返回列表
        </button>
      </div>
    );
  }

  const statusInfo = getStatusLabel(penalty.status);

  return (
    <div className="penalty-detail">
      <div className="detail-header">
        <h2>惩罚详情</h2>
        <button onClick={() => navigate('/penalties')} className="btn-back">
          <i className="fas fa-arrow-left"></i> 返回列表
        </button>
      </div>

      {/* 基本信息 */}
      <div className="detail-section">
        <h3 className="section-title">基本信息</h3>
        <div className="detail-grid">
          <div className="detail-item">
            <span className="detail-label">惩罚ID</span>
            <span className="detail-value">{penalty.id}</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">状态</span>
            <span className="detail-value">
              <span className="status-badge" style={{ backgroundColor: statusInfo.color }}>
                {statusInfo.label}
              </span>
            </span>
          </div>
          <div className="detail-item">
            <span className="detail-label">企业</span>
            <span className="detail-value">{penalty.enterprise_name}</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">惩罚金额</span>
            <span className="detail-value">¥{penalty.amount.toFixed(2)}</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">惩罚时间</span>
            <span className="detail-value">{formatDate(penalty.penalty_time)}</span>
          </div>
          <div className="detail-item detail-item-full">
            <span className="detail-label">惩罚原因</span>
            <span className="detail-value detail-reason">{penalty.reason}</span>
          </div>
        </div>
        
        {/* 管理员操作按钮 */}
        {userRole === 'admin' && (
          <div className="penalty-actions">
            <button 
              onClick={() => updatePenaltyStatus('paid')} 
              className="btn-paid"
              disabled={penalty.status === 'paid'}
            >
              标记为已缴纳
            </button>
            <button 
              onClick={() => updatePenaltyStatus('disputed')} 
              className="btn-disputed"
              disabled={penalty.status === 'disputed'}
            >
              标记为有异议
            </button>
            <button 
              onClick={() => updatePenaltyStatus('pending')} 
              className="btn-pending"
              disabled={penalty.status === 'pending'}
            >
              标记为待处理
            </button>
          </div>
        )}
      </div>

      {/* 区块链信息 */}
      {penalty.blockchain_hash && (
        <div className="detail-section">
          <h3 className="section-title">区块链信息</h3>
          <BlockchainInfo
            transactionHash={penalty.blockchain_hash}
            blockNumber={penalty.blockchain_block}
            timestamp={penalty.penalty_time}
            status="confirmed"
            type="penalty"
          />
          <BlockchainVerification
            dataId={penalty.id}
            dataType="penalty"
          />
        </div>
      )}
      
      {/* 区块链数据 */}
      {penalty.blockchain_data && (
        <div className="detail-section">
          <h3 className="section-title">区块链数据</h3>
          <div className="blockchain-data">
            <pre>{JSON.stringify(penalty.blockchain_data, null, 2)}</pre>
          </div>
        </div>
      )}
    </div>
  );
};

export default PenaltyDetail;
