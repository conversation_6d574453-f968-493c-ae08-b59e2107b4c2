import React, { useState, useEffect } from 'react';
import axios from 'axios';
import '../styles/EmissionData.css';

function EmissionData() {
  const [emissions, setEmissions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [formData, setFormData] = useState({
    date: '',
    type: '',
    amount: '',
    unit: 'tCO2e',
    description: ''
  });

  useEffect(() => {
    fetchEmissions();
  }, []);

  const fetchEmissions = async () => {
    try {
      const response = await axios.get('/api/emissions', {
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      setEmissions(response.data);
      setLoading(false);
    } catch (err) {
      setError('获取排放数据失败');
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await axios.post('/api/emissions', formData, {
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      fetchEmissions();
      setFormData({
        date: '',
        type: '',
        amount: '',
        unit: 'tCO2e',
        description: ''
      });
    } catch (err) {
      setError('提交排放数据失败');
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  if (loading) return <div className="loading">加载中...</div>;

  return (
    <div className="emission-data">
      <h2>排放数据管理</h2>
      {error && <div className="alert alert-danger">{error}</div>}
      
      <div className="data-form">
        <h3>添加新排放数据</h3>
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="date">日期</label>
            <input
              type="date"
              id="date"
              name="date"
              value={formData.date}
              onChange={handleChange}
              required
              className="form-control"
            />
          </div>
          <div className="form-group">
            <label htmlFor="type">排放类型</label>
            <select
              id="type"
              name="type"
              value={formData.type}
              onChange={handleChange}
              required
              className="form-control"
            >
              <option value="">请选择类型</option>
              <option value="scope1">范围一排放</option>
              <option value="scope2">范围二排放</option>
              <option value="scope3">范围三排放</option>
            </select>
          </div>
          <div className="form-group">
            <label htmlFor="amount">排放量</label>
            <input
              type="number"
              id="amount"
              name="amount"
              value={formData.amount}
              onChange={handleChange}
              required
              className="form-control"
              step="0.01"
            />
          </div>
          <div className="form-group">
            <label htmlFor="unit">单位</label>
            <select
              id="unit"
              name="unit"
              value={formData.unit}
              onChange={handleChange}
              required
              className="form-control"
            >
              <option value="tCO2e">吨二氧化碳当量</option>
              <option value="kgCO2e">千克二氧化碳当量</option>
            </select>
          </div>
          <div className="form-group">
            <label htmlFor="description">描述</label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              className="form-control"
              rows="3"
            />
          </div>
          <button type="submit" className="btn btn-primary">
            提交数据
          </button>
        </form>
      </div>

      <div className="data-list">
        <h3>历史排放数据</h3>
        <div className="table-responsive">
          <table className="table">
            <thead>
              <tr>
                <th>日期</th>
                <th>类型</th>
                <th>排放量</th>
                <th>单位</th>
                <th>描述</th>
                <th>状态</th>
              </tr>
            </thead>
            <tbody>
              {emissions.map(emission => (
                <tr key={emission.id}>
                  <td>{new Date(emission.date).toLocaleDateString()}</td>
                  <td>
                    {emission.type === 'scope1' ? '范围一排放' :
                     emission.type === 'scope2' ? '范围二排放' :
                     '范围三排放'}
                  </td>
                  <td>{emission.amount}</td>
                  <td>{emission.unit}</td>
                  <td>{emission.description}</td>
                  <td>
                    <span className={`status ${emission.status}`}>
                      {emission.status === 'pending' ? '待审核' :
                       emission.status === 'approved' ? '已通过' :
                       '已拒绝'}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default EmissionData; 