/**
 * 碳排放核查系统演示导航栏统一样式
 * 绿色环保主题
 */

/* 全局变量 */
:root {
    --primary-color: #43a047;
    --primary-light: #76d275;
    --primary-dark: #00701a;
    --secondary-color: #1de9b6;
    --secondary-light: #6effe8;
    --secondary-dark: #00b686;
    --text-on-primary: #ffffff;
    --text-on-secondary: #000000;
    --background-color: #f5f5f5;
    --card-background: #ffffff;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --info-color: #2196f3;
}

/* 头部样式 */
.demo-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 15px 0;
    box-shadow: 0 4px 10px rgba(0,0,0,0.2);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.logo {
    font-size: 24px;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
    letter-spacing: 0.5px;
}

.user-info {
    display: flex;
    align-items: center;
}

.user-name {
    margin-right: 15px;
    font-weight: 500;
    font-size: 16px;
    letter-spacing: 0.3px;
}

.logout-btn {
    background: linear-gradient(to right, #e74c3c, #c0392b);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 30px;
    cursor: pointer;
    text-decoration: none;
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    font-weight: 500;
    letter-spacing: 0.3px;
}

.logout-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 7px 15px rgba(0,0,0,0.2);
    color: white;
}

/* 导航栏样式 */
.demo-nav {
    background: linear-gradient(to right, #2E7D32, #43A047);
    padding: 0;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    overflow-x: auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.nav-content::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.nav-item {
    color: white;
    text-decoration: none;
    padding: 15px 20px;
    margin-right: 5px;
    border-radius: 0;
    transition: all 0.3s ease;
    white-space: nowrap;
    font-weight: 500;
    font-size: 16px;
    letter-spacing: 0.5px;
    position: relative;
    display: flex;
    align-items: center;
}

.nav-item i {
    margin-right: 8px;
    font-size: 18px;
}

.nav-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

.nav-item.active {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    font-weight: 600;
}

.nav-item.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 20%;
    right: 20%;
    height: 3px;
    background-color: white;
    border-radius: 3px 3px 0 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .nav-item {
        padding: 12px 15px;
        font-size: 14px;
    }
    
    .nav-item i {
        margin-right: 5px;
        font-size: 16px;
    }
    
    .logo {
        font-size: 20px;
    }
    
    .user-name {
        font-size: 14px;
    }
    
    .logout-btn {
        padding: 6px 12px;
        font-size: 14px;
    }
}
