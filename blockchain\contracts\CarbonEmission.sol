// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract CarbonEmission {
    // 结构体定义
    struct EmissionData {
        uint256 id;
        address enterprise;
        string emissionSource;
        uint256 emissionAmount;
        string calculationMethod;
        uint256 submissionTime;
        string status;
        string proofFileHash;
    }

    struct VerificationRecord {
        uint256 id;
        address verifier;
        address enterprise;
        uint256 emissionDataId;
        string conclusion;
        string comments;
        uint256 verificationTime;
    }

    struct Transaction {
        uint256 id;
        address buyer;
        address seller;
        uint256 amount;
        uint256 price;
        uint256 transactionTime;
        string status;
    }

    struct Penalty {
        uint256 id;
        address enterprise;
        uint256 amount;
        string reason;
        uint256 penaltyTime;
        string status;
    }

    // 状态变量
    mapping(uint256 => EmissionData) public emissionData;
    mapping(uint256 => VerificationRecord) public verificationRecords;
    mapping(uint256 => Transaction) public transactions;
    mapping(uint256 => Penalty) public penalties;
    mapping(address => bool) public isEnterprise;
    mapping(address => bool) public isVerifier;
    mapping(address => bool) public isAdmin;

    uint256 public emissionDataCount;
    uint256 public verificationRecordCount;
    uint256 public transactionCount;
    uint256 public penaltyCount;

    // 事件定义
    event EmissionDataSubmitted(uint256 indexed id, address indexed enterprise);
    event VerificationRecordCreated(uint256 indexed id, address indexed verifier, address indexed enterprise);
    event TransactionCreated(uint256 indexed id, address indexed buyer, address indexed seller);
    event TransactionConfirmed(uint256 indexed id, address indexed seller);
    event TransactionCancelled(uint256 indexed id);
    event PenaltyCreated(uint256 indexed id, address indexed enterprise);

    // 修饰器
    modifier onlyEnterprise() {
        require(isEnterprise[msg.sender], "Only enterprises can call this function");
        _;
    }

    modifier onlyVerifier() {
        require(isVerifier[msg.sender], "Only verifiers can call this function");
        _;
    }

    modifier onlyAdmin() {
        require(isAdmin[msg.sender], "Only admins can call this function");
        _;
    }

    // 构造函数
    constructor() {
        isAdmin[msg.sender] = true;
    }

    // 企业注册
    function registerEnterprise(address enterprise) public onlyAdmin {
        isEnterprise[enterprise] = true;
    }

    // 核查机构注册
    function registerVerifier(address verifier) public onlyAdmin {
        isVerifier[verifier] = true;
    }

    // 提交碳排放数据
    function submitEmissionData(
        string memory emissionSource,
        uint256 emissionAmount,
        string memory calculationMethod,
        string memory proofFileHash
    ) public onlyEnterprise {
        emissionDataCount++;
        emissionData[emissionDataCount] = EmissionData({
            id: emissionDataCount,
            enterprise: msg.sender,
            emissionSource: emissionSource,
            emissionAmount: emissionAmount,
            calculationMethod: calculationMethod,
            submissionTime: block.timestamp,
            status: "pending",
            proofFileHash: proofFileHash
        });

        emit EmissionDataSubmitted(emissionDataCount, msg.sender);
    }

    // 提交核查结果
    function submitVerification(
        uint256 emissionDataId,
        string memory conclusion,
        string memory comments
    ) public onlyVerifier {
        require(emissionData[emissionDataId].id != 0, "Emission data does not exist");
        require(keccak256(bytes(emissionData[emissionDataId].status)) == keccak256(bytes("pending")), "Emission data is not pending");

        verificationRecordCount++;
        verificationRecords[verificationRecordCount] = VerificationRecord({
            id: verificationRecordCount,
            verifier: msg.sender,
            enterprise: emissionData[emissionDataId].enterprise,
            emissionDataId: emissionDataId,
            conclusion: conclusion,
            comments: comments,
            verificationTime: block.timestamp
        });

        emissionData[emissionDataId].status = conclusion;

        emit VerificationRecordCreated(verificationRecordCount, msg.sender, emissionData[emissionDataId].enterprise);
    }

    // 创建交易
    function createTransaction(
        address seller,
        uint256 amount,
        uint256 price
    ) public onlyEnterprise {
        require(isEnterprise[seller], "Seller must be an enterprise");
        require(seller != msg.sender, "Cannot trade with yourself");

        transactionCount++;
        transactions[transactionCount] = Transaction({
            id: transactionCount,
            buyer: msg.sender,
            seller: seller,
            amount: amount,
            price: price,
            transactionTime: block.timestamp,
            status: "pending"
        });

        emit TransactionCreated(transactionCount, msg.sender, seller);
    }

    // 确认交易
    function confirmTransaction(uint256 transactionId) public {
        Transaction storage transaction = transactions[transactionId];

        // 验证交易存在
        require(transaction.id != 0, "Transaction does not exist");

        // 验证调用者是卖方
        require(transaction.seller == msg.sender, "Only seller can confirm the transaction");

        // 验证交易状态为待处理
        require(keccak256(bytes(transaction.status)) == keccak256(bytes("pending")), "Transaction is not pending");

        // 更新交易状态
        transaction.status = "completed";

        // 触发交易确认事件
        emit TransactionConfirmed(transactionId, msg.sender);
    }

    // 取消交易
    function cancelTransaction(uint256 transactionId) public {
        Transaction storage transaction = transactions[transactionId];

        // 验证交易存在
        require(transaction.id != 0, "Transaction does not exist");

        // 验证调用者是买方或卖方
        require(transaction.buyer == msg.sender || transaction.seller == msg.sender, "Only buyer or seller can cancel the transaction");

        // 验证交易状态为待处理
        require(keccak256(bytes(transaction.status)) == keccak256(bytes("pending")), "Transaction is not pending");

        // 更新交易状态
        transaction.status = "cancelled";

        // 触发交易取消事件
        emit TransactionCancelled(transactionId);
    }

    // 创建惩罚记录
    function createPenalty(
        address enterprise,
        uint256 amount,
        string memory reason
    ) public onlyAdmin {
        require(isEnterprise[enterprise], "Target must be an enterprise");

        penaltyCount++;
        penalties[penaltyCount] = Penalty({
            id: penaltyCount,
            enterprise: enterprise,
            amount: amount,
            reason: reason,
            penaltyTime: block.timestamp,
            status: "pending"
        });

        emit PenaltyCreated(penaltyCount, enterprise);
    }

    // 查询函数
    function getEmissionData(uint256 id) public view returns (
        uint256,
        address,
        string memory,
        uint256,
        string memory,
        uint256,
        string memory,
        string memory
    ) {
        EmissionData memory data = emissionData[id];
        return (
            data.id,
            data.enterprise,
            data.emissionSource,
            data.emissionAmount,
            data.calculationMethod,
            data.submissionTime,
            data.status,
            data.proofFileHash
        );
    }

    function getVerificationRecord(uint256 id) public view returns (
        uint256,
        address,
        address,
        uint256,
        string memory,
        string memory,
        uint256
    ) {
        VerificationRecord memory record = verificationRecords[id];
        return (
            record.id,
            record.verifier,
            record.enterprise,
            record.emissionDataId,
            record.conclusion,
            record.comments,
            record.verificationTime
        );
    }

    function getTransaction(uint256 id) public view returns (
        uint256,
        address,
        address,
        uint256,
        uint256,
        uint256,
        string memory
    ) {
        Transaction memory transaction = transactions[id];
        return (
            transaction.id,
            transaction.buyer,
            transaction.seller,
            transaction.amount,
            transaction.price,
            transaction.transactionTime,
            transaction.status
        );
    }

    function getPenalty(uint256 id) public view returns (
        uint256,
        address,
        uint256,
        string memory,
        uint256,
        string memory
    ) {
        Penalty memory penalty = penalties[id];
        return (
            penalty.id,
            penalty.enterprise,
            penalty.amount,
            penalty.reason,
            penalty.penaltyTime,
            penalty.status
        );
    }

    // 获取企业的所有惩罚记录ID
    function getEnterpriseAllPenalties(address enterprise) public view returns (uint256[] memory) {
        // 计算企业的惩罚记录数量
        uint256 count = 0;
        for (uint256 i = 1; i <= penaltyCount; i++) {
            if (penalties[i].enterprise == enterprise) {
                count++;
            }
        }

        // 创建结果数组
        uint256[] memory result = new uint256[](count);
        uint256 index = 0;

        // 填充结果数组
        for (uint256 i = 1; i <= penaltyCount; i++) {
            if (penalties[i].enterprise == enterprise) {
                result[index] = i;
                index++;
            }
        }

        return result;
    }
}