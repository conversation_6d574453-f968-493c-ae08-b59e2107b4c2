import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider, useAuth } from './contexts/AuthContext';

// 页面组件
import Login from './pages/Login';
import Register from './pages/Register';
import Dashboard from './pages/Dashboard';
import EmissionData from './pages/EmissionData';
import VerificationTasks from './pages/VerificationTasks';
import AdminDashboard from './pages/admin/Dashboard';
import UserManagement from './pages/admin/UserManagement';
import Profile from './pages/Profile';
import NotFound from './pages/NotFound';

// 新功能组件
import CarbonCalculator from './components/CarbonCalculator';
import CarbonTrading from './components/CarbonTrading';
import EmissionPrediction from './components/EmissionPrediction';
import ReportGenerator from './components/ReportGenerator';

// 布局组件
import Layout from './components/Layout';

// 受保护的路由
const ProtectedRoute = ({ children, requiredRole }) => {
  const { currentUser, isAdmin, isEnterprise, isVerifier } = useAuth();

  if (!currentUser) {
    return <Navigate to="/login" />;
  }

  if (requiredRole) {
    const hasRole =
      (requiredRole === 'admin' && isAdmin) ||
      (requiredRole === 'enterprise' && isEnterprise) ||
      (requiredRole === 'verifier' && isVerifier);

    if (!hasRole) {
      return <Navigate to="/dashboard" />;
    }
  }

  return children;
};

function App() {
  return (
    <Router>
      <AuthProvider>
        <Routes>
          {/* 公共路由 */}
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />

          {/* 受保护的路由 */}
          <Route path="/" element={
            <ProtectedRoute>
              <Layout />
            </ProtectedRoute>
          }>
            <Route index element={<Navigate to="/dashboard" />} />
            <Route path="dashboard" element={<Dashboard />} />
            <Route path="profile" element={<Profile />} />

            {/* 企业用户路由 */}
            <Route path="emissions" element={
              <ProtectedRoute requiredRole="enterprise">
                <EmissionData />
              </ProtectedRoute>
            } />

            {/* 核查机构路由 */}
            <Route path="verifications" element={
              <ProtectedRoute requiredRole="verifier">
                <VerificationTasks />
              </ProtectedRoute>
            } />

            {/* 新功能路由 */}
            <Route path="calculator" element={<CarbonCalculator />} />

            <Route path="trading" element={<CarbonTrading />} />

            <Route path="prediction" element={<EmissionPrediction />} />

            <Route path="reports" element={<ReportGenerator />} />

            {/* 管理员路由 */}
            <Route path="admin">
              <Route index element={
                <ProtectedRoute requiredRole="admin">
                  <AdminDashboard />
                </ProtectedRoute>
              } />
              <Route path="users" element={
                <ProtectedRoute requiredRole="admin">
                  <UserManagement />
                </ProtectedRoute>
              } />
            </Route>
          </Route>

          {/* 404 页面 */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </AuthProvider>
    </Router>
  );
}

export default App;
