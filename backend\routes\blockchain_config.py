"""
区块链配置路由
用于管理区块链配置和部署智能合约
"""

import os
import json
import subprocess
from flask import Blueprint, render_template, request, redirect, url_for, flash, current_app, jsonify
from web3 import Web3
from web3.middleware import geth_poa_middleware
from dotenv import load_dotenv, set_key
from flask_jwt_extended import jwt_required, get_jwt_identity
from backend.utils.auth import admin_required
from backend.models.user import User

# 创建蓝图
blockchain_config_bp = Blueprint('blockchain_config', __name__)

def load_env_config():
    """加载环境变量配置"""
    load_dotenv()

    config = {
        'ETHEREUM_NODE_URL': os.getenv('ETHEREUM_NODE_URL', 'http://127.0.0.1:8545'),
        'CONTRACT_ADDRESS': os.getenv('CONTRACT_ADDRESS', ''),
        'ADMIN_ADDRESS': os.getenv('ADMIN_ADDRESS', ''),
        'ADMIN_PRIVATE_KEY': os.getenv('ADMIN_PRIVATE_KEY', ''),
        'ENTERPRISE_1_ADDRESS': os.getenv('ENTERPRISE_1_ADDRESS', ''),
        'ENTERPRISE_1_KEY': os.getenv('ENTERPRISE_1_KEY', ''),
        'ENTERPRISE_2_ADDRESS': os.getenv('ENTERPRISE_2_ADDRESS', ''),
        'ENTERPRISE_2_KEY': os.getenv('ENTERPRISE_2_KEY', ''),
        'ENTERPRISE_3_ADDRESS': os.getenv('ENTERPRISE_3_ADDRESS', ''),
        'ENTERPRISE_3_KEY': os.getenv('ENTERPRISE_3_KEY', ''),
        'VERIFIER_1_ADDRESS': os.getenv('VERIFIER_1_ADDRESS', ''),
        'VERIFIER_1_KEY': os.getenv('VERIFIER_1_KEY', ''),
        'VERIFIER_2_ADDRESS': os.getenv('VERIFIER_2_ADDRESS', ''),
        'VERIFIER_2_KEY': os.getenv('VERIFIER_2_KEY', '')
    }

    return config

def save_env_config(config):
    """保存环境变量配置"""
    try:
        env_path = '.env'

        # 确保.env文件存在
        if not os.path.exists(env_path):
            with open(env_path, 'w') as f:
                f.write('# 区块链配置\n')

        # 验证配置
        required_fields = ['ETHEREUM_NODE_URL', 'ADMIN_ADDRESS', 'ADMIN_PRIVATE_KEY']
        for field in required_fields:
            if not config.get(field):
                return False, f"缺少必填字段: {field}"

        # 更新环境变量
        for key, value in config.items():
            if value:  # 只保存非空值
                set_key(env_path, key, value)

        # 重新加载环境变量
        load_dotenv()

        return True, "配置保存成功"
    except Exception as e:
        return False, f"保存配置失败: {str(e)}"

def get_ganache_accounts(node_url):
    """获取Ganache账户信息"""
    try:
        # 验证节点URL
        if not node_url:
            return None, "节点URL不能为空"

        # 连接到Ganache
        w3 = Web3(Web3.HTTPProvider(node_url))

        # 如果使用的是PoA共识的网络，需要添加这个中间件
        w3.middleware_onion.inject(geth_poa_middleware, layer=0)

        # 检查连接
        if not w3.is_connected():
            return None, "无法连接到Ganache节点，请确保Ganache已启动并运行在指定地址"

        # 获取账户列表
        accounts = w3.eth.accounts

        # 检查账户数量
        if len(accounts) < 6:
            return None, f"Ganache账户数量不足，需要至少6个账户，当前只有{len(accounts)}个"

        # 获取账户余额
        account_info = []
        for account in accounts:
            balance = w3.eth.get_balance(account)
            balance_eth = w3.from_wei(balance, 'ether')
            account_info.append({
                'address': account,
                'balance': balance_eth
            })

        return account_info, None
    except ConnectionError:
        return None, "连接Ganache节点失败，请确保Ganache已启动"
    except Exception as e:
        return None, f"获取Ganache账户失败: {str(e)}"

def deploy_smart_contract():
    """部署智能合约"""
    try:
        # 加载环境变量
        config = load_env_config()

        # 验证必要的配置
        if not config.get('ETHEREUM_NODE_URL'):
            return False, "缺少以太坊节点URL配置"
        if not config.get('ADMIN_ADDRESS'):
            return False, "缺少管理员地址配置"
        if not config.get('ADMIN_PRIVATE_KEY'):
            return False, "缺少管理员私钥配置"

        # 检查Ganache连接
        node_url = config.get('ETHEREUM_NODE_URL')
        accounts, error = get_ganache_accounts(node_url)
        if error:
            return False, f"无法连接到Ganache: {error}"

        # 运行部署脚本
        result = subprocess.run(['python', 'blockchain/deploy_contract.py'],
                               capture_output=True, text=True, check=True)

        # 重新加载环境变量
        load_dotenv()

        # 验证合约地址
        if not os.getenv('CONTRACT_ADDRESS'):
            return False, "部署成功，但未能获取合约地址"

        return True, "智能合约部署成功，合约地址: " + os.getenv('CONTRACT_ADDRESS')
    except subprocess.CalledProcessError as e:
        return False, f"部署失败: {e.stderr}"
    except FileNotFoundError:
        return False, "部署脚本不存在，请确保 blockchain/deploy_contract.py 文件存在"
    except PermissionError:
        return False, "权限不足，无法执行部署脚本"
    except Exception as e:
        return False, f"部署失败: {str(e)}"

@blockchain_config_bp.route('/admin/blockchain/config', methods=['GET', 'POST'])
@admin_required
def blockchain_config():
    """区块链配置页面"""
    success_message = None
    error_message = None
    ganache_accounts = None

    # 加载当前配置
    config = load_env_config()

    if request.method == 'POST':
        try:
            # 获取表单数据
            new_config = {
                'ETHEREUM_NODE_URL': request.form.get('ethereum_node_url'),
                'CONTRACT_ADDRESS': request.form.get('contract_address'),
                'ADMIN_ADDRESS': request.form.get('admin_address'),
                'ADMIN_PRIVATE_KEY': request.form.get('admin_private_key'),
                'ENTERPRISE_1_ADDRESS': request.form.get('enterprise_1_address'),
                'ENTERPRISE_1_KEY': request.form.get('enterprise_1_key'),
                'ENTERPRISE_2_ADDRESS': request.form.get('enterprise_2_address'),
                'ENTERPRISE_2_KEY': request.form.get('enterprise_2_key'),
                'ENTERPRISE_3_ADDRESS': request.form.get('enterprise_3_address'),
                'ENTERPRISE_3_KEY': request.form.get('enterprise_3_key'),
                'VERIFIER_1_ADDRESS': request.form.get('verifier_1_address'),
                'VERIFIER_1_KEY': request.form.get('verifier_1_key'),
                'VERIFIER_2_ADDRESS': request.form.get('verifier_2_address'),
                'VERIFIER_2_KEY': request.form.get('verifier_2_key')
            }

            # 保存配置
            success, message = save_env_config(new_config)
            if success:
                success_message = message
                # 更新当前配置
                config = load_env_config()  # 重新加载配置
            else:
                error_message = message
        except Exception as e:
            error_message = f"处理表单数据时出错: {str(e)}"

    return render_template('blockchain_config.html',
                          config=config,
                          success_message=success_message,
                          error_message=error_message,
                          ganache_accounts=ganache_accounts)

@blockchain_config_bp.route('/admin/blockchain/fetch-accounts')
@admin_required
def fetch_ganache_accounts():
    """获取Ganache账户信息"""
    success_message = None
    error_message = None

    try:
        # 加载当前配置
        config = load_env_config()

        # 验证节点URL
        node_url = config.get('ETHEREUM_NODE_URL')
        if not node_url:
            flash("请先配置以太坊节点URL", 'warning')
            return redirect(url_for('blockchain_config.blockchain_config'))

        # 获取Ganache账户
        ganache_accounts, error = get_ganache_accounts(node_url)

        if error:
            flash(f"获取Ganache账户失败: {error}", 'danger')
        elif ganache_accounts:
            flash(f"成功获取到 {len(ganache_accounts)} 个Ganache账户", 'success')
        else:
            flash("未获取到任何Ganache账户", 'warning')

        return render_template('blockchain_config.html',
                              config=config,
                              ganache_accounts=ganache_accounts,
                              success_message=success_message,
                              error_message=error_message)
    except Exception as e:
        flash(f"获取Ganache账户时发生错误: {str(e)}", 'danger')
        return redirect(url_for('blockchain_config.blockchain_config'))

@blockchain_config_bp.route('/admin/blockchain/deploy-contract')
@admin_required
def deploy_contract():
    """部署智能合约"""
    try:
        # 加载当前配置
        config = load_env_config()

        # 验证必要的配置
        if not config.get('ETHEREUM_NODE_URL'):
            flash("请先配置以太坊节点URL", 'warning')
            return redirect(url_for('blockchain_config.blockchain_config'))

        if not config.get('ADMIN_ADDRESS') or not config.get('ADMIN_PRIVATE_KEY'):
            flash("请先配置管理员地址和私钥", 'warning')
            return redirect(url_for('blockchain_config.blockchain_config'))

        # 部署合约
        success, message = deploy_smart_contract()

        if success:
            flash(message, 'success')
            # 重新加载配置以获取新的合约地址
            config = load_env_config()
            return render_template('blockchain_config.html',
                                  config=config,
                                  success_message="智能合约部署成功，请查看合约地址")
        else:
            flash(f"智能合约部署失败: {message}", 'danger')
            return render_template('blockchain_config.html',
                                  config=config,
                                  error_message=f"智能合约部署失败: {message}")
    except Exception as e:
        flash(f"部署智能合约时发生错误: {str(e)}", 'danger')
        return redirect(url_for('blockchain_config.blockchain_config'))

# API端点，用于前端获取区块链配置
@blockchain_config_bp.route('/api/blockchain/config', methods=['GET'])
@jwt_required()
def get_blockchain_config():
    """获取区块链配置"""
    # 获取用户身份
    user_id = get_jwt_identity()

    # 从数据库获取用户信息
    user = User.query.get(user_id)

    # 检查用户是否是管理员
    if not user or user.role != 'admin':
        return jsonify({"error": "需要管理员权限"}), 403

    # 加载当前配置
    config = load_env_config()

    # 移除敏感信息
    safe_config = {
        'ETHEREUM_NODE_URL': config.get('ETHEREUM_NODE_URL', ''),
        'CONTRACT_ADDRESS': config.get('CONTRACT_ADDRESS', ''),
        'ADMIN_ADDRESS': config.get('ADMIN_ADDRESS', ''),
        'ENTERPRISE_1_ADDRESS': config.get('ENTERPRISE_1_ADDRESS', ''),
        'ENTERPRISE_2_ADDRESS': config.get('ENTERPRISE_2_ADDRESS', ''),
        'ENTERPRISE_3_ADDRESS': config.get('ENTERPRISE_3_ADDRESS', ''),
        'VERIFIER_1_ADDRESS': config.get('VERIFIER_1_ADDRESS', ''),
        'VERIFIER_2_ADDRESS': config.get('VERIFIER_2_ADDRESS', '')
    }

    return jsonify(safe_config)

# API端点，用于前端保存区块链配置
@blockchain_config_bp.route('/api/blockchain/config', methods=['POST'])
@jwt_required()
def save_blockchain_config():
    """保存区块链配置"""
    # 获取用户身份
    user_id = get_jwt_identity()

    # 从数据库获取用户信息
    user = User.query.get(user_id)

    # 检查用户是否是管理员
    if not user or user.role != 'admin':
        return jsonify({"error": "需要管理员权限"}), 403

    # 获取JSON数据
    data = request.get_json()

    # 验证数据
    required_fields = ['ethereum_node_url', 'admin_address', 'admin_private_key']
    for field in required_fields:
        if not data.get(field):
            return jsonify({"error": f"缺少必填字段: {field}"}), 400

    # 构建配置
    new_config = {
        'ETHEREUM_NODE_URL': data.get('ethereum_node_url'),
        'CONTRACT_ADDRESS': data.get('contract_address', ''),
        'ADMIN_ADDRESS': data.get('admin_address'),
        'ADMIN_PRIVATE_KEY': data.get('admin_private_key'),
        'ENTERPRISE_1_ADDRESS': data.get('enterprise_1_address', ''),
        'ENTERPRISE_1_KEY': data.get('enterprise_1_key', ''),
        'ENTERPRISE_2_ADDRESS': data.get('enterprise_2_address', ''),
        'ENTERPRISE_2_KEY': data.get('enterprise_2_key', ''),
        'ENTERPRISE_3_ADDRESS': data.get('enterprise_3_address', ''),
        'ENTERPRISE_3_KEY': data.get('enterprise_3_key', ''),
        'VERIFIER_1_ADDRESS': data.get('verifier_1_address', ''),
        'VERIFIER_1_KEY': data.get('verifier_1_key', ''),
        'VERIFIER_2_ADDRESS': data.get('verifier_2_address', ''),
        'VERIFIER_2_KEY': data.get('verifier_2_key', '')
    }

    # 保存配置
    success, message = save_env_config(new_config)
    if success:
        return jsonify({"message": message})
    else:
        return jsonify({"error": message}), 500

# API端点，用于前端获取Ganache账户
@blockchain_config_bp.route('/api/blockchain/accounts', methods=['GET'])
@jwt_required()
def api_get_ganache_accounts():
    """获取Ganache账户信息"""
    # 获取用户身份
    user_id = get_jwt_identity()

    # 从数据库获取用户信息
    user = User.query.get(user_id)

    # 检查用户是否是管理员
    if not user or user.role != 'admin':
        return jsonify({"error": "需要管理员权限"}), 403

    # 加载当前配置
    config = load_env_config()

    # 获取Ganache账户
    node_url = config.get('ETHEREUM_NODE_URL', 'http://127.0.0.1:8545')
    ganache_accounts, error = get_ganache_accounts(node_url)

    if error:
        return jsonify({"error": f"获取Ganache账户失败: {error}"}), 500

    return jsonify({"accounts": ganache_accounts})

# API端点，用于前端部署智能合约
@blockchain_config_bp.route('/api/blockchain/deploy', methods=['POST'])
@jwt_required()
def api_deploy_contract():
    """部署智能合约"""
    # 获取用户身份
    user_id = get_jwt_identity()

    # 从数据库获取用户信息
    user = User.query.get(user_id)

    # 检查用户是否是管理员
    if not user or user.role != 'admin':
        return jsonify({"error": "需要管理员权限"}), 403

    # 部署合约
    success, message = deploy_smart_contract()

    if success:
        # 重新加载配置
        config = load_env_config()
        return jsonify({
            "message": "智能合约部署成功",
            "contract_address": config.get('CONTRACT_ADDRESS', '')
        })
    else:
        return jsonify({"error": f"智能合约部署失败: {message}"}), 500
