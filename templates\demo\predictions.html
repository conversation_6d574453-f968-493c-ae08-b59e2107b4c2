<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放管理系统 - 预测分析</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background: linear-gradient(to right, #1B5E20, #388E3C);
            color: white;
            padding: 20px 0;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-name {
            margin-right: 15px;
        }
        .logout-btn {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 30px;
            cursor: pointer;
            text-decoration: none;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .logout-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        nav {
            background: linear-gradient(to right, #2E7D32, #43A047);
            padding: 10px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
        }
        .nav-item {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            margin-right: 5px;
            border-radius: 30px;
            transition: all 0.3s ease;
        }
        .nav-item:hover, .nav-item.active {
            background: linear-gradient(to right, #1B5E20, #2E7D32);
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        .card {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            transform: translateY(-5px);
        }
        .card-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        .btn-primary {
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            color: white;
        }
        .chart-container {
            height: 400px;
            margin-top: 20px;
            position: relative;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .chart-placeholder {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 15px;
        }
        .prediction-summary {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        .summary-card {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 25px;
            text-align: center;
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .summary-card:hover {
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            transform: translateY(-5px);
        }
        .summary-value {
            font-size: 36px;
            font-weight: bold;
            margin: 15px 0;
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .summary-label {
            color: #7f8c8d;
            font-size: 14px;
        }
        .prediction-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }
        .control-group {
            display: flex;
            align-items: center;
        }
        .control-group label {
            margin-right: 5px;
            font-weight: bold;
        }
        .control-group select {
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 30px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .control-group select:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 2px 10px rgba(76, 175, 80, 0.2);
        }
        .prediction-details {
            margin-top: 30px;
        }
        .detail-item {
            margin-bottom: 15px;
        }
        .detail-label {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .detail-value {
            color: #7f8c8d;
        }
        .confidence {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            color: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .confidence-high {
            background: linear-gradient(to right, #2ecc71, #27ae60);
        }
        .confidence-medium {
            background: linear-gradient(to right, #f39c12, #e67e22);
        }
        .confidence-low {
            background: linear-gradient(to right, #e74c3c, #c0392b);
        }
        .trend {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            color: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .trend-up {
            background: linear-gradient(to right, #e74c3c, #c0392b);
        }
        .trend-down {
            background: linear-gradient(to right, #2ecc71, #27ae60);
        }
        .trend-stable {
            background: linear-gradient(to right, #3498db, #2980b9);
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo">碳排放管理系统</div>
            <div class="user-info">
                <span class="user-name">北京碳排放科技有限公司</span>
                <a href="/login" class="logout-btn">退出登录</a>
            </div>
        </div>
    </header>

    <nav>
        <div class="nav-content">
            <a href="/dashboard" class="nav-item">仪表板</a>
            <a href="/emissions" class="nav-item">排放数据</a>
            <a href="/verifications" class="nav-item">核查记录</a>
            <a href="/transactions" class="nav-item">碳交易</a>
            <a href="/calculator" class="nav-item">碳计算器</a>
            <a href="/predictions" class="nav-item active">预测分析</a>
            <a href="/reports" class="nav-item">报告生成</a>
        </div>
    </nav>

    <div class="container">
        <h1>排放预测分析</h1>

        <div class="prediction-summary">
            <div class="summary-card">
                <div class="summary-label">预测年排放量</div>
                <div class="summary-value">6,350</div>
                <div class="summary-label">吨CO2e</div>
            </div>
            <div class="summary-card">
                <div class="summary-label">排放趋势</div>
                <div class="summary-value"><span class="trend trend-up">上升</span></div>
                <div class="summary-label">同比增长 8.5%</div>
            </div>
            <div class="summary-card">
                <div class="summary-label">预测置信度</div>
                <div class="summary-value"><span class="confidence confidence-high">高</span></div>
                <div class="summary-label">85% 准确率</div>
            </div>
        </div>

        <div class="prediction-controls">
            <div class="control-group">
                <label for="model-select">预测模型:</label>
                <select id="model-select">
                    <option value="linear">线性回归</option>
                    <option value="arima" selected>ARIMA时间序列</option>
                    <option value="lstm">LSTM神经网络</option>
                </select>
            </div>
            <div class="control-group">
                <label for="period-select">预测周期:</label>
                <select id="period-select">
                    <option value="month">月度</option>
                    <option value="quarter" selected>季度</option>
                    <option value="year">年度</option>
                </select>
            </div>
            <div class="control-group">
                <label for="horizon-select">预测范围:</label>
                <select id="horizon-select">
                    <option value="6">6个月</option>
                    <option value="12" selected>12个月</option>
                    <option value="24">24个月</option>
                </select>
            </div>
            <button class="btn btn-primary" id="update-prediction">更新预测</button>
        </div>

        <div class="card">
            <div class="card-title">排放预测趋势</div>
            <div class="chart-container">
                <canvas id="emissionTrendChart"></canvas>
            </div>
        </div>

        <div class="card">
            <div class="card-title">排放源预测分布</div>
            <div class="chart-container">
                <canvas id="emissionSourceChart"></canvas>
            </div>
        </div>

        <div class="card prediction-details">
            <div class="card-title">预测详情</div>

            <div class="detail-item">
                <div class="detail-label">预测模型</div>
                <div class="detail-value">ARIMA(2,1,2) 时间序列模型</div>
            </div>

            <div class="detail-item">
                <div class="detail-label">训练数据</div>
                <div class="detail-value">使用2021年1月至2023年6月的历史排放数据</div>
            </div>

            <div class="detail-item">
                <div class="detail-label">模型评估</div>
                <div class="detail-value">
                    <div>均方根误差(RMSE): 42.5</div>
                    <div>平均绝对误差(MAE): 35.8</div>
                    <div>R²: 0.87</div>
                </div>
            </div>

            <div class="detail-item">
                <div class="detail-label">影响因素</div>
                <div class="detail-value">
                    <div>1. 生产活动水平 (影响权重: 45%)</div>
                    <div>2. 能源结构变化 (影响权重: 30%)</div>
                    <div>3. 季节性因素 (影响权重: 15%)</div>
                    <div>4. 技术改进 (影响权重: 10%)</div>
                </div>
            </div>

            <div class="detail-item">
                <div class="detail-label">减排建议</div>
                <div class="detail-value">
                    <div>1. 优化能源结构，增加清洁能源使用比例</div>
                    <div>2. 提高能源利用效率，减少能源浪费</div>
                    <div>3. 改进生产工艺，降低单位产品排放强度</div>
                    <div>4. 实施碳捕集技术，减少直接排放</div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 更新预测按钮点击事件
            document.getElementById('update-prediction').addEventListener('click', function() {
                const model = document.getElementById('model-select').value;
                const period = document.getElementById('period-select').value;
                const horizon = document.getElementById('horizon-select').value;

                // 在实际应用中，这里应该发送AJAX请求获取新的预测数据
                // 这里只是演示，显示一个提示
                alert(`正在使用 ${model} 模型生成 ${horizon} 个${period === 'month' ? '月' : period === 'quarter' ? '季度' : '年'}的预测...`);

                // 重新生成图表（模拟更新）
                createCharts();
            });

            // 创建图表
            createCharts();

            function createCharts() {
                // 排放预测趋势图
                const trendCtx = document.getElementById('emissionTrendChart').getContext('2d');
                if (window.trendChart) window.trendChart.destroy();

                window.trendChart = new Chart(trendCtx, {
                    type: 'line',
                    data: {
                        labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                        datasets: [
                            {
                                label: '历史排放量',
                                data: [520, 480, 510, 550, 580, 620, 650, 630, 580, 540, 500, 530],
                                borderColor: 'rgba(52, 152, 219, 1)',
                                backgroundColor: 'rgba(52, 152, 219, 0.1)',
                                borderWidth: 2,
                                pointRadius: 4,
                                fill: true,
                                tension: 0.4
                            },
                            {
                                label: '预测排放量',
                                data: [null, null, null, null, null, null, 650, 670, 690, 710, 730, 750],
                                borderColor: 'rgba(46, 204, 113, 1)',
                                backgroundColor: 'rgba(46, 204, 113, 0.1)',
                                borderWidth: 2,
                                borderDash: [5, 5],
                                pointRadius: 4,
                                fill: true,
                                tension: 0.4
                            },
                            {
                                label: '预测上限',
                                data: [null, null, null, null, null, null, 680, 710, 740, 770, 800, 830],
                                borderColor: 'rgba(231, 76, 60, 0.5)',
                                backgroundColor: 'rgba(0, 0, 0, 0)',
                                borderWidth: 1,
                                borderDash: [3, 3],
                                pointRadius: 0,
                                fill: false
                            },
                            {
                                label: '预测下限',
                                data: [null, null, null, null, null, null, 620, 630, 640, 650, 660, 670],
                                borderColor: 'rgba(46, 204, 113, 0.5)',
                                backgroundColor: 'rgba(0, 0, 0, 0)',
                                borderWidth: 1,
                                borderDash: [3, 3],
                                pointRadius: 0,
                                fill: false
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: false,
                                title: {
                                    display: true,
                                    text: '排放量 (吨CO2e)'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: '2023年'
                                }
                            }
                        },
                        plugins: {
                            title: {
                                display: true,
                                text: '排放量预测趋势 (ARIMA模型)',
                                font: {
                                    size: 16
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const label = context.dataset.label || '';
                                        const value = context.raw || 0;
                                        return `${label}: ${value} 吨CO2e`;
                                    }
                                }
                            }
                        }
                    }
                });

                // 排放源预测分布图
                const sourceCtx = document.getElementById('emissionSourceChart').getContext('2d');
                if (window.sourceChart) window.sourceChart.destroy();

                window.sourceChart = new Chart(sourceCtx, {
                    type: 'bar',
                    data: {
                        labels: ['天然气锅炉', '燃煤锅炉', '工业生产过程', '交通运输', '电力消耗'],
                        datasets: [
                            {
                                label: '当前排放量',
                                data: [1200, 1800, 1500, 800, 1050],
                                backgroundColor: 'rgba(52, 152, 219, 0.8)',
                                borderColor: 'rgba(52, 152, 219, 1)',
                                borderWidth: 1
                            },
                            {
                                label: '预测排放量',
                                data: [1250, 1950, 1600, 850, 1100],
                                backgroundColor: 'rgba(46, 204, 113, 0.8)',
                                borderColor: 'rgba(46, 204, 113, 1)',
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: '排放量 (吨CO2e)'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: '排放源'
                                }
                            }
                        },
                        plugins: {
                            title: {
                                display: true,
                                text: '各排放源预测分布',
                                font: {
                                    size: 16
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const label = context.dataset.label || '';
                                        const value = context.raw || 0;
                                        return `${label}: ${value} 吨CO2e`;
                                    }
                                }
                            }
                        }
                    }
                });
            }
        });
    </script>
</body>
</html>
