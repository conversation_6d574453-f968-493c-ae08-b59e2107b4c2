/**
 * 碳排放核查系统统一样式
 * 绿色环保主题
 */

/* 全局样式 */
:root {
    --primary-color: #43a047;
    --primary-light: #76d275;
    --primary-dark: #00701a;
    --secondary-color: #1de9b6;
    --secondary-light: #6effe8;
    --secondary-dark: #00b686;
    --text-on-primary: #ffffff;
    --text-on-secondary: #000000;
    --background-color: #f5f5f5;
    --card-background: #ffffff;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --info-color: #2196f3;
}

body {
    font-family: 'Roboto', 'Noto Sans SC', sans-serif;
    background-color: var(--background-color);
    color: #333;
    line-height: 1.6;
}

/* 导航栏样式 */
.navbar-dark.bg-success {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 700;
    letter-spacing: 0.5px;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
}

.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 5px;
}

/* 卡片样式 */
.card {
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    overflow: hidden;
    margin-bottom: 20px;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
    color: white;
    font-weight: 600;
    padding: 1rem 1.5rem;
    border: none;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background-color: rgba(0, 0, 0, 0.03);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1rem 1.5rem;
}

/* 按钮样式 */
.btn {
    border-radius: 10px;
    font-weight: 500;
    padding: 0.5rem 1.5rem;
    transition: all 0.3s ease;
    text-transform: none;
    letter-spacing: 0.5px;
}

.btn-success {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border: none;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.btn-success:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--secondary-dark) 100%);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #2196f3 0%, #64b5f6 100%);
    border: none;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.btn-warning {
    background: linear-gradient(135deg, #ff9800 0%, #ffb74d 100%);
    border: none;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.btn-warning:hover {
    background: linear-gradient(135deg, #f57c00 0%, #ffa726 100%);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.btn-danger {
    background: linear-gradient(135deg, #f44336 0%, #ef5350 100%);
    border: none;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #d32f2f 0%, #e53935 100%);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

/* 表单样式 */
.form-control {
    border-radius: 10px;
    padding: 0.75rem 1rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(67, 160, 71, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #555;
}

.form-text {
    color: #777;
}

/* 表格样式 */
.table {
    border-collapse: separate;
    border-spacing: 0;
}

.table th {
    background-color: rgba(67, 160, 71, 0.1);
    font-weight: 600;
    color: #444;
    border-top: none;
}

.table td, .table th {
    padding: 1rem;
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: rgba(67, 160, 71, 0.05);
}

/* 徽章样式 */
.badge {
    font-weight: 500;
    padding: 0.5em 0.8em;
    border-radius: 10px;
}

.badge.bg-success {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, #ff9800 0%, #ffb74d 100%) !important;
}

.badge.bg-danger {
    background: linear-gradient(135deg, #f44336 0%, #ef5350 100%) !important;
}

.badge.bg-info {
    background: linear-gradient(135deg, #2196f3 0%, #64b5f6 100%) !important;
}

.badge.bg-primary {
    background: linear-gradient(135deg, #2196f3 0%, #64b5f6 100%) !important;
}

/* 进度条样式 */
.progress {
    height: 20px;
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

/* 警告框样式 */
.alert {
    border-radius: 15px;
    border: none;
    padding: 1rem 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.alert-success {
    background-color: rgba(76, 175, 80, 0.1);
    color: #2e7d32;
}

.alert-warning {
    background-color: rgba(255, 152, 0, 0.1);
    color: #ef6c00;
}

.alert-danger {
    background-color: rgba(244, 67, 54, 0.1);
    color: #c62828;
}

.alert-info {
    background-color: rgba(33, 150, 243, 0.1);
    color: #1565c0;
}

/* 页脚样式 */
footer {
    background: linear-gradient(135deg, #333 0%, #555 100%);
    color: white;
    padding: 2rem 0;
    margin-top: 3rem;
}

/* 统计卡片样式 */
.stat-card {
    text-align: center;
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    background-color: white;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.stat-card .stat-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.stat-card .stat-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--primary-dark);
}

.stat-card .stat-label {
    font-size: 1rem;
    color: #777;
}

/* 图表容器样式 */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .card {
        margin-bottom: 15px;
    }
    
    .btn {
        padding: 0.4rem 1.2rem;
    }
    
    .stat-card .stat-value {
        font-size: 1.5rem;
    }
    
    .chart-container {
        height: 250px;
    }
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* 打印样式优化 */
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        background-color: white;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .card-header {
        background: #f5f5f5 !important;
        color: #333;
    }
}
