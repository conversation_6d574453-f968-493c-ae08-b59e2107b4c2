<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放管理系统 - 配额管理</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background: linear-gradient(to right, #1B5E20, #388E3C);
            color: white;
            padding: 20px 0;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-name {
            margin-right: 15px;
        }
        .logout-btn {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 30px;
            cursor: pointer;
            text-decoration: none;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .logout-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        nav {
            background: linear-gradient(to right, #2E7D32, #43A047);
            padding: 10px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
        }
        .nav-item {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            margin-right: 5px;
            border-radius: 30px;
            transition: all 0.3s ease;
        }
        .nav-item:hover, .nav-item.active {
            background: linear-gradient(to right, #1B5E20, #2E7D32);
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        .card {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            transform: translateY(-5px);
        }
        .card-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        .btn-primary {
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            color: white;
        }
        .btn-success {
            background: linear-gradient(to right, #2ecc71, #27ae60);
            color: white;
        }
        .btn-danger {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
        }
        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        th, td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        th {
            background: linear-gradient(to right, #43A047, #66BB6A);
            color: white;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 14px;
            letter-spacing: 0.5px;
        }
        tr:last-child td {
            border-bottom: none;
        }
        tr:nth-child(even) {
            background-color: rgba(0,0,0,0.02);
        }
        tr:hover {
            background-color: rgba(76, 175, 80, 0.05);
        }
        .filters {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }
        .filter-group {
            display: flex;
            align-items: center;
        }
        .filter-group label {
            margin-right: 5px;
            font-weight: bold;
        }
        .filter-group select {
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 30px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .filter-group select:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 2px 10px rgba(76, 175, 80, 0.2);
        }
        .quota-stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 25px;
            text-align: center;
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        .stat-value {
            font-size: 36px;
            font-weight: bold;
            margin: 15px 0;
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .stat-label {
            color: #7f8c8d;
            font-size: 14px;
        }
        .chart-container {
            height: 300px;
            margin-top: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .chart-placeholder {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo">碳排放管理系统</div>
            <div class="user-info">
                <span class="user-name">管理员</span>
                <a href="/login" class="logout-btn">退出登录</a>
            </div>
        </div>
    </header>

    <nav>
        <div class="nav-content">
            <a href="/admin" class="nav-item">系统概览</a>
            <a href="/admin_users" class="nav-item">用户管理</a>
            <a href="/admin_quotas" class="nav-item active">配额管理</a>
            <a href="/admin_verifications" class="nav-item">核查管理</a>
            <a href="/admin_transactions" class="nav-item">交易管理</a>
            <a href="/admin_settings" class="nav-item">系统配置</a>
            <a href="/admin_logs" class="nav-item">日志查看</a>
        </div>
    </nav>

    <div class="container">
        <h1>配额管理</h1>

        <div class="quota-stats">
            <div class="stat-card">
                <div class="stat-label">总配额</div>
                <div class="stat-value">100,000</div>
                <div class="stat-label">吨CO2e</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">已分配配额</div>
                <div class="stat-value">75,000</div>
                <div class="stat-label">吨CO2e</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">剩余配额</div>
                <div class="stat-value">25,000</div>
                <div class="stat-label">吨CO2e</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">当前碳价</div>
                <div class="stat-value">50</div>
                <div class="stat-label">元/吨</div>
            </div>
        </div>

        <div class="card">
            <div class="card-title">配额分配情况</div>
            <div class="chart-container">
                <canvas id="quotaDistributionChart"></canvas>
            </div>
        </div>

        <div class="filters">
            <div class="filter-group">
                <label for="industry-filter">行业:</label>
                <select id="industry-filter">
                    <option value="all">全部</option>
                    <option value="energy">能源生产</option>
                    <option value="manufacturing">制造业</option>
                    <option value="chemical">化工行业</option>
                    <option value="building">建筑业</option>
                    <option value="transportation">交通运输</option>
                </select>
            </div>
            <div class="filter-group">
                <label for="year-filter">年份:</label>
                <select id="year-filter">
                    <option value="2023" selected>2023</option>
                    <option value="2022">2022</option>
                    <option value="2021">2021</option>
                </select>
            </div>
        </div>

        <div class="card">
            <div class="card-title">
                <span>企业配额列表</span>
                <a href="#" class="btn btn-primary">分配配额</a>
            </div>
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>企业名称</th>
                        <th>行业</th>
                        <th>年度配额</th>
                        <th>已使用配额</th>
                        <th>剩余配额</th>
                        <th>分配时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>北京碳排放科技有限公司</td>
                        <td>能源生产</td>
                        <td>5,000 吨</td>
                        <td>2,350 吨</td>
                        <td>2,650 吨</td>
                        <td>2023-01-01</td>
                        <td><a href="/admin_quota_detail?id=1" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>上海绿色能源有限公司</td>
                        <td>制造业</td>
                        <td>8,000 吨</td>
                        <td>3,500 吨</td>
                        <td>4,500 吨</td>
                        <td>2023-01-01</td>
                        <td><a href="/admin_quota_detail?id=2" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td>广州环保科技有限公司</td>
                        <td>化工行业</td>
                        <td>10,000 吨</td>
                        <td>4,200 吨</td>
                        <td>5,800 吨</td>
                        <td>2023-01-01</td>
                        <td><a href="/admin_quota_detail?id=3" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>4</td>
                        <td>深圳低碳技术有限公司</td>
                        <td>建筑业</td>
                        <td>7,000 吨</td>
                        <td>3,100 吨</td>
                        <td>3,900 吨</td>
                        <td>2023-01-01</td>
                        <td><a href="/admin_quota_detail?id=4" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>5</td>
                        <td>杭州绿色发展有限公司</td>
                        <td>交通运输</td>
                        <td>6,000 吨</td>
                        <td>2,800 吨</td>
                        <td>3,200 吨</td>
                        <td>2023-01-01</td>
                        <td><a href="/admin_quota_detail?id=5" class="btn btn-primary">查看</a></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 导航切换 - 不需要额外的事件监听器，因为我们使用的是链接
            // 根据当前URL设置活动导航项
            const currentPath = window.location.pathname;
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                if (item.getAttribute('href') === currentPath) {
                    navItems.forEach(i => i.classList.remove('active'));
                    item.classList.add('active');
                }
            });

            // 筛选功能
            const industryFilter = document.getElementById('industry-filter');
            const yearFilter = document.getElementById('year-filter');

            // 这里只是演示，实际应用中应该根据筛选条件过滤数据
            industryFilter.addEventListener('change', function() {
                console.log('行业筛选:', this.value);
            });

            yearFilter.addEventListener('change', function() {
                console.log('年份筛选:', this.value);
            });

            // 创建配额分配图表
            const ctx = document.getElementById('quotaDistributionChart').getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['能源生产', '制造业', '化工行业', '建筑业', '交通运输'],
                    datasets: [
                        {
                            label: '已分配配额',
                            data: [20000, 18000, 15000, 12000, 10000],
                            backgroundColor: 'rgba(46, 204, 113, 0.8)',
                            borderColor: 'rgba(46, 204, 113, 1)',
                            borderWidth: 1
                        },
                        {
                            label: '已使用配额',
                            data: [12000, 8000, 6000, 5000, 4000],
                            backgroundColor: 'rgba(52, 152, 219, 0.8)',
                            borderColor: 'rgba(52, 152, 219, 1)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '配额量 (吨CO2e)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: '行业'
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: '2023年各行业配额分配与使用情况',
                            font: {
                                size: 16
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.dataset.label || '';
                                    const value = context.raw || 0;
                                    return `${label}: ${value.toLocaleString()} 吨CO2e`;
                                }
                            }
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>
