import React, { useState, useEffect } from 'react';
import axios from 'axios';
import '../styles/UserManagement.css';

function UserManagement() {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRole, setSelectedRole] = useState('all');
  const [sortField, setSortField] = useState('createTime');
  const [sortDirection, setSortDirection] = useState('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedItems, setSelectedItems] = useState([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    role: 'user',
    status: 'active',
    department: '',
    position: '',
    phone: ''
  });

  const itemsPerPage = 10;

  useEffect(() => {
    fetchUsers();
  }, [currentPage, sortField, sortDirection, selectedRole, searchTerm]);

  const fetchUsers = async () => {
    try {
      const response = await axios.get('/api/users', {
        params: {
          page: currentPage,
          limit: itemsPerPage,
          sort: sortField,
          direction: sortDirection,
          role: selectedRole,
          search: searchTerm
        },
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      setUsers(response.data.items);
      setTotalPages(Math.ceil(response.data.total / itemsPerPage));
      setLoading(false);
    } catch (err) {
      setError('获取用户列表失败');
      setLoading(false);
    }
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const handleRoleChange = (e) => {
    setSelectedRole(e.target.value);
    setCurrentPage(1);
  };

  const handleSort = (field) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleSelectItem = (id) => {
    setSelectedItems(prev =>
      prev.includes(id)
        ? prev.filter(itemId => itemId !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    setSelectedItems(prev =>
      prev.length === users.length
        ? []
        : users.map(item => item.id)
    );
  };

  const handleDelete = async () => {
    if (!window.confirm('确定要删除选中的用户吗？')) return;

    try {
      await axios.delete('/api/users', {
        data: { ids: selectedItems },
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      setSelectedItems([]);
      fetchUsers();
      setSuccess('用户已删除');
      setTimeout(() => setSuccess(''), 3000);
    } catch (err) {
      setError('删除用户失败');
      setTimeout(() => setError(''), 3000);
    }
  };

  const handleCreate = async () => {
    try {
      await axios.post('/api/users', formData, {
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      setShowCreateModal(false);
      setFormData({
        username: '',
        email: '',
        password: '',
        role: 'user',
        status: 'active',
        department: '',
        position: '',
        phone: ''
      });
      fetchUsers();
      setSuccess('用户已创建');
      setTimeout(() => setSuccess(''), 3000);
    } catch (err) {
      setError('创建用户失败');
      setTimeout(() => setError(''), 3000);
    }
  };

  const handleEdit = async () => {
    try {
      await axios.put(`/api/users/${currentUser.id}`, formData, {
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      setShowEditModal(false);
      setCurrentUser(null);
      setFormData({
        username: '',
        email: '',
        password: '',
        role: 'user',
        status: 'active',
        department: '',
        position: '',
        phone: ''
      });
      fetchUsers();
      setSuccess('用户已更新');
      setTimeout(() => setSuccess(''), 3000);
    } catch (err) {
      setError('更新用户失败');
      setTimeout(() => setError(''), 3000);
    }
  };

  const handleStatusChange = async (userId, newStatus) => {
    try {
      await axios.patch(`/api/users/${userId}/status`, 
        { status: newStatus },
        { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } }
      );
      fetchUsers();
      setSuccess('用户状态已更新');
      setTimeout(() => setSuccess(''), 3000);
    } catch (err) {
      setError('更新用户状态失败');
      setTimeout(() => setError(''), 3000);
    }
  };

  const handleResetPassword = async (userId) => {
    if (!window.confirm('确定要重置该用户的密码吗？')) return;

    try {
      await axios.post(`/api/users/${userId}/reset-password`, {}, {
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      setSuccess('密码已重置');
      setTimeout(() => setSuccess(''), 3000);
    } catch (err) {
      setError('重置密码失败');
      setTimeout(() => setError(''), 3000);
    }
  };

  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) return <div className="loading">加载中...</div>;

  return (
    <div className="user-management">
      <div className="user-header">
        <h2>用户管理</h2>
        <div className="user-actions">
          <button
            className="btn btn-primary"
            onClick={() => setShowCreateModal(true)}
          >
            创建用户
          </button>
          <button
            className="btn btn-danger"
            onClick={handleDelete}
            disabled={selectedItems.length === 0}
          >
            删除选中
          </button>
        </div>
      </div>

      {error && <div className="alert alert-danger">{error}</div>}
      {success && <div className="alert alert-success">{success}</div>}

      <div className="user-filters">
        <div className="search-box">
          <input
            type="text"
            placeholder="搜索用户..."
            value={searchTerm}
            onChange={handleSearch}
            className="form-control"
          />
        </div>
        <div className="filter-box">
          <select
            value={selectedRole}
            onChange={handleRoleChange}
            className="form-control"
          >
            <option value="all">所有角色</option>
            <option value="admin">管理员</option>
            <option value="user">普通用户</option>
            <option value="guest">访客</option>
          </select>
        </div>
      </div>

      <div className="user-table">
        <table>
          <thead>
            <tr>
              <th>
                <input
                  type="checkbox"
                  checked={selectedItems.length === users.length}
                  onChange={handleSelectAll}
                />
              </th>
              <th onClick={() => handleSort('username')}>
                用户名
                {sortField === 'username' && (
                  <span className="sort-icon">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
              <th onClick={() => handleSort('email')}>
                邮箱
                {sortField === 'email' && (
                  <span className="sort-icon">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
              <th onClick={() => handleSort('role')}>
                角色
                {sortField === 'role' && (
                  <span className="sort-icon">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
              <th onClick={() => handleSort('status')}>
                状态
                {sortField === 'status' && (
                  <span className="sort-icon">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
              <th onClick={() => handleSort('createTime')}>
                创建时间
                {sortField === 'createTime' && (
                  <span className="sort-icon">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            {users.map(user => (
              <tr key={user.id}>
                <td>
                  <input
                    type="checkbox"
                    checked={selectedItems.includes(user.id)}
                    onChange={() => handleSelectItem(user.id)}
                  />
                </td>
                <td>{user.username}</td>
                <td>{user.email}</td>
                <td>
                  <span className={`role-tag ${user.role}`}>
                    {user.role === 'admin' ? '管理员' :
                     user.role === 'user' ? '普通用户' : '访客'}
                  </span>
                </td>
                <td>
                  <span className={`status-tag ${user.status}`}>
                    {user.status === 'active' ? '正常' :
                     user.status === 'inactive' ? '禁用' : '待审核'}
                  </span>
                </td>
                <td>{formatDate(user.createTime)}</td>
                <td>
                  <div className="action-buttons">
                    <button
                      className="btn btn-sm btn-secondary"
                      onClick={() => {
                        setCurrentUser(user);
                        setFormData({
                          username: user.username,
                          email: user.email,
                          role: user.role,
                          status: user.status,
                          department: user.department,
                          position: user.position,
                          phone: user.phone
                        });
                        setShowEditModal(true);
                      }}
                    >
                      编辑
                    </button>
                    <button
                      className="btn btn-sm btn-secondary"
                      onClick={() => handleResetPassword(user.id)}
                    >
                      重置密码
                    </button>
                    <select
                      value={user.status}
                      onChange={(e) => handleStatusChange(user.id, e.target.value)}
                      className="status-select"
                    >
                      <option value="active">正常</option>
                      <option value="inactive">禁用</option>
                      <option value="pending">待审核</option>
                    </select>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="pagination">
        <button
          className="btn btn-sm"
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          上一页
        </button>
        <span className="page-info">
          第 {currentPage} 页，共 {totalPages} 页
        </span>
        <button
          className="btn btn-sm"
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          下一页
        </button>
      </div>

      {showCreateModal && (
        <div className="modal">
          <div className="modal-content">
            <h3>创建用户</h3>
            <div className="create-form">
              <div className="form-group">
                <label htmlFor="username">用户名</label>
                <input
                  type="text"
                  id="username"
                  name="username"
                  value={formData.username}
                  onChange={handleFormChange}
                  className="form-control"
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="email">邮箱</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleFormChange}
                  className="form-control"
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="password">密码</label>
                <input
                  type="password"
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleFormChange}
                  className="form-control"
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="role">角色</label>
                <select
                  id="role"
                  name="role"
                  value={formData.role}
                  onChange={handleFormChange}
                  className="form-control"
                  required
                >
                  <option value="admin">管理员</option>
                  <option value="user">普通用户</option>
                  <option value="guest">访客</option>
                </select>
              </div>
              <div className="form-group">
                <label htmlFor="department">部门</label>
                <input
                  type="text"
                  id="department"
                  name="department"
                  value={formData.department}
                  onChange={handleFormChange}
                  className="form-control"
                />
              </div>
              <div className="form-group">
                <label htmlFor="position">职位</label>
                <input
                  type="text"
                  id="position"
                  name="position"
                  value={formData.position}
                  onChange={handleFormChange}
                  className="form-control"
                />
              </div>
              <div className="form-group">
                <label htmlFor="phone">电话</label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleFormChange}
                  className="form-control"
                />
              </div>
              <div className="modal-actions">
                <button
                  className="btn btn-primary"
                  onClick={handleCreate}
                >
                  创建
                </button>
                <button
                  className="btn btn-secondary"
                  onClick={() => setShowCreateModal(false)}
                >
                  取消
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {showEditModal && (
        <div className="modal">
          <div className="modal-content">
            <h3>编辑用户</h3>
            <div className="edit-form">
              <div className="form-group">
                <label htmlFor="edit-username">用户名</label>
                <input
                  type="text"
                  id="edit-username"
                  name="username"
                  value={formData.username}
                  onChange={handleFormChange}
                  className="form-control"
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="edit-email">邮箱</label>
                <input
                  type="email"
                  id="edit-email"
                  name="email"
                  value={formData.email}
                  onChange={handleFormChange}
                  className="form-control"
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="edit-role">角色</label>
                <select
                  id="edit-role"
                  name="role"
                  value={formData.role}
                  onChange={handleFormChange}
                  className="form-control"
                  required
                >
                  <option value="admin">管理员</option>
                  <option value="user">普通用户</option>
                  <option value="guest">访客</option>
                </select>
              </div>
              <div className="form-group">
                <label htmlFor="edit-status">状态</label>
                <select
                  id="edit-status"
                  name="status"
                  value={formData.status}
                  onChange={handleFormChange}
                  className="form-control"
                  required
                >
                  <option value="active">正常</option>
                  <option value="inactive">禁用</option>
                  <option value="pending">待审核</option>
                </select>
              </div>
              <div className="form-group">
                <label htmlFor="edit-department">部门</label>
                <input
                  type="text"
                  id="edit-department"
                  name="department"
                  value={formData.department}
                  onChange={handleFormChange}
                  className="form-control"
                />
              </div>
              <div className="form-group">
                <label htmlFor="edit-position">职位</label>
                <input
                  type="text"
                  id="edit-position"
                  name="position"
                  value={formData.position}
                  onChange={handleFormChange}
                  className="form-control"
                />
              </div>
              <div className="form-group">
                <label htmlFor="edit-phone">电话</label>
                <input
                  type="tel"
                  id="edit-phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleFormChange}
                  className="form-control"
                />
              </div>
              <div className="modal-actions">
                <button
                  className="btn btn-primary"
                  onClick={handleEdit}
                >
                  保存
                </button>
                <button
                  className="btn btn-secondary"
                  onClick={() => {
                    setShowEditModal(false);
                    setCurrentUser(null);
                  }}
                >
                  取消
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default UserManagement; 