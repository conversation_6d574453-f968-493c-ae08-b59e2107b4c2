"""
一键启动脚本
用于启动演示服务器，方便系统演示
"""

import subprocess
import sys
import time
import webbrowser

from db_utils import test_connection, verify_database


def check_database():
    """检查数据库连接和初始化状态"""
    print("检查数据库连接...")
    if not test_connection():
        print("数据库连接失败，请检查数据库配置")
        return False

    print("检查数据库初始化状态...")
    if not verify_database():
        print("数据库未初始化或初始化不完整")
        choice = input("是否要初始化数据库？(y/n): ")
        if choice.lower() == 'y':
            subprocess.run([sys.executable, "init_db.py", "--sql"])
        else:
            print("跳过数据库初始化")
    else:
        print("数据库已初始化")

    # 检查用户表结构
    print("检查用户表结构...")
    try:
        from db_utils import get_db_connection
        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查password_hash字段长度
        cursor.execute("""
            SELECT CHARACTER_MAXIMUM_LENGTH
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'user'
            AND COLUMN_NAME = 'password_hash'
        """)
        result = cursor.fetchone()

        if result and result[0] < 512:
            print("用户表中的password_hash字段长度不足，需要修改")
            choice = input("是否要修改用户表结构？(y/n): ")
            if choice.lower() == 'y':
                try:
                    # 直接执行SQL语句修改表结构
                    cursor.execute("ALTER TABLE `user` MODIFY COLUMN `password_hash` varchar(512) NOT NULL;")
                    conn.commit()
                    print("用户表修改成功")
                except Exception as e:
                    print(f"修改用户表失败: {str(e)}")
            else:
                print("跳过修改用户表结构，可能会导致演示数据生成失败")

        conn.close()
    except Exception as e:
        print(f"检查用户表结构失败: {str(e)}")

    return True


def generate_demo_data():
    """生成演示数据"""
    print("检查是否需要生成演示数据...")

    # 检查数据库中是否已有数据
    conn = None
    try:
        from db_utils import get_db_connection
        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查用户表
        cursor.execute("SELECT COUNT(*) FROM `user`")
        user_count = cursor.fetchone()[0]

        if user_count > 1:  # 已有管理员用户和其他用户
            print("数据库中已有演示数据，跳过生成")
            return True

        print("数据库中没有足够的演示数据，需要生成")
        choice = input("是否要生成演示数据？(y/n): ")
        if choice.lower() == 'y':
            subprocess.run([sys.executable, "generate_demo_data.py"])
            return True
        else:
            print("跳过生成演示数据")
            return True
    except Exception as e:
        print(f"检查演示数据失败: {str(e)}")
        return False
    finally:
        if conn:
            conn.close()


def start_demo_server():
    """启动演示服务器"""
    print("启动演示服务器...")
    demo_process = subprocess.Popen([sys.executable, "demo_server.py"])
    print("演示服务器已启动，PID:", demo_process.pid)
    return demo_process


def open_browser():
    """打开浏览器"""
    print("等待服务启动...")
    time.sleep(3)
    print("打开浏览器...")
    webbrowser.open("http://localhost:5000")


def main():
    """主函数"""
    print("=== 碳排放管理系统演示启动脚本 ===")

    # 检查数据库
    if not check_database():
        return

    # 生成演示数据
    if not generate_demo_data():
        return

    # 启动演示服务器
    demo_process = start_demo_server()

    # 打开浏览器
    open_browser()

    print("演示系统已启动，按Ctrl+C停止服务")

    try:
        # 等待用户中断
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("正在停止服务...")
        demo_process.terminate()
        print("服务已停止")


if __name__ == "__main__":
    main()
