{% extends 'demo/layouts/verifier_base_unified.html' %}

{% set active_page = 'dashboard' %}

{% block head %}
<style>
    .dashboard {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
    }
    .stat-card {
        text-align: center;
        padding: 30px 20px;
    }
    .stat-value {
        font-size: 36px;
        font-weight: bold;
        background: linear-gradient(to right, #4CAF50, #2E7D32);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        margin: 15px 0;
        text-shadow: 0 2px 5px rgba(0,0,0,0.05);
    }
    .stat-label {
        color: #7f8c8d;
    }
    .chart-container {
        height: 300px;
        margin-top: 20px;
        margin-bottom: 30px;
    }
    .status {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        display: inline-block;
    }
    .status-pending {
        background: linear-gradient(to right, #f39c12, #e67e22);
        color: white;
    }
    .status-verified {
        background: linear-gradient(to right, #2ecc71, #27ae60);
        color: white;
    }
    .status-rejected {
        background: linear-gradient(to right, #e74c3c, #c0392b);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
    <h1>核查机构仪表板</h1>

    <div class="dashboard">
        <div class="card stat-card">
            <div class="stat-label">待核查任务</div>
            <div class="stat-value">12</div>
            <div class="stat-label">个</div>
        </div>
        <div class="card stat-card">
            <div class="stat-label">已完成核查</div>
            <div class="stat-value">45</div>
            <div class="stat-label">个</div>
        </div>
        <div class="card stat-card">
            <div class="stat-label">核查通过率</div>
            <div class="stat-value">85%</div>
            <div class="stat-label">本年度</div>
        </div>
    </div>

    <div class="chart-container">
        <canvas id="verificationChart"></canvas>
    </div>

    <div class="card">
        <div class="card-title">待核查任务</div>
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>企业</th>
                    <th>排放源</th>
                    <th>排放量</th>
                    <th>提交时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>1002</td>
                    <td>北京碳排放科技有限公司</td>
                    <td>天然气锅炉</td>
                    <td>320 吨CO2e</td>
                    <td>2025-05-10</td>
                    <td><a href="/verification_detail?id=1002" class="btn btn-primary">核查</a></td>
                </tr>
                <tr>
                    <td>1005</td>
                    <td>上海绿色能源有限公司</td>
                    <td>电力消耗</td>
                    <td>450 吨CO2e</td>
                    <td>2025-05-11</td>
                    <td><a href="/verification_detail?id=1005" class="btn btn-primary">核查</a></td>
                </tr>
                <tr>
                    <td>1008</td>
                    <td>广州环保科技有限公司</td>
                    <td>工业生产过程</td>
                    <td>680 吨CO2e</td>
                    <td>2025-05-12</td>
                    <td><a href="/verification_detail?id=1008" class="btn btn-primary">核查</a></td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="card">
        <div class="card-title">最近核查记录</div>
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>企业</th>
                    <th>排放源</th>
                    <th>排放量</th>
                    <th>核查结果</th>
                    <th>核查时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>1001</td>
                    <td>北京碳排放科技有限公司</td>
                    <td>燃煤锅炉</td>
                    <td>450 吨CO2e</td>
                    <td><span class="status status-verified">通过</span></td>
                    <td>2025-05-08</td>
                    <td><a href="/verification_detail?id=1001" class="btn btn-primary">查看</a></td>
                </tr>
                <tr>
                    <td>1003</td>
                    <td>广州环保科技有限公司</td>
                    <td>工业生产过程</td>
                    <td>780 吨CO2e</td>
                    <td><span class="status status-rejected">拒绝</span></td>
                    <td>2025-05-09</td>
                    <td><a href="/verification_detail?id=1003" class="btn btn-primary">查看</a></td>
                </tr>
                <tr>
                    <td>1004</td>
                    <td>上海绿色能源有限公司</td>
                    <td>交通运输</td>
                    <td>250 吨CO2e</td>
                    <td><span class="status status-verified">通过</span></td>
                    <td>2025-05-10</td>
                    <td><a href="/verification_detail?id=1004" class="btn btn-primary">查看</a></td>
                </tr>
            </tbody>
        </table>
    </div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 创建图表
        const ctx = document.getElementById('verificationChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                datasets: [
                    {
                        label: '核查任务数',
                        data: [8, 12, 15, 18, 20, 25],
                        borderColor: 'rgba(46, 204, 113, 1)',
                        backgroundColor: 'rgba(46, 204, 113, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    },
                    {
                        label: '通过率 (%)',
                        data: [75, 80, 85, 82, 88, 90],
                        borderColor: 'rgba(52, 152, 219, 1)',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '核查任务数'
                        }
                    },
                    y1: {
                        beginAtZero: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: '通过率 (%)'
                        },
                        min: 0,
                        max: 100,
                        grid: {
                            drawOnChartArea: false
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: '核查任务统计',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                }
            }
        });
    });
</script>
{% endblock %}
