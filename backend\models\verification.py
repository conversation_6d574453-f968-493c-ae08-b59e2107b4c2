"""
核查记录模型
"""

from datetime import datetime
from backend import db
from backend.models.emission import EmissionData, Emission
from backend.models.user import User

class Verification(db.Model):
    """核查数据模型"""
    __tablename__ = 'verification'

    id = db.Column(db.Integer, primary_key=True)
    emission_id = db.Column(db.Integer, db.<PERSON>ey('emission.id'), nullable=False)
    verifier_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    conclusion = db.Column(db.String(20), nullable=False)  # approved, rejected
    comments = db.Column(db.Text)
    verification_time = db.Column(db.DateTime, default=datetime.now)
    blockchain_hash = db.Column(db.String(66))
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # 关联关系
    emission = db.relationship('Emission', backref=db.backref('verification', uselist=False, lazy=True))
    verifier = db.relationship('User', backref=db.backref('verifications', lazy=True))

    def __init__(self, emission_id, verifier_id, conclusion, comments=None, blockchain_hash=None):
        self.emission_id = emission_id
        self.verifier_id = verifier_id
        self.conclusion = conclusion
        self.comments = comments
        self.blockchain_hash = blockchain_hash

    def __repr__(self):
        return f'<Verification {self.id}: {self.conclusion}>'

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'emission_id': self.emission_id,
            'verifier_id': self.verifier_id,
            'verifier_name': self.verifier.company_name if self.verifier else None,
            'conclusion': self.conclusion,
            'comments': self.comments,
            'verification_time': self.verification_time.strftime('%Y-%m-%d %H:%M:%S') if self.verification_time else None,
            'blockchain_hash': self.blockchain_hash,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S')
        }

# 保留旧模型以兼容现有代码
class VerificationOld(db.Model):
    __tablename__ = 'verification_old'

    id = db.Column(db.Integer, primary_key=True)
    emission_data_id = db.Column(db.Integer, db.ForeignKey('emission_data.id'), nullable=False)
    verifier_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    conclusion = db.Column(db.String(20), nullable=False)  # approved, rejected
    comments = db.Column(db.Text)
    verification_time = db.Column(db.DateTime, default=datetime.now)
    blockchain_hash = db.Column(db.String(66))
    blockchain_block = db.Column(db.Integer)

    emission_data = db.relationship('EmissionData', backref=db.backref('verifications', lazy=True))
    verifier = db.relationship('User', backref=db.backref('verifications_old', lazy=True))

    def to_dict(self):
        return {
            'id': self.id,
            'emission_data_id': self.emission_data_id,
            'verifier_id': self.verifier_id,
            'verifier_name': self.verifier.username if self.verifier else None,
            'conclusion': self.conclusion,
            'comments': self.comments,
            'verification_time': self.verification_time.isoformat() if self.verification_time else None,
            'blockchain_hash': self.blockchain_hash,
            'blockchain_block': self.blockchain_block
        }
