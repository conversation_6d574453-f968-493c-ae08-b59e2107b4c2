<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放管理系统 - 碳计算器</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background: linear-gradient(to right, #1B5E20, #388E3C);
            color: white;
            padding: 20px 0;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-name {
            margin-right: 15px;
        }
        .logout-btn {
            background-color: #e74c3c;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        nav {
            background: linear-gradient(to right, #2E7D32, #43A047);
            padding: 10px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
        }
        .nav-item {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            margin-right: 5px;
            border-radius: 30px;
            transition: all 0.3s ease;
        }
        .nav-item:hover, .nav-item.active {
            background: linear-gradient(to right, #1B5E20, #2E7D32);
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        .card {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            transform: translateY(-5px);
        }
        .card-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 30px;
            font-size: 14px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        input:focus, select:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 2px 10px rgba(76, 175, 80, 0.2);
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        .btn-primary {
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            color: white;
        }
        .btn-success {
            background: linear-gradient(to right, #2ecc71, #27ae60);
            color: white;
        }
        .calculator-form {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }
        .calculator-section {
            margin-bottom: 20px;
        }
        .calculator-section-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        .result-container {
            margin-top: 30px;
            padding: 30px;
            background: linear-gradient(to bottom, #f1f8e9, #dcedc8);
            border-radius: 15px;
            border: 1px solid rgba(76, 175, 80, 0.2);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .result-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .result-value {
            font-size: 48px;
            font-weight: bold;
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin: 15px 0;
            text-align: center;
            text-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .result-unit {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 20px;
        }
        .result-breakdown {
            margin-top: 20px;
        }
        .breakdown-item {
            display: flex;
            justify-content: space-between;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.7);
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .breakdown-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .breakdown-label {
            font-weight: bold;
            color: #2c3e50;
        }
        .breakdown-value {
            color: #4CAF50;
            font-weight: bold;
        }
        .actions {
            margin-top: 20px;
            display: flex;
            justify-content: flex-end;
        }
        .actions button {
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo">碳排放管理系统</div>
            <div class="user-info">
                <span class="user-name">北京碳排放科技有限公司</span>
                <a href="/login" class="logout-btn">退出登录</a>
            </div>
        </div>
    </header>

    <nav>
        <div class="nav-content">
            <a href="/dashboard" class="nav-item">仪表板</a>
            <a href="/emissions" class="nav-item">排放数据</a>
            <a href="/verifications" class="nav-item">核查记录</a>
            <a href="/transactions" class="nav-item">碳交易</a>
            <a href="/calculator" class="nav-item active">碳计算器</a>
            <a href="/predictions" class="nav-item">预测分析</a>
            <a href="/reports" class="nav-item">报告生成</a>
        </div>
    </nav>

    <div class="container">
        <h1>碳排放计算器</h1>

        <div class="card">
            <div class="card-title">输入活动数据</div>

            <form id="calculator-form" class="calculator-form">
                <div>
                    <div class="calculator-section">
                        <div class="calculator-section-title">能源消耗</div>

                        <div class="form-group">
                            <label for="electricity">电力消耗 (kWh)</label>
                            <input type="number" id="electricity" name="electricity" placeholder="输入电力消耗量">
                        </div>

                        <div class="form-group">
                            <label for="coal">煤炭消耗 (kg)</label>
                            <input type="number" id="coal" name="coal" placeholder="输入煤炭消耗量">
                        </div>

                        <div class="form-group">
                            <label for="natural_gas">天然气消耗 (m³)</label>
                            <input type="number" id="natural_gas" name="natural_gas" placeholder="输入天然气消耗量">
                        </div>
                    </div>

                    <div class="calculator-section">
                        <div class="calculator-section-title">交通运输</div>

                        <div class="form-group">
                            <label for="gasoline">汽油消耗 (L)</label>
                            <input type="number" id="gasoline" name="gasoline" placeholder="输入汽油消耗量">
                        </div>

                        <div class="form-group">
                            <label for="diesel">柴油消耗 (L)</label>
                            <input type="number" id="diesel" name="diesel" placeholder="输入柴油消耗量">
                        </div>
                    </div>
                </div>

                <div>
                    <div class="calculator-section">
                        <div class="calculator-section-title">工业生产</div>

                        <div class="form-group">
                            <label for="cement">水泥生产 (kg)</label>
                            <input type="number" id="cement" name="cement" placeholder="输入水泥生产量">
                        </div>

                        <div class="form-group">
                            <label for="steel">钢铁生产 (kg)</label>
                            <input type="number" id="steel" name="steel" placeholder="输入钢铁生产量">
                        </div>
                    </div>

                    <div class="calculator-section">
                        <div class="calculator-section-title">废弃物处理</div>

                        <div class="form-group">
                            <label for="landfill">填埋废弃物 (kg)</label>
                            <input type="number" id="landfill" name="landfill" placeholder="输入填埋废弃物量">
                        </div>

                        <div class="form-group">
                            <label for="incineration">焚烧废弃物 (kg)</label>
                            <input type="number" id="incineration" name="incineration" placeholder="输入焚烧废弃物量">
                        </div>
                    </div>
                </div>

                <div class="actions" style="grid-column: span 2;">
                    <button type="reset" class="btn">重置</button>
                    <button type="submit" class="btn btn-primary">计算</button>
                </div>
            </form>
        </div>

        <div class="result-container" id="result-container" style="display: none;">
            <div class="result-title">计算结果</div>

            <div class="result-value" id="total-emission">0</div>
            <div class="result-unit">吨CO2e</div>

            <div style="height: 300px; margin: 30px 0;">
                <canvas id="emissionChart"></canvas>
            </div>

            <div class="result-breakdown">
                <div class="breakdown-item">
                    <div class="breakdown-label">能源消耗</div>
                    <div class="breakdown-value" id="energy-emission">0 吨CO2e</div>
                </div>
                <div class="breakdown-item">
                    <div class="breakdown-label">交通运输</div>
                    <div class="breakdown-value" id="transport-emission">0 吨CO2e</div>
                </div>
                <div class="breakdown-item">
                    <div class="breakdown-label">工业生产</div>
                    <div class="breakdown-value" id="industrial-emission">0 吨CO2e</div>
                </div>
                <div class="breakdown-item">
                    <div class="breakdown-label">废弃物处理</div>
                    <div class="breakdown-value" id="waste-emission">0 吨CO2e</div>
                </div>
            </div>

            <div class="actions">
                <button class="btn btn-success" id="save-result">保存结果</button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const calculatorForm = document.getElementById('calculator-form');
            const resultContainer = document.getElementById('result-container');
            let emissionChart = null;

            calculatorForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // 获取表单数据
                const formData = {
                    electricity: parseFloat(document.getElementById('electricity').value) || 0,
                    coal: parseFloat(document.getElementById('coal').value) || 0,
                    natural_gas: parseFloat(document.getElementById('natural_gas').value) || 0,
                    gasoline: parseFloat(document.getElementById('gasoline').value) || 0,
                    diesel: parseFloat(document.getElementById('diesel').value) || 0,
                    cement: parseFloat(document.getElementById('cement').value) || 0,
                    steel: parseFloat(document.getElementById('steel').value) || 0,
                    landfill: parseFloat(document.getElementById('landfill').value) || 0,
                    incineration: parseFloat(document.getElementById('incineration').value) || 0
                };

                // 计算排放量（简化版）
                const emissions = {
                    energy: (formData.electricity * 0.5839 + formData.coal * 2.86 + formData.natural_gas * 2.1) / 1000,
                    transport: (formData.gasoline * 2.3 + formData.diesel * 2.68) / 1000,
                    industrial: (formData.cement * 0.52 + formData.steel * 1.85) / 1000,
                    waste: (formData.landfill * 0.99 + formData.incineration * 0.58) / 1000
                };

                const totalEmission = emissions.energy + emissions.transport + emissions.industrial + emissions.waste;

                // 显示结果
                document.getElementById('total-emission').textContent = totalEmission.toFixed(2);
                document.getElementById('energy-emission').textContent = emissions.energy.toFixed(2) + ' 吨CO2e';
                document.getElementById('transport-emission').textContent = emissions.transport.toFixed(2) + ' 吨CO2e';
                document.getElementById('industrial-emission').textContent = emissions.industrial.toFixed(2) + ' 吨CO2e';
                document.getElementById('waste-emission').textContent = emissions.waste.toFixed(2) + ' 吨CO2e';

                // 创建或更新图表
                const ctx = document.getElementById('emissionChart').getContext('2d');

                // 如果图表已存在，销毁它
                if (emissionChart) {
                    emissionChart.destroy();
                }

                // 创建新图表
                emissionChart = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: ['能源消耗', '交通运输', '工业生产', '废弃物处理'],
                        datasets: [{
                            data: [
                                emissions.energy.toFixed(2),
                                emissions.transport.toFixed(2),
                                emissions.industrial.toFixed(2),
                                emissions.waste.toFixed(2)
                            ],
                            backgroundColor: [
                                'rgba(46, 204, 113, 0.8)',
                                'rgba(52, 152, 219, 0.8)',
                                'rgba(155, 89, 182, 0.8)',
                                'rgba(241, 196, 15, 0.8)'
                            ],
                            borderColor: [
                                'rgba(46, 204, 113, 1)',
                                'rgba(52, 152, 219, 1)',
                                'rgba(155, 89, 182, 1)',
                                'rgba(241, 196, 15, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'right',
                            },
                            title: {
                                display: true,
                                text: '碳排放来源分布',
                                font: {
                                    size: 16
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const label = context.label || '';
                                        const value = context.raw || 0;
                                        const total = context.chart.data.datasets[0].data.reduce(
                                            (sum, value) => sum + parseFloat(value), 0
                                        );
                                        const percentage = Math.round((value * 100) / total);
                                        return `${label}: ${value} 吨CO2e (${percentage}%)`;
                                    }
                                }
                            }
                        }
                    }
                });

                resultContainer.style.display = 'block';
            });

            // 保存结果按钮
            document.getElementById('save-result').addEventListener('click', function() {
                alert('计算结果已保存');
            });
        });
    </script>
</body>
</html>
