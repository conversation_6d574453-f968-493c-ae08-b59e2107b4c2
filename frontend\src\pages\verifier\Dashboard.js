import React, { useState, useEffect } from 'react';
import api, { endpoints } from '../../config/api';
import '../../styles/verifier/Dashboard.css';

const VerifierDashboard = () => {
  const [stats, setStats] = useState({
    pendingVerifications: 0,
    completedVerifications: 0,
    totalReports: 0,
    verificationRate: 0
  });
  const [verifications, setVerifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const [statsResponse, verificationsResponse] = await Promise.all([
        api.get(endpoints.verifier.stats),
        api.get(endpoints.verifier.pendingVerifications)
      ]);
      setStats(statsResponse.data);
      setVerifications(verificationsResponse.data);
    } catch (err) {
      setError('获取数据失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleVerify = async (id, status) => {
    try {
      await api.post(endpoints.verifier.verify(id), { status });
      fetchDashboardData();
    } catch (err) {
      setError('验证操作失败，请重试');
    }
  };

  if (loading) {
    return <div className="loading">加载中...</div>;
  }

  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }

  return (
    <div className="verifier-dashboard">
      <h1 className="page-title">验证者仪表板</h1>
      
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon pending-icon">
            <i className="fas fa-clock"></i>
          </div>
          <div className="stat-content">
            <h3 className="stat-title">待验证</h3>
            <p className="stat-value">{stats.pendingVerifications}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon completed-icon">
            <i className="fas fa-check-circle"></i>
          </div>
          <div className="stat-content">
            <h3 className="stat-title">已完成</h3>
            <p className="stat-value">{stats.completedVerifications}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon reports-icon">
            <i className="fas fa-file-alt"></i>
          </div>
          <div className="stat-content">
            <h3 className="stat-title">报告总数</h3>
            <p className="stat-value">{stats.totalReports}</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon rate-icon">
            <i className="fas fa-chart-line"></i>
          </div>
          <div className="stat-content">
            <h3 className="stat-title">验证率</h3>
            <p className="stat-value">{stats.verificationRate}%</p>
          </div>
        </div>
      </div>

      <div className="dashboard-grid">
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">待验证列表</h2>
          </div>
          <div className="table-container">
            <table className="table">
              <thead>
                <tr>
                  <th>企业名称</th>
                  <th>排放类型</th>
                  <th>提交时间</th>
                  <th>状态</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                {verifications.map(verification => (
                  <tr key={verification.id}>
                    <td>{verification.companyName}</td>
                    <td>{verification.emissionType}</td>
                    <td>{new Date(verification.submittedAt).toLocaleDateString()}</td>
                    <td>
                      <span className={`status-badge ${verification.status}`}>
                        {verification.status === 'pending' ? '待验证' : '已验证'}
                      </span>
                    </td>
                    <td>
                      <div className="action-buttons">
                        <button
                          className="btn btn-primary btn-sm"
                          onClick={() => handleVerify(verification.id, 'approved')}
                        >
                          通过
                        </button>
                        <button
                          className="btn btn-danger btn-sm"
                          onClick={() => handleVerify(verification.id, 'rejected')}
                        >
                          拒绝
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        <div className="card">
          <div className="card-header">
            <h2 className="card-title">验证统计</h2>
          </div>
          <div className="verification-stats">
            <div className="stat-item">
              <span className="stat-label">今日验证</span>
              <span className="stat-value">{stats.todayVerifications}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">本周验证</span>
              <span className="stat-value">{stats.weekVerifications}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">平均响应时间</span>
              <span className="stat-value">{stats.averageResponseTime}小时</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VerifierDashboard; 