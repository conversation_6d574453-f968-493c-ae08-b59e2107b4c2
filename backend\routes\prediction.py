"""
预测分析相关路由
"""

from datetime import datetime
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from sqlalchemy import func

from backend import db
from backend.models.user import User
from backend.models.emission import EmissionData
from backend.models.prediction_model import PredictionModel
from backend.models.activity import Activity

prediction_bp = Blueprint('prediction', __name__)

@prediction_bp.route('/analyze', methods=['POST'])
@jwt_required()
def analyze_emissions():
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    if not user:
        return jsonify({'error': '用户不存在'}), 404
    
    data = request.get_json()
    
    # 获取历史排放数据
    if user.role == 'admin':
        # 管理员可以分析所有企业的数据
        if 'enterprise_id' in data:
            emissions = EmissionData.query.filter_by(
                enterprise_id=data['enterprise_id'],
                status='verified'
            ).order_by(EmissionData.emission_period_start).all()
        else:
            # 如果没有指定企业，则分析所有企业的总排放量
            emissions = db.session.query(
                func.date(EmissionData.emission_period_start).label('date'),
                func.sum(EmissionData.emission_amount).label('amount')
            ).filter(EmissionData.status == 'verified').group_by('date').order_by('date').all()
            
            emission_data = [item.amount for item in emissions]
    elif user.role == 'enterprise':
        # 企业用户只能分析自己的数据
        emissions = EmissionData.query.filter_by(
            enterprise_id=current_user_id,
            status='verified'
        ).order_by(EmissionData.emission_period_start).all()
        
        emission_data = [emission.emission_amount for emission in emissions]
    else:
        return jsonify({'error': '无权进行预测分析'}), 403
    
    # 如果没有足够的数据，返回错误
    if len(emission_data) < 3:
        return jsonify({'error': '历史数据不足，无法进行预测分析'}), 400
    
    # 分析趋势
    trend_result = current_app.emission_predictor.analyze_trends(emission_data)
    
    # 训练模型
    model_type = data.get('model_type', 'linear')
    model = current_app.emission_predictor.train_model(emission_data, model_type)
    
    # 预测未来排放
    periods = data.get('periods', 12)
    predictions = current_app.emission_predictor.predict(model, periods)
    
    # 记录活动
    activity = Activity(
        user_id=current_user_id,
        activity_type='prediction_analysis',
        description=f'用户进行了排放预测分析，模型类型: {model_type}'
    )
    db.session.add(activity)
    db.session.commit()
    
    return jsonify({
        'message': '分析成功',
        'trend': trend_result,
        'predictions': predictions,
        'model_type': model_type
    }), 200

@prediction_bp.route('/models', methods=['POST'])
@jwt_required()
def create_model():
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    if not user or user.role not in ['admin', 'enterprise']:
        return jsonify({'error': '无权创建预测模型'}), 403
    
    data = request.get_json()
    
    # 验证必填字段
    required_fields = ['name', 'description', 'model_type']
    for field in required_fields:
        if field not in data:
            return jsonify({'error': f'缺少必填字段: {field}'}), 400
    
    # 获取历史排放数据
    if user.role == 'admin' and 'enterprise_id' in data:
        emissions = EmissionData.query.filter_by(
            enterprise_id=data['enterprise_id'],
            status='verified'
        ).order_by(EmissionData.emission_period_start).all()
        
        emission_data = [emission.emission_amount for emission in emissions]
    else:
        emissions = EmissionData.query.filter_by(
            enterprise_id=current_user_id,
            status='verified'
        ).order_by(EmissionData.emission_period_start).all()
        
        emission_data = [emission.emission_amount for emission in emissions]
    
    # 如果没有足够的数据，返回错误
    if len(emission_data) < 3:
        return jsonify({'error': '历史数据不足，无法创建预测模型'}), 400
    
    # 训练模型
    model_type = data['model_type']
    model = current_app.emission_predictor.train_model(emission_data, model_type)
    
    # 保存模型
    model_path = f"models/{data['name'].replace(' ', '_').lower()}_{datetime.now().strftime('%Y%m%d%H%M%S')}.json"
    
    prediction_model = PredictionModel(
        name=data['name'],
        description=data['description'],
        model_path=model_path,
        created_at=datetime.now(),
        last_trained=datetime.now()
    )
    prediction_model.set_metrics({'model': model})
    
    db.session.add(prediction_model)
    db.session.commit()
    
    # 记录活动
    activity = Activity(
        user_id=current_user_id,
        activity_type='create_prediction_model',
        description=f'用户创建了预测模型，ID: {prediction_model.id}，名称: {prediction_model.name}'
    )
    db.session.add(activity)
    db.session.commit()
    
    return jsonify({
        'message': '预测模型创建成功',
        'model': prediction_model.to_dict()
    }), 201

@prediction_bp.route('/models', methods=['GET'])
@jwt_required()
def get_models():
    # 获取所有预测模型
    models = PredictionModel.query.all()
    
    return jsonify({
        'models': [model.to_dict() for model in models]
    }), 200

@prediction_bp.route('/models/<int:model_id>', methods=['GET'])
@jwt_required()
def get_model(model_id):
    model = PredictionModel.query.get(model_id)
    if not model:
        return jsonify({'error': '预测模型不存在'}), 404
    
    return jsonify({
        'model': model.to_dict()
    }), 200
