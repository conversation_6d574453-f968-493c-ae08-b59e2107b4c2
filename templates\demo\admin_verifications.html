<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放管理系统 - 核查管理</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/unified-nav.css') }}">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background: linear-gradient(to right, #1B5E20, #388E3C);
            color: white;
            padding: 20px 0;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-name {
            margin-right: 15px;
        }
        .logout-btn {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 30px;
            cursor: pointer;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .logout-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        nav {
            background: linear-gradient(to right, #2E7D32, #43A047);
            padding: 10px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
        }
        .nav-item {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            margin-right: 5px;
            border-radius: 30px;
            transition: all 0.3s ease;
        }
        .nav-item:hover, .nav-item.active {
            background: linear-gradient(to right, #1B5E20, #2E7D32);
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        .card {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            transform: translateY(-5px);
        }
        .card-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        th, td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        th {
            background: linear-gradient(to right, #43A047, #66BB6A);
            color: white;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 14px;
            letter-spacing: 0.5px;
        }
        tr:last-child td {
            border-bottom: none;
        }
        tr:nth-child(even) {
            background-color: rgba(0,0,0,0.02);
        }
        tr:hover {
            background-color: rgba(76, 175, 80, 0.05);
        }
        .status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            display: inline-block;
        }
        .status-pending {
            background: linear-gradient(to right, #f39c12, #e67e22);
            color: white;
        }
        .status-verified {
            background: linear-gradient(to right, #2ecc71, #27ae60);
            color: white;
        }
        .status-rejected {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        .btn-primary {
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            color: white;
        }
        .chart-container {
            height: 300px;
            margin-top: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo">碳排放管理系统</div>
            <div class="user-info">
                <span class="user-name">管理员</span>
                <a href="/login" class="logout-btn">退出登录</a>
            </div>
        </div>
    </header>

    <div class="demo-nav">
        <div class="nav-content">
            <a href="/admin" class="nav-item"><i class="fas fa-tachometer-alt mr-2"></i>系统概览</a>
            <a href="/admin_users" class="nav-item"><i class="fas fa-users mr-2"></i>用户管理</a>
            <a href="/admin_quotas" class="nav-item"><i class="fas fa-chart-pie mr-2"></i>配额管理</a>
            <a href="/admin_verifications" class="nav-item active"><i class="fas fa-clipboard-check mr-2"></i>核查管理</a>
            <a href="/admin_transactions" class="nav-item"><i class="fas fa-exchange-alt mr-2"></i>交易管理</a>
            <a href="/admin_reports" class="nav-item"><i class="fas fa-file-alt mr-2"></i>系统报告</a>
            <a href="/admin_settings" class="nav-item"><i class="fas fa-cog mr-2"></i>系统配置</a>
            <a href="/admin_logs" class="nav-item"><i class="fas fa-list-alt mr-2"></i>日志查看</a>
            <a href="/admin_blockchain_config" class="nav-item"><i class="fas fa-link mr-2"></i>区块链配置</a>
        </div>
    </div>

    <div class="container">
        <h1>核查管理</h1>

        <div class="card">
            <div class="card-title">核查统计</div>
            <div class="chart-container">
                <canvas id="verificationChart"></canvas>
            </div>
        </div>

        <div class="card">
            <div class="card-title">最近核查记录</div>
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>企业</th>
                        <th>核查机构</th>
                        <th>排放源</th>
                        <th>排放量</th>
                        <th>核查结果</th>
                        <th>核查时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1001</td>
                        <td>北京碳排放科技有限公司</td>
                        <td>国家碳排放核查中心</td>
                        <td>燃煤锅炉</td>
                        <td>450 吨CO2e</td>
                        <td><span class="status status-verified">已通过</span></td>
                        <td>2023-04-15</td>
                        <td><a href="/verification_detail?id=1001" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>1002</td>
                        <td>上海绿色能源有限公司</td>
                        <td>国家碳排放核查中心</td>
                        <td>天然气锅炉</td>
                        <td>320 吨CO2e</td>
                        <td><span class="status status-pending">审核中</span></td>
                        <td>2023-04-20</td>
                        <td><a href="/verification_detail?id=1002" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>1003</td>
                        <td>广州环保科技有限公司</td>
                        <td>华南碳排放核查中心</td>
                        <td>工业生产过程</td>
                        <td>780 吨CO2e</td>
                        <td><span class="status status-rejected">已拒绝</span></td>
                        <td>2023-04-25</td>
                        <td><a href="/verification_detail?id=1003" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>1004</td>
                        <td>深圳新能源有限公司</td>
                        <td>华南碳排放核查中心</td>
                        <td>电力消耗</td>
                        <td>520 吨CO2e</td>
                        <td><span class="status status-verified">已通过</span></td>
                        <td>2023-05-01</td>
                        <td><a href="/verification_detail?id=1004" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>1005</td>
                        <td>重庆工业集团</td>
                        <td>西南碳排放核查中心</td>
                        <td>交通运输</td>
                        <td>380 吨CO2e</td>
                        <td><span class="status status-pending">审核中</span></td>
                        <td>2023-05-05</td>
                        <td><a href="/verification_detail?id=1005" class="btn btn-primary">查看</a></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 导航切换
            const currentPath = window.location.pathname;
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                if (item.getAttribute('href') === currentPath) {
                    navItems.forEach(i => i.classList.remove('active'));
                    item.classList.add('active');
                }
            });

            // 核查统计图表
            const ctx = document.getElementById('verificationChart').getContext('2d');
            const verificationChart = new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: ['已通过', '审核中', '已拒绝'],
                    datasets: [{
                        data: [65, 25, 10],
                        backgroundColor: [
                            'rgba(46, 204, 113, 0.8)',
                            'rgba(243, 156, 18, 0.8)',
                            'rgba(231, 76, 60, 0.8)'
                        ],
                        borderColor: [
                            'rgba(46, 204, 113, 1)',
                            'rgba(243, 156, 18, 1)',
                            'rgba(231, 76, 60, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                        },
                        title: {
                            display: true,
                            text: '核查结果分布'
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>
