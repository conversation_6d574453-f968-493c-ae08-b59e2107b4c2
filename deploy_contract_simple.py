"""
简化版智能合约部署脚本
用于将CarbonEmission.sol合约部署到Ganache本地区块链
"""

import os
import json
from web3 import Web3
from web3.middleware import geth_poa_middleware
from eth_account import Account
from dotenv import load_dotenv, set_key

# 加载环境变量
load_dotenv()

# 获取环境变量
ETHEREUM_NODE_URL = os.getenv('ETHEREUM_NODE_URL', 'http://127.0.0.1:8545')
ADMIN_PRIVATE_KEY = os.getenv('ADMIN_PRIVATE_KEY')

# 合约ABI和字节码
CONTRACT_ABI_PATH = 'blockchain/contracts/artifacts/CarbonEmission_metadata.json'
CONTRACT_BIN_PATH = 'blockchain/contracts/artifacts/CarbonEmission.bin'

def deploy_contract():
    """部署智能合约"""
    print(f"连接到以太坊节点: {ETHEREUM_NODE_URL}")
    
    # 连接到以太坊节点
    w3 = Web3(Web3.HTTPProvider(ETHEREUM_NODE_URL))
    
    # 如果使用的是PoA共识的网络，需要添加这个中间件
    w3.middleware_onion.inject(geth_poa_middleware, layer=0)
    
    # 检查连接
    if not w3.is_connected():
        print("无法连接到以太坊节点")
        return
    
    print(f"成功连接到以太坊节点，当前区块号: {w3.eth.block_number}")
    
    # 创建账户
    if not ADMIN_PRIVATE_KEY:
        print("错误: 未提供部署账户私钥，请在.env文件中设置ADMIN_PRIVATE_KEY")
        return
    
    account = Account.from_key(ADMIN_PRIVATE_KEY)
    address = account.address
    
    print(f"使用账户地址: {address}")
    
    # 加载合约ABI
    try:
        with open(CONTRACT_ABI_PATH, 'r') as f:
            contract_metadata = json.load(f)
            abi = contract_metadata['output']['abi']
    except Exception as e:
        print(f"加载合约ABI失败: {str(e)}")
        return
    
    # 加载合约字节码
    try:
        with open(CONTRACT_BIN_PATH, 'r') as f:
            bytecode = f.read().strip()
    except Exception as e:
        print(f"加载合约字节码失败: {str(e)}")
        return
    
    # 获取nonce
    nonce = w3.eth.get_transaction_count(address)
    
    # 创建合约
    contract = w3.eth.contract(
        abi=abi,
        bytecode=bytecode
    )
    
    # 构建部署交易
    transaction = contract.constructor().build_transaction({
        'from': address,
        'nonce': nonce,
        'gas': 5000000,
        'gasPrice': w3.eth.gas_price
    })
    
    # 签名交易
    signed_txn = w3.eth.account.sign_transaction(transaction, private_key=ADMIN_PRIVATE_KEY)
    
    # 发送交易
    print("发送部署交易...")
    try:
        tx_hash = w3.eth.send_raw_transaction(signed_txn.rawTransaction)
        print(f"交易已发送，交易哈希: {tx_hash.hex()}")
        
        # 等待交易被确认
        print("等待交易确认...")
        tx_receipt = w3.eth.wait_for_transaction_receipt(tx_hash)
        
        # 获取合约地址
        contract_address = tx_receipt.contractAddress
        
        print(f"合约已部署，地址: {contract_address}")
        print(f"交易哈希: {tx_hash.hex()}")
        print(f"区块号: {tx_receipt.blockNumber}")
        print(f"Gas使用量: {tx_receipt.gasUsed}")
        
        # 更新.env文件
        if os.path.exists('.env'):
            set_key('.env', 'CONTRACT_ADDRESS', contract_address)
            print(f".env文件已更新，CONTRACT_ADDRESS={contract_address}")
        else:
            print("警告: .env文件不存在，请手动创建并设置CONTRACT_ADDRESS")
        
        return contract_address
    except Exception as e:
        print(f"部署失败: {str(e)}")
        return None

if __name__ == '__main__':
    deploy_contract()
