<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放管理系统 - 注册</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .register-container {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 20px;
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
            padding: 40px;
            width: 500px;
            max-width: 90%;
            border: 1px solid rgba(0,0,0,0.05);
        }
        .register-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .register-title {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .register-subtitle {
            color: #7f8c8d;
            font-size: 14px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #2c3e50;
        }
        input, select {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 30px;
            font-size: 14px;
            box-sizing: border-box;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        input:focus, select:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 2px 10px rgba(76, 175, 80, 0.2);
        }
        .register-btn {
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            color: white;
            border: none;
            padding: 14px;
            border-radius: 30px;
            cursor: pointer;
            font-weight: bold;
            width: 100%;
            font-size: 16px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }
        .register-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 20px rgba(0, 0, 0, 0.3);
            background: linear-gradient(to right, #2E7D32, #4CAF50);
        }
        .login-link {
            text-align: center;
            margin-top: 20px;
            font-size: 14px;
        }
        .login-link a {
            color: #4CAF50;
            text-decoration: none;
            font-weight: bold;
        }
        .login-link a:hover {
            text-decoration: underline;
        }
        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }
        .form-col {
            flex: 1;
        }
        .role-selector {
            margin-bottom: 20px;
        }
        .role-option {
            display: flex;
            align-items: center;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 15px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .role-option:hover {
            background: linear-gradient(to right, #f8f9fa, #f1f3f5);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .role-option.selected {
            border-color: #4CAF50;
            background: linear-gradient(to right, #e8f5e9, #c8e6c9);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.2);
        }
        .role-option input {
            margin-right: 10px;
            width: auto;
        }
        .role-details {
            flex: 1;
        }
        .role-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .role-description {
            font-size: 12px;
            color: #7f8c8d;
        }
        .company-info {
            display: none;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        .company-info.show {
            display: block;
        }
        .required::after {
            content: "*";
            color: #e74c3c;
            margin-left: 3px;
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-header">
            <div style="text-align: center; margin-bottom: 20px;">
                <svg width="80" height="80" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20Z" fill="#4CAF50"/>
                    <path d="M12 7C9.24 7 7 9.24 7 12C7 14.76 9.24 17 12 17C14.76 17 17 14.76 17 12C17 9.24 14.76 7 12 7ZM12 15C10.35 15 9 13.65 9 12C9 10.35 10.35 9 12 9C13.65 9 15 10.35 15 12C15 13.65 13.65 15 12 15Z" fill="#4CAF50"/>
                    <path d="M12 10C10.9 10 10 10.9 10 12C10 13.1 10.9 14 12 14C13.1 14 14 13.1 14 12C14 10.9 13.1 10 12 10Z" fill="#4CAF50"/>
                </svg>
            </div>
            <div class="register-title">碳排放管理系统</div>
            <div class="register-subtitle">创建新账号</div>
        </div>

        <form id="register-form">
            <div class="form-row">
                <div class="form-col">
                    <div class="form-group">
                        <label for="username" class="required">用户名</label>
                        <input type="text" id="username" name="username" placeholder="请输入用户名" required>
                    </div>
                </div>
                <div class="form-col">
                    <div class="form-group">
                        <label for="email" class="required">电子邮箱</label>
                        <input type="email" id="email" name="email" placeholder="请输入电子邮箱" required>
                    </div>
                </div>
            </div>

            <div class="form-row">
                <div class="form-col">
                    <div class="form-group">
                        <label for="password" class="required">密码</label>
                        <input type="password" id="password" name="password" placeholder="请输入密码" required>
                    </div>
                </div>
                <div class="form-col">
                    <div class="form-group">
                        <label for="confirm-password" class="required">确认密码</label>
                        <input type="password" id="confirm-password" name="confirm_password" placeholder="请再次输入密码" required>
                    </div>
                </div>
            </div>

            <div class="role-selector">
                <label class="required">选择用户类型</label>

                <div class="role-option" data-role="enterprise">
                    <input type="radio" name="role" value="enterprise" id="role-enterprise">
                    <div style="margin-right: 15px; color: #4CAF50;">
                        <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 7V3H2V21H22V7H12ZM6 19H4V17H6V19ZM6 15H4V13H6V15ZM6 11H4V9H6V11ZM6 7H4V5H6V7ZM10 19H8V17H10V19ZM10 15H8V13H10V15ZM10 11H8V9H10V11ZM10 7H8V5H10V7ZM20 19H12V17H14V15H12V13H14V11H12V9H20V19ZM18 11H16V13H18V11ZM18 15H16V17H18V15Z" fill="currentColor"/>
                        </svg>
                    </div>
                    <div class="role-details">
                        <div class="role-title">企业用户</div>
                        <div class="role-description">适用于需要管理碳排放数据、参与碳交易的企业</div>
                    </div>
                </div>

                <div class="role-option" data-role="verifier">
                    <input type="radio" name="role" value="verifier" id="role-verifier">
                    <div style="margin-right: 15px; color: #4CAF50;">
                        <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1ZM19 11C19 15.52 16.02 19.69 12 20.93C7.98 19.69 5 15.52 5 11V6.3L12 3.19L19 6.3V11ZM7.41 11.59L6 13L10 17L18 9L16.59 7.58L10 14.17L7.41 11.59Z" fill="currentColor"/>
                        </svg>
                    </div>
                    <div class="role-details">
                        <div class="role-title">核查机构</div>
                        <div class="role-description">适用于负责核查企业碳排放数据的第三方机构</div>
                    </div>
                </div>
            </div>

            <div class="company-info" id="company-info">
                <div class="form-group">
                    <label for="company-name" class="required">公司名称</label>
                    <input type="text" id="company-name" name="company_name" placeholder="请输入公司名称">
                </div>

                <div class="form-group">
                    <label for="credit-code" class="required">统一社会信用代码</label>
                    <input type="text" id="credit-code" name="credit_code" placeholder="请输入统一社会信用代码">
                </div>

                <div class="form-group">
                    <label for="industry">所属行业</label>
                    <select id="industry" name="industry">
                        <option value="">请选择行业</option>
                        <option value="energy">能源生产</option>
                        <option value="manufacturing">制造业</option>
                        <option value="chemical">化工行业</option>
                        <option value="building">建筑业</option>
                        <option value="transportation">交通运输</option>
                        <option value="other">其他</option>
                    </select>
                </div>
            </div>

            <button type="submit" class="register-btn">注册</button>
        </form>

        <div class="login-link">
            已有账号？<a href="/login">立即登录</a>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 角色选择
            const roleOptions = document.querySelectorAll('.role-option');
            const companyInfo = document.getElementById('company-info');

            roleOptions.forEach(option => {
                option.addEventListener('click', function() {
                    // 更新选中状态
                    roleOptions.forEach(opt => opt.classList.remove('selected'));
                    this.classList.add('selected');

                    // 选中对应的单选按钮
                    const radio = this.querySelector('input[type="radio"]');
                    radio.checked = true;

                    // 显示或隐藏公司信息
                    if (radio.value === 'enterprise' || radio.value === 'verifier') {
                        companyInfo.classList.add('show');
                    } else {
                        companyInfo.classList.remove('show');
                    }
                });
            });

            // 表单提交
            const registerForm = document.getElementById('register-form');
            registerForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // 验证密码
                const password = document.getElementById('password').value;
                const confirmPassword = document.getElementById('confirm-password').value;

                if (password !== confirmPassword) {
                    alert('两次输入的密码不一致');
                    return;
                }

                // 验证角色选择
                const roleSelected = document.querySelector('input[name="role"]:checked');
                if (!roleSelected) {
                    alert('请选择用户类型');
                    return;
                }

                // 验证公司信息
                if (roleSelected.value === 'enterprise' || roleSelected.value === 'verifier') {
                    const companyName = document.getElementById('company-name').value;
                    const creditCode = document.getElementById('credit-code').value;

                    if (!companyName || !creditCode) {
                        alert('请填写完整的公司信息');
                        return;
                    }
                }

                // 注册成功，跳转到登录页面
                alert('注册成功！请使用新账号登录。');
                window.location.href = '/login';
            });
        });
    </script>
</body>
</html>
