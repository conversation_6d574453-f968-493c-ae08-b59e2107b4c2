"""
碳排放预测分析工具
提供基于历史数据的碳排放预测功能
"""

import os
import numpy as np
import pandas as pd
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score
import joblib
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EmissionPredictor:
    """碳排放预测模型"""
    
    def __init__(self, model_path=None):
        """初始化预测模型
        
        Args:
            model_path: 模型文件路径，如果提供则加载已有模型
        """
        self.model = None
        self.scaler = StandardScaler()
        self.feature_names = None
        
        if model_path and os.path.exists(model_path):
            self.load_model(model_path)
            logger.info(f"从 {model_path} 加载模型成功")
    
    def preprocess_data(self, data):
        """预处理数据
        
        Args:
            data: 排放数据列表
            
        Returns:
            处理后的DataFrame
        """
        # 将数据转换为DataFrame
        df = pd.DataFrame(data)
        
        # 特征工程
        if 'emission_period_start' in df.columns:
            df['emission_period_start'] = pd.to_datetime(df['emission_period_start'])
            df['month'] = df['emission_period_start'].dt.month
            df['year'] = df['emission_period_start'].dt.year
            df['quarter'] = df['emission_period_start'].dt.quarter
            df['day_of_year'] = df['emission_period_start'].dt.dayofyear
        
        # 处理分类特征
        if 'emission_source' in df.columns:
            df = pd.get_dummies(df, columns=['emission_source'], drop_first=True)
        
        if 'calculation_method' in df.columns:
            df = pd.get_dummies(df, columns=['calculation_method'], drop_first=True)
        
        # 删除不需要的列
        columns_to_drop = ['id', 'enterprise_id', 'enterprise_name', 'emission_period_end', 
                          'status', 'submission_time', 'blockchain_hash', 'blockchain_block',
                          'proof_file_path']
        for col in columns_to_drop:
            if col in df.columns:
                df = df.drop(col, axis=1)
        
        return df
    
    def train(self, data, target_column='emission_amount', test_size=0.2, random_state=42):
        """训练预测模型
        
        Args:
            data: 排放数据列表
            target_column: 目标列名
            test_size: 测试集比例
            random_state: 随机种子
            
        Returns:
            训练结果指标
        """
        logger.info("开始训练模型")
        
        # 预处理数据
        df = self.preprocess_data(data)
        
        if target_column not in df.columns:
            raise ValueError(f"目标列 {target_column} 不存在")
        
        # 分离特征和目标
        X = df.drop(target_column, axis=1)
        y = df[target_column]
        
        # 保存特征名称
        self.feature_names = X.columns.tolist()
        
        # 标准化特征
        X_scaled = self.scaler.fit_transform(X)
        
        # 分割训练集和测试集
        X_train, X_test, y_train, y_test = train_test_split(X_scaled, y, test_size=test_size, random_state=random_state)
        
        # 训练模型
        self.model = RandomForestRegressor(n_estimators=100, random_state=random_state)
        self.model.fit(X_train, y_train)
        
        # 评估模型
        y_pred = self.model.predict(X_test)
        mse = mean_squared_error(y_test, y_pred)
        rmse = np.sqrt(mse)
        r2 = r2_score(y_test, y_pred)
        
        # 特征重要性
        feature_importance = dict(zip(X.columns, self.model.feature_importances_))
        sorted_importance = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)
        
        logger.info(f"模型训练完成，RMSE: {rmse:.4f}, R²: {r2:.4f}")
        
        return {
            'mse': mse,
            'rmse': rmse,
            'r2': r2,
            'feature_importance': feature_importance,
            'top_features': sorted_importance[:5]
        }
    
    def predict(self, data):
        """使用模型进行预测
        
        Args:
            data: 待预测的数据
            
        Returns:
            预测结果
        """
        if self.model is None:
            raise ValueError("模型尚未训练")
        
        # 预处理数据
        df = self.preprocess_data(data)
        
        # 确保所有特征都存在
        for feature in self.feature_names:
            if feature not in df.columns:
                df[feature] = 0
        
        # 只保留模型需要的特征
        df = df[self.feature_names]
        
        # 标准化特征
        X_scaled = self.scaler.transform(df)
        
        # 进行预测
        predictions = self.model.predict(X_scaled)
        
        return predictions
    
    def predict_future(self, last_data, periods=6, freq='M'):
        """预测未来排放趋势
        
        Args:
            last_data: 最后一条历史数据
            periods: 预测期数
            freq: 频率，'M'表示月，'Q'表示季度，'Y'表示年
            
        Returns:
            未来预测数据
        """
        if self.model is None:
            raise ValueError("模型尚未训练")
        
        # 复制最后一条数据作为模板
        template = last_data.copy()
        
        # 创建未来日期序列
        if isinstance(template['emission_period_start'], str):
            last_date = pd.to_datetime(template['emission_period_start'])
        else:
            last_date = template['emission_period_start']
        
        future_dates = pd.date_range(start=last_date, periods=periods+1, freq=freq)[1:]
        
        # 为每个未来日期创建预测数据
        future_data = []
        for date in future_dates:
            future_item = template.copy()
            future_item['emission_period_start'] = date.strftime('%Y-%m-%d')
            future_data.append(future_item)
        
        # 进行预测
        predictions = self.predict(future_data)
        
        # 组合结果
        result = []
        for i, date in enumerate(future_dates):
            result.append({
                'date': date.strftime('%Y-%m'),
                'emission_amount': float(predictions[i]),
                'is_prediction': True
            })
        
        return result
    
    def save_model(self, path):
        """保存模型到文件
        
        Args:
            path: 保存路径
        """
        if self.model is None:
            raise ValueError("模型尚未训练")
        
        # 创建目录
        os.makedirs(os.path.dirname(path), exist_ok=True)
        
        model_data = {
            'model': self.model,
            'scaler': self.scaler,
            'feature_names': self.feature_names
        }
        
        joblib.dump(model_data, path)
        logger.info(f"模型已保存到 {path}")
    
    def load_model(self, path):
        """从文件加载模型
        
        Args:
            path: 模型文件路径
        """
        model_data = joblib.load(path)
        self.model = model_data['model']
        self.scaler = model_data['scaler']
        self.feature_names = model_data['feature_names']
