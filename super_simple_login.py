"""
超级简单的登录系统
"""

from flask import Flask, render_template_string, request, redirect, url_for, session

app = Flask(__name__)
app.secret_key = 'super-simple-login-secret-key'

# 测试用户
USERS = {
    'admin': {
        'password': 'admin123',
        'role': 'admin'
    },
    'enterprise1': {
        'password': 'enterprise123',
        'role': 'enterprise'
    },
    'verifier1': {
        'password': 'verifier123',
        'role': 'verifier'
    }
}

# 登录页面模板
LOGIN_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>超级简单登录系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 500px;
            margin: 0 auto;
            padding: 20px;
        }
        .error {
            color: red;
            margin-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input[type="text"],
        input[type="password"] {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        .test-accounts {
            margin-top: 20px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>超级简单登录系统</h1>
    
    {% if error %}
        <div class="error">{{ error }}</div>
    {% endif %}
    
    <form method="post">
        <div class="form-group">
            <label for="username">用户名</label>
            <input type="text" id="username" name="username" required>
        </div>
        <div class="form-group">
            <label for="password">密码</label>
            <input type="password" id="password" name="password" required>
        </div>
        <button type="submit">登录</button>
    </form>
    
    <div class="test-accounts">
        <h3>测试账户</h3>
        <p><strong>管理员:</strong> admin / admin123</p>
        <p><strong>企业:</strong> enterprise1 / enterprise123</p>
        <p><strong>核查机构:</strong> verifier1 / verifier123</p>
    </div>
</body>
</html>
"""

# 仪表板页面模板
DASHBOARD_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>{{ role }} 仪表板</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 500px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .logout {
            padding: 5px 10px;
            background-color: #f44336;
            color: white;
            text-decoration: none;
            border-radius: 3px;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ role }} 仪表板</h1>
        <a href="/logout" class="logout">退出登录</a>
    </div>
    
    <div class="card">
        <h2>欢迎, {{ username }}!</h2>
        <p>您的角色是: {{ role }}</p>
    </div>
    
    <div class="card">
        <h2>功能列表</h2>
        <ul>
            {% for feature in features %}
                <li>
                    <a href="{{ feature.url }}">{{ feature.name }}</a>
                </li>
            {% endfor %}
        </ul>
    </div>
</body>
</html>
"""

@app.route('/')
def index():
    """首页"""
    if 'username' in session:
        return redirect('/dashboard')
    return redirect('/login')

@app.route('/login', methods=['GET', 'POST'])
def login():
    """登录页面"""
    error = None
    
    if 'username' in session:
        return redirect('/dashboard')
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        print(f"尝试登录: 用户名={username}, 密码={'*' * len(password)}")
        
        if username in USERS and USERS[username]['password'] == password:
            # 登录成功
            session.clear()
            session['username'] = username
            session['role'] = USERS[username]['role']
            print(f"登录成功: 用户名={username}, 角色={USERS[username]['role']}")
            return redirect('/dashboard')
        else:
            # 登录失败
            error = "用户名或密码不正确"
            print(f"登录失败: 用户名={username}")
    
    return render_template_string(LOGIN_TEMPLATE, error=error)

@app.route('/dashboard')
def dashboard():
    """仪表板页面"""
    if 'username' not in session:
        return redirect('/login')
    
    username = session['username']
    role = session['role']
    
    # 根据角色设置功能列表
    features = []
    
    if role == 'admin':
        features = [
            {'name': '用户管理', 'url': '/admin/users'},
            {'name': '配额管理', 'url': '/admin/quotas'},
            {'name': '惩罚管理', 'url': '/admin/penalties'},
            {'name': '报告管理', 'url': '/admin/reports'},
            {'name': '区块链配置', 'url': '/admin/blockchain/config'}
        ]
    elif role == 'enterprise':
        features = [
            {'name': '排放管理', 'url': '/enterprise/emissions'},
            {'name': '交易管理', 'url': '/enterprise/transactions'},
            {'name': '报告管理', 'url': '/enterprise/reports'},
            {'name': '碳计算器', 'url': '/enterprise/calculator'}
        ]
    elif role == 'verifier':
        features = [
            {'name': '核查管理', 'url': '/verifier/verifications'},
            {'name': '报告管理', 'url': '/verifier/reports'},
            {'name': '企业管理', 'url': '/verifier/enterprises'}
        ]
    
    return render_template_string(DASHBOARD_TEMPLATE, username=username, role=role, features=features)

@app.route('/logout')
def logout():
    """退出登录"""
    session.clear()
    return redirect('/login')

# 模拟功能页面
@app.route('/<path:path>')
def feature_page(path):
    """功能页面"""
    if 'username' not in session:
        return redirect('/login')
    
    return f"""
    <h1>{path}</h1>
    <p>这是 {path} 页面</p>
    <p>用户: {session['username']}</p>
    <p>角色: {session['role']}</p>
    <a href="/dashboard">返回仪表板</a>
    <a href="/logout">退出登录</a>
    """

if __name__ == '__main__':
    app.run(debug=True, port=5002)
