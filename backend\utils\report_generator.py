"""
报告生成工具
"""

from datetime import datetime

class ReportGenerator:
    """报告生成工具"""
    
    @staticmethod
    def generate_emission_report(enterprise, emission_data, period_start, period_end):
        """
        生成排放报告
        
        Args:
            enterprise: 企业信息
            emission_data: 排放数据列表
            period_start: 报告期开始日期
            period_end: 报告期结束日期
            
        Returns:
            报告内容
        """
        # 计算总排放量
        total_emission = sum(data.emission_amount for data in emission_data)
        
        # 按排放源分类
        emission_by_source = {}
        for data in emission_data:
            source = data.emission_source
            if source not in emission_by_source:
                emission_by_source[source] = 0
            emission_by_source[source] += data.emission_amount
        
        # 生成报告内容
        report = {
            'title': f"{enterprise.company_name}碳排放报告",
            'period': f"{period_start.strftime('%Y-%m-%d')} 至 {period_end.strftime('%Y-%m-%d')}",
            'enterprise': {
                'name': enterprise.company_name,
                'credit_code': enterprise.credit_code
            },
            'summary': {
                'total_emission': total_emission,
                'emission_unit': emission_data[0].emission_unit if emission_data else 'tCO2e'
            },
            'details': {
                'by_source': emission_by_source,
                'data_count': len(emission_data),
                'verified_count': sum(1 for data in emission_data if data.status == 'verified')
            },
            'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        return report
    
    @staticmethod
    def generate_trading_report(enterprise, transactions, period_start, period_end):
        """
        生成交易报告
        
        Args:
            enterprise: 企业信息
            transactions: 交易数据列表
            period_start: 报告期开始日期
            period_end: 报告期结束日期
            
        Returns:
            报告内容
        """
        # 分离买入和卖出交易
        purchases = [t for t in transactions if t.buyer_id == enterprise.id]
        sales = [t for t in transactions if t.seller_id == enterprise.id]
        
        # 计算总量和总价
        total_purchase_amount = sum(t.amount for t in purchases)
        total_purchase_price = sum(t.total_price for t in purchases)
        total_sale_amount = sum(t.amount for t in sales)
        total_sale_price = sum(t.total_price for t in sales)
        
        # 计算平均价格
        avg_purchase_price = total_purchase_price / total_purchase_amount if total_purchase_amount > 0 else 0
        avg_sale_price = total_sale_price / total_sale_amount if total_sale_amount > 0 else 0
        
        # 生成报告内容
        report = {
            'title': f"{enterprise.company_name}碳交易报告",
            'period': f"{period_start.strftime('%Y-%m-%d')} 至 {period_end.strftime('%Y-%m-%d')}",
            'enterprise': {
                'name': enterprise.company_name,
                'credit_code': enterprise.credit_code
            },
            'summary': {
                'total_purchase_amount': total_purchase_amount,
                'total_purchase_price': total_purchase_price,
                'avg_purchase_price': avg_purchase_price,
                'total_sale_amount': total_sale_amount,
                'total_sale_price': total_sale_price,
                'avg_sale_price': avg_sale_price,
                'net_amount': total_purchase_amount - total_sale_amount,
                'net_price': total_purchase_price - total_sale_price
            },
            'details': {
                'purchase_count': len(purchases),
                'sale_count': len(sales),
                'completed_count': sum(1 for t in transactions if t.status == 'completed'),
                'pending_count': sum(1 for t in transactions if t.status == 'pending')
            },
            'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        return report
    
    @staticmethod
    def generate_compliance_report(enterprise, emission_data, quota, period_start, period_end):
        """
        生成合规报告
        
        Args:
            enterprise: 企业信息
            emission_data: 排放数据列表
            quota: 配额信息
            period_start: 报告期开始日期
            period_end: 报告期结束日期
            
        Returns:
            报告内容
        """
        # 计算总排放量
        total_emission = sum(data.emission_amount for data in emission_data)
        
        # 计算合规状态
        compliance_status = 'compliant' if total_emission <= quota.current_amount else 'non_compliant'
        
        # 计算差额
        difference = quota.current_amount - total_emission
        
        # 生成报告内容
        report = {
            'title': f"{enterprise.company_name}碳排放合规报告",
            'period': f"{period_start.strftime('%Y-%m-%d')} 至 {period_end.strftime('%Y-%m-%d')}",
            'enterprise': {
                'name': enterprise.company_name,
                'credit_code': enterprise.credit_code
            },
            'summary': {
                'total_emission': total_emission,
                'emission_unit': emission_data[0].emission_unit if emission_data else 'tCO2e',
                'quota_amount': quota.current_amount,
                'difference': difference,
                'compliance_status': compliance_status
            },
            'details': {
                'data_count': len(emission_data),
                'verified_count': sum(1 for data in emission_data if data.status == 'verified'),
                'initial_quota': quota.initial_amount,
                'quota_year': quota.year
            },
            'recommendations': [
                '继续减少碳排放' if difference > 0 else '需要购买额外的碳配额',
                '优化能源结构，提高能源利用效率',
                '加强碳资产管理，积极参与碳交易市场'
            ],
            'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        return report
