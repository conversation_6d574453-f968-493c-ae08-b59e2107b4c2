"""
自动化报告生成工具
提供各类碳排放报告的自动生成功能
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
from datetime import datetime, timedelta
import jinja2
import pdfkit
import logging
from io import BytesIO
import base64

# 配置中文字体支持
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
matplotlib.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ReportGenerator:
    """报告生成器"""
    
    def __init__(self, template_dir='templates/reports'):
        """初始化报告生成器
        
        Args:
            template_dir: 报告模板目录
        """
        self.template_dir = template_dir
        self.env = jinja2.Environment(
            loader=jinja2.FileSystemLoader(template_dir),
            autoescape=jinja2.select_autoescape(['html', 'xml'])
        )
    
    def _fig_to_base64(self, fig):
        """将matplotlib图表转换为base64编码
        
        Args:
            fig: matplotlib图表对象
            
        Returns:
            base64编码的图表
        """
        buf = BytesIO()
        fig.savefig(buf, format='png', dpi=300, bbox_inches='tight')
        buf.seek(0)
        img_str = base64.b64encode(buf.read()).decode('utf-8')
        buf.close()
        plt.close(fig)
        return img_str
    
    def _generate_emission_trend_chart(self, emission_data):
        """生成排放趋势图表
        
        Args:
            emission_data: 排放数据列表
            
        Returns:
            base64编码的图表
        """
        # 转换为DataFrame
        df = pd.DataFrame(emission_data)
        df['emission_period_start'] = pd.to_datetime(df['emission_period_start'])
        df = df.sort_values('emission_period_start')
        
        # 按月聚合
        monthly = df.groupby(pd.Grouper(key='emission_period_start', freq='M')).agg({
            'emission_amount': 'sum'
        }).reset_index()
        monthly['date'] = monthly['emission_period_start'].dt.strftime('%Y-%m')
        
        # 创建图表
        fig, ax = plt.subplots(figsize=(10, 6))
        ax.plot(monthly['date'], monthly['emission_amount'], marker='o', linestyle='-', linewidth=2)
        ax.set_title('月度碳排放趋势', fontsize=16)
        ax.set_xlabel('月份', fontsize=12)
        ax.set_ylabel('排放量 (吨CO₂e)', fontsize=12)
        ax.grid(True, linestyle='--', alpha=0.7)
        
        # 旋转x轴标签
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        return self._fig_to_base64(fig)
    
    def _generate_emission_source_chart(self, emission_data):
        """生成排放源占比图表
        
        Args:
            emission_data: 排放数据列表
            
        Returns:
            base64编码的图表
        """
        # 转换为DataFrame
        df = pd.DataFrame(emission_data)
        
        # 按排放源聚合
        source_summary = df.groupby('emission_source').agg({
            'emission_amount': 'sum'
        }).reset_index()
        
        # 计算占比
        total = source_summary['emission_amount'].sum()
        source_summary['percentage'] = source_summary['emission_amount'] / total * 100
        
        # 创建图表
        fig, ax = plt.subplots(figsize=(8, 8))
        wedges, texts, autotexts = ax.pie(
            source_summary['percentage'], 
            labels=source_summary['emission_source'],
            autopct='%1.1f%%',
            startangle=90,
            shadow=False
        )
        
        # 设置字体大小
        plt.setp(autotexts, size=10, weight='bold')
        plt.setp(texts, size=12)
        
        ax.set_title('碳排放源占比', fontsize=16)
        ax.axis('equal')  # 确保饼图是圆的
        
        return self._fig_to_base64(fig)
    
    def _generate_prediction_chart(self, historical_data, prediction_data):
        """生成预测趋势图表
        
        Args:
            historical_data: 历史数据列表
            prediction_data: 预测数据列表
            
        Returns:
            base64编码的图表
        """
        # 合并数据
        all_data = historical_data + prediction_data
        df = pd.DataFrame(all_data)
        
        # 创建图表
        fig, ax = plt.subplots(figsize=(12, 6))
        
        # 绘制历史数据
        historical_df = df[df['is_prediction'] == False]
        ax.plot(historical_df['date'], historical_df['emission_amount'], 
                marker='o', linestyle='-', linewidth=2, color='blue', label='历史数据')
        
        # 绘制预测数据
        prediction_df = df[df['is_prediction'] == True]
        ax.plot(prediction_df['date'], prediction_df['emission_amount'], 
                marker='s', linestyle='--', linewidth=2, color='red', label='预测数据')
        
        # 添加图表元素
        ax.set_title('碳排放趋势预测', fontsize=16)
        ax.set_xlabel('月份', fontsize=12)
        ax.set_ylabel('排放量 (吨CO₂e)', fontsize=12)
        ax.grid(True, linestyle='--', alpha=0.7)
        ax.legend(fontsize=12)
        
        # 旋转x轴标签
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        return self._fig_to_base64(fig)
    
    def generate_emission_report(self, enterprise, emission_data, period_start, period_end, prediction_data=None):
        """生成碳排放报告
        
        Args:
            enterprise: 企业信息
            emission_data: 排放数据列表
            period_start: 报告期开始日期
            period_end: 报告期结束日期
            prediction_data: 预测数据
            
        Returns:
            HTML格式的报告内容
        """
        logger.info(f"开始生成企业 {enterprise['company_name']} 的碳排放报告")
        
        # 准备数据
        df = pd.DataFrame(emission_data)
        
        # 计算总排放量
        total_emission = df['emission_amount'].sum()
        
        # 按排放源统计
        source_summary = df.groupby('emission_source').agg({
            'emission_amount': ['sum', 'mean', 'count']
        })
        source_summary.columns = ['total', 'average', 'count']
        source_summary = source_summary.reset_index()
        
        # 生成图表
        trend_chart = self._generate_emission_trend_chart(emission_data)
        source_chart = self._generate_emission_source_chart(emission_data)
        
        # 预测图表
        prediction_chart = None
        if prediction_data:
            # 提取最近12个月的历史数据
            recent_data = df.sort_values('emission_period_start').tail(12).to_dict('records')
            historical_data = [
                {
                    'date': pd.to_datetime(item['emission_period_start']).strftime('%Y-%m'),
                    'emission_amount': item['emission_amount'],
                    'is_prediction': False
                } for item in recent_data
            ]
            prediction_chart = self._generate_prediction_chart(historical_data, prediction_data)
        
        # 准备模板数据
        template_data = {
            'enterprise': enterprise,
            'period_start': period_start,
            'period_end': period_end,
            'report_date': datetime.now().strftime('%Y-%m-%d'),
            'total_emission': total_emission,
            'emission_data': emission_data,
            'source_summary': source_summary.to_dict('records'),
            'trend_chart': trend_chart,
            'source_chart': source_chart,
            'prediction_chart': prediction_chart,
            'has_prediction': prediction_data is not None
        }
        
        # 渲染模板
        template = self.env.get_template('emission_report.html')
        html_content = template.render(**template_data)
        
        logger.info(f"企业 {enterprise['company_name']} 的碳排放报告生成完成")
        
        return html_content
    
    def generate_pdf_report(self, html_content, output_path):
        """将HTML报告转换为PDF
        
        Args:
            html_content: HTML格式的报告内容
            output_path: PDF输出路径
            
        Returns:
            PDF文件路径
        """
        # 创建目录
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # 配置wkhtmltopdf选项
        options = {
            'page-size': 'A4',
            'margin-top': '1cm',
            'margin-right': '1cm',
            'margin-bottom': '1cm',
            'margin-left': '1cm',
            'encoding': 'UTF-8',
            'no-outline': None,
            'enable-local-file-access': None
        }
        
        # 转换为PDF
        pdfkit.from_string(html_content, output_path, options=options)
        
        logger.info(f"PDF报告已保存到 {output_path}")
        
        return output_path
