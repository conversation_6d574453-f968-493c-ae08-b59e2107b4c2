"""
区块链配置模型
"""

from backend import db
from datetime import datetime

class BlockchainConfig(db.Model):
    """区块链配置模型"""
    __tablename__ = 'blockchain_config'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON><PERSON>('user.id'), nullable=False)
    ethereum_node_url = db.Column(db.String(255), nullable=False, default='http://127.0.0.1:8545')
    private_key = db.Column(db.String(255))
    blockchain_address = db.Column(db.String(255))
    contract_address = db.Column(db.String(255))
    simulation_mode = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    # 关联关系
    user = db.relationship('User', backref=db.backref('blockchain_config', lazy=True))
    
    def __init__(self, user_id, ethereum_node_url, private_key=None, blockchain_address=None, contract_address=None, simulation_mode=True):
        self.user_id = user_id
        self.ethereum_node_url = ethereum_node_url
        self.private_key = private_key
        self.blockchain_address = blockchain_address
        self.contract_address = contract_address
        self.simulation_mode = simulation_mode
    
    def __repr__(self):
        return f'<BlockchainConfig {self.id}: {self.user_id}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'ethereum_node_url': self.ethereum_node_url,
            'blockchain_address': self.blockchain_address,
            'contract_address': self.contract_address,
            'simulation_mode': self.simulation_mode,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S')
        }
