{% extends 'base.html' %}

{% block title %}碳排放核查系统 - 区块链配置（演示）{% endblock %}

{% block head %}
<style>
    .alert {
        padding: 15px;
        border-radius: 10px;
        margin-bottom: 20px;
        box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    }
    .alert-info {
        background: linear-gradient(to right, #d1ecf1, #bee5eb);
        border-left: 5px solid #0c5460;
        color: #0c5460;
    }
    .status-box {
        background: linear-gradient(to right, #f1f8e9, #dcedc8);
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
        box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    }
    .status-item {
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }
    .status-item:last-child {
        border-bottom: none;
    }
    .status-label {
        font-weight: bold;
        color: #2c3e50;
    }
    .status-value {
        color: #27ae60;
    }
    .status-value.error {
        color: #e74c3c;
    }
</style>
{% endblock %}

{% block content %}

    <div class="container">
        <h1>区块链配置</h1>

        <div class="alert alert-info">
            <div style="display: flex; align-items: center;">
                <i class="fas fa-info-circle fa-2x" style="margin-right: 15px;"></i>
                <div>
                    <h4 style="margin-top: 0;">区块链配置说明</h4>
                    <p>本页面用于配置区块链节点、部署智能合约，以及为不同角色分配区块链账户。请按照以下步骤操作：</p>
                    <ol>
                        <li>确保 Ganache 已启动并运行在 <code>http://127.0.0.1:8545</code>（默认地址）</li>
                        <li>点击"获取Ganache账户"按钮，获取可用的账户列表</li>
                        <li>将账户地址和私钥填入相应的表单字段</li>
                        <li>点击"保存配置"按钮，保存配置信息</li>
                        <li>点击"部署智能合约"按钮，部署智能合约</li>
                    </ol>
                    <p style="margin-bottom: 0;">完成以上步骤后，系统将能够与区块链进行交互，实现碳排放数据的上链和核查。</p>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-title">区块链状态</div>
            <div class="status-box">
                <div class="status-item">
                    <span class="status-label">连接状态:</span>
                    <span class="status-value">已连接</span>
                </div>
                <div class="status-item">
                    <span class="status-label">网络ID:</span>
                    <span class="status-value">1337 (Ganache)</span>
                </div>
                <div class="status-item">
                    <span class="status-label">当前区块:</span>
                    <span class="status-value">12345</span>
                </div>
                <div class="status-item">
                    <span class="status-label">智能合约:</span>
                    <span class="status-value">已部署 (0x1234...5678)</span>
                </div>
                <div class="status-item">
                    <span class="status-label">管理员地址:</span>
                    <span class="status-value">0xABCD...EF01</span>
                </div>
            </div>
            <button class="btn btn-primary" id="testConnectionBtn">测试连接</button>
        </div>

        <div class="card">
            <div class="card-title">以太坊节点配置</div>
            <form id="blockchainConfigForm">
                <div class="form-group">
                    <label for="ethereum_node_url">以太坊节点URL</label>
                    <input type="text" id="ethereum_node_url" name="ethereum_node_url" value="http://127.0.0.1:8545" required>
                    <small>例如: http://127.0.0.1:8545 (本地Ganache)</small>
                </div>
                <div class="form-group">
                    <label for="contract_address">智能合约地址</label>
                    <input type="text" id="contract_address" name="contract_address" value="******************************************">
                    <small>如果留空，系统将尝试部署新的合约</small>
                </div>
                <div class="form-group">
                    <label for="admin_address">管理员地址</label>
                    <input type="text" id="admin_address" name="admin_address" value="******************************************" required>
                </div>
                <div class="form-group">
                    <label for="admin_private_key">管理员私钥</label>
                    <input type="password" id="admin_private_key" name="admin_private_key" value="0x0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef" required>
                    <small>注意: 私钥仅用于签名交易，不会被发送到区块链网络</small>
                </div>
                <div style="display: flex; gap: 10px;">
                    <button type="button" class="btn btn-primary" id="saveConfigBtn">保存配置</button>
                    <button type="button" class="btn btn-success" id="deployContractBtn">部署智能合约</button>
                    <button type="button" class="btn btn-primary" id="getAccountsBtn">获取Ganache账户</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 测试连接按钮点击事件
            document.getElementById('testConnectionBtn').addEventListener('click', function() {
                alert('连接成功!\n网络ID: 1337\n当前区块: 12345');
            });

            // 保存配置按钮点击事件
            document.getElementById('saveConfigBtn').addEventListener('click', function() {
                alert('配置已保存');
            });

            // 部署智能合约按钮点击事件
            document.getElementById('deployContractBtn').addEventListener('click', function() {
                alert('智能合约已成功部署\n合约地址: ******************************************');
            });

            // 获取Ganache账户按钮点击事件
            document.getElementById('getAccountsBtn').addEventListener('click', function() {
                alert('已获取10个Ganache账户');
            });
        });
    </script>
{% endblock %}
