<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放管理系统 - 用户管理</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/unified-nav.css') }}">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background: linear-gradient(to right, #1B5E20, #388E3C);
            color: white;
            padding: 20px 0;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-name {
            margin-right: 15px;
        }
        .logout-btn {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 30px;
            cursor: pointer;
            text-decoration: none;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .logout-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        nav {
            background: linear-gradient(to right, #2E7D32, #43A047);
            padding: 10px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
        }
        .nav-item {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            margin-right: 5px;
            border-radius: 30px;
            transition: all 0.3s ease;
        }
        .nav-item:hover, .nav-item.active {
            background: linear-gradient(to right, #1B5E20, #2E7D32);
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        .card {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            transform: translateY(-5px);
        }
        .card-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        .btn-primary {
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            color: white;
        }
        .btn-success {
            background: linear-gradient(to right, #2ecc71, #27ae60);
            color: white;
        }
        .btn-danger {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
        }
        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        th, td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        th {
            background: linear-gradient(to right, #43A047, #66BB6A);
            color: white;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 14px;
            letter-spacing: 0.5px;
        }
        tr:last-child td {
            border-bottom: none;
        }
        tr:nth-child(even) {
            background-color: rgba(0,0,0,0.02);
        }
        tr:hover {
            background-color: rgba(76, 175, 80, 0.05);
        }
        .status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            color: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            display: inline-block;
        }
        .status-active {
            background: linear-gradient(to right, #2ecc71, #27ae60);
        }
        .status-inactive {
            background: linear-gradient(to right, #e74c3c, #c0392b);
        }
        .filters {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }
        .filter-group {
            display: flex;
            align-items: center;
        }
        .filter-group label {
            margin-right: 5px;
            font-weight: bold;
        }
        .filter-group select {
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 30px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .filter-group select:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 2px 10px rgba(76, 175, 80, 0.2);
        }
        .user-stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 25px;
            text-align: center;
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        .stat-value {
            font-size: 36px;
            font-weight: bold;
            margin: 15px 0;
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .stat-label {
            color: #7f8c8d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo">碳排放管理系统</div>
            <div class="user-info">
                <span class="user-name">管理员</span>
                <a href="/login" class="logout-btn">退出登录</a>
            </div>
        </div>
    </header>

    <div class="demo-nav">
        <div class="nav-content">
            <a href="/admin" class="nav-item"><i class="fas fa-tachometer-alt mr-2"></i>系统概览</a>
            <a href="/admin_users" class="nav-item active"><i class="fas fa-users mr-2"></i>用户管理</a>
            <a href="/admin_quotas" class="nav-item"><i class="fas fa-chart-pie mr-2"></i>配额管理</a>
            <a href="/admin_verifications" class="nav-item"><i class="fas fa-clipboard-check mr-2"></i>核查管理</a>
            <a href="/admin_transactions" class="nav-item"><i class="fas fa-exchange-alt mr-2"></i>交易管理</a>
            <a href="/admin_reports" class="nav-item"><i class="fas fa-file-alt mr-2"></i>系统报告</a>
            <a href="/admin_settings" class="nav-item"><i class="fas fa-cog mr-2"></i>系统配置</a>
            <a href="/admin_logs" class="nav-item"><i class="fas fa-list-alt mr-2"></i>日志查看</a>
            <a href="/admin_blockchain_config" class="nav-item"><i class="fas fa-link mr-2"></i>区块链配置</a>
        </div>
    </div>

    <div class="container">
        <h1>用户管理</h1>

        <div class="user-stats">
            <div class="stat-card">
                <div class="stat-label">总用户数</div>
                <div class="stat-value">21</div>
                <div class="stat-label">个</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">企业用户</div>
                <div class="stat-value">15</div>
                <div class="stat-label">个</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">核查机构</div>
                <div class="stat-value">5</div>
                <div class="stat-label">个</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">管理员</div>
                <div class="stat-value">1</div>
                <div class="stat-label">个</div>
            </div>
        </div>

        <div class="filters">
            <div class="filter-group">
                <label for="role-filter">角色:</label>
                <select id="role-filter">
                    <option value="all">全部</option>
                    <option value="admin">管理员</option>
                    <option value="enterprise">企业</option>
                    <option value="verifier">核查机构</option>
                </select>
            </div>
            <div class="filter-group">
                <label for="status-filter">状态:</label>
                <select id="status-filter">
                    <option value="all">全部</option>
                    <option value="active">活跃</option>
                    <option value="inactive">非活跃</option>
                </select>
            </div>
        </div>

        <div class="card">
            <div class="card-title">
                <span>用户列表</span>
                <a href="#" class="btn btn-primary">添加用户</a>
            </div>
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>用户名</th>
                        <th>角色</th>
                        <th>公司名称</th>
                        <th>注册时间</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>admin</td>
                        <td>管理员</td>
                        <td>系统管理</td>
                        <td>2023-01-01</td>
                        <td><span class="status status-active">活跃</span></td>
                        <td><a href="/admin_user_detail?id=1" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>enterprise1</td>
                        <td>企业</td>
                        <td>北京碳排放科技有限公司</td>
                        <td>2023-01-15</td>
                        <td><span class="status status-active">活跃</span></td>
                        <td><a href="/admin_user_detail?id=2" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td>enterprise2</td>
                        <td>企业</td>
                        <td>上海绿色能源有限公司</td>
                        <td>2023-01-20</td>
                        <td><span class="status status-active">活跃</span></td>
                        <td><a href="/admin_user_detail?id=3" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>4</td>
                        <td>verifier1</td>
                        <td>核查机构</td>
                        <td>国家碳排放核查中心</td>
                        <td>2023-02-01</td>
                        <td><span class="status status-active">活跃</span></td>
                        <td><a href="/admin_user_detail?id=4" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>5</td>
                        <td>verifier2</td>
                        <td>核查机构</td>
                        <td>碳核查认证机构</td>
                        <td>2023-02-10</td>
                        <td><span class="status status-active">活跃</span></td>
                        <td><a href="/admin_user_detail?id=5" class="btn btn-primary">查看</a></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 导航切换 - 不需要额外的事件监听器，因为我们使用的是链接
            // 根据当前URL设置活动导航项
            const currentPath = window.location.pathname;
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                if (item.getAttribute('href') === currentPath) {
                    navItems.forEach(i => i.classList.remove('active'));
                    item.classList.add('active');
                }
            });

            // 筛选功能
            const roleFilter = document.getElementById('role-filter');
            const statusFilter = document.getElementById('status-filter');

            // 这里只是演示，实际应用中应该根据筛选条件过滤数据
            roleFilter.addEventListener('change', function() {
                console.log('角色筛选:', this.value);
            });

            statusFilter.addEventListener('change', function() {
                console.log('状态筛选:', this.value);
            });

            // 添加用户分布图表
            const chartContainer = document.createElement('div');
            chartContainer.style.height = '300px';
            chartContainer.style.marginBottom = '30px';
            chartContainer.innerHTML = '<canvas id="userDistributionChart"></canvas>';

            // 将图表插入到用户统计下方
            const userStats = document.querySelector('.user-stats');
            userStats.parentNode.insertBefore(chartContainer, userStats.nextSibling);

            // 创建图表
            const ctx = document.getElementById('userDistributionChart').getContext('2d');
            new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: ['企业用户', '核查机构', '管理员'],
                    datasets: [{
                        data: [15, 5, 1],
                        backgroundColor: [
                            'rgba(46, 204, 113, 0.8)',
                            'rgba(52, 152, 219, 0.8)',
                            'rgba(155, 89, 182, 0.8)'
                        ],
                        borderColor: [
                            'rgba(46, 204, 113, 1)',
                            'rgba(52, 152, 219, 1)',
                            'rgba(155, 89, 182, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                        },
                        title: {
                            display: true,
                            text: '用户角色分布',
                            font: {
                                size: 16
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.chart.data.datasets[0].data.reduce(
                                        (sum, value) => sum + value, 0
                                    );
                                    const percentage = Math.round((value * 100) / total);
                                    return `${label}: ${value} 个 (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>
