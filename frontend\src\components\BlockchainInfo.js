import React, { useState } from 'react';
import '../styles/BlockchainInfo.css';

/**
 * 区块链信息展示组件
 * 用于展示区块链交易信息
 */
const BlockchainInfo = ({ transactionHash, blockNumber, timestamp, status, type }) => {
  const [expanded, setExpanded] = useState(false);

  // 根据交易类型设置图标和颜色
  const getTypeIcon = () => {
    switch (type) {
      case 'emission':
        return { icon: 'fa-leaf', color: 'green', label: '排放数据' };
      case 'verification':
        return { icon: 'fa-check-circle', color: 'blue', label: '核查结果' };
      case 'transaction':
        return { icon: 'fa-exchange-alt', color: 'orange', label: '碳交易' };
      case 'penalty':
        return { icon: 'fa-exclamation-triangle', color: 'red', label: '惩罚记录' };
      default:
        return { icon: 'fa-file-contract', color: 'gray', label: '区块链记录' };
    }
  };

  const typeInfo = getTypeIcon();
  
  // 格式化时间戳
  const formatTimestamp = (timestamp) => {
    if (!timestamp) return '未知时间';
    
    try {
      const date = new Date(timestamp);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch (e) {
      return '时间格式错误';
    }
  };

  // 截断交易哈希
  const truncateHash = (hash) => {
    if (!hash) return '未知哈希';
    return `${hash.substring(0, 10)}...${hash.substring(hash.length - 8)}`;
  };

  // 获取状态标签
  const getStatusLabel = () => {
    switch (status) {
      case 'confirmed':
        return { label: '已确认', color: 'green' };
      case 'pending':
        return { label: '处理中', color: 'orange' };
      case 'failed':
        return { label: '失败', color: 'red' };
      default:
        return { label: '未知', color: 'gray' };
    }
  };

  const statusInfo = getStatusLabel();

  // 打开区块浏览器
  const openExplorer = () => {
    // 这里应该根据实际使用的区块链网络选择合适的区块浏览器
    // 例如以太坊主网使用 Etherscan，测试网使用对应的测试网浏览器
    const explorerUrl = `https://etherscan.io/tx/${transactionHash}`;
    window.open(explorerUrl, '_blank');
  };

  return (
    <div className="blockchain-info">
      <div className="blockchain-info-header" onClick={() => setExpanded(!expanded)}>
        <div className="blockchain-info-type">
          <i className={`fas ${typeInfo.icon}`} style={{ color: typeInfo.color }}></i>
          <span>{typeInfo.label}</span>
        </div>
        <div className="blockchain-info-hash">
          <span>{truncateHash(transactionHash)}</span>
        </div>
        <div className="blockchain-info-status">
          <span style={{ color: statusInfo.color }}>{statusInfo.label}</span>
        </div>
        <div className="blockchain-info-toggle">
          <i className={`fas ${expanded ? 'fa-chevron-up' : 'fa-chevron-down'}`}></i>
        </div>
      </div>
      
      {expanded && (
        <div className="blockchain-info-details">
          <div className="blockchain-info-detail-row">
            <span className="detail-label">交易哈希:</span>
            <span className="detail-value">{transactionHash}</span>
          </div>
          <div className="blockchain-info-detail-row">
            <span className="detail-label">区块号:</span>
            <span className="detail-value">{blockNumber || '待确认'}</span>
          </div>
          <div className="blockchain-info-detail-row">
            <span className="detail-label">时间戳:</span>
            <span className="detail-value">{formatTimestamp(timestamp)}</span>
          </div>
          <div className="blockchain-info-actions">
            <button className="btn-explorer" onClick={openExplorer}>
              <i className="fas fa-external-link-alt"></i> 在区块浏览器中查看
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default BlockchainInfo;
