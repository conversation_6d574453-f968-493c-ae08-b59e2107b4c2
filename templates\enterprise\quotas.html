<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放核查系统 - 配额管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <style>
        .gradient-custom {
            background: linear-gradient(135deg, #43a047 0%, #1de9b6 100%);
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            border-radius: 15px 15px 0 0;
            background: linear-gradient(135deg, #43a047 0%, #1de9b6 100%);
            color: white;
        }
        .btn-success {
            background: linear-gradient(135deg, #43a047 0%, #1de9b6 100%);
            border: none;
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #388e3c 0%, #00bfa5 100%);
        }
        .progress {
            height: 20px;
            border-radius: 10px;
        }
        .progress-bar {
            background: linear-gradient(135deg, #43a047 0%, #1de9b6 100%);
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container">
            <a class="navbar-brand" href="/"><i class="fas fa-leaf me-2"></i>碳排放核查系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise"><i class="fas fa-tachometer-alt me-1"></i>仪表板</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise/emissions"><i class="fas fa-cloud me-1"></i>排放管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/enterprise/quotas"><i class="fas fa-chart-pie me-1"></i>配额管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise/transactions"><i class="fas fa-exchange-alt me-1"></i>交易管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise/reports"><i class="fas fa-file-alt me-1"></i>报告管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise/blockchain/config"><i class="fas fa-link me-1"></i>区块链配置</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>{{ current_user.get('username', '企业用户') }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="/enterprise/profile"><i class="fas fa-id-card me-1"></i>企业资料</a></li>
                            <li><a class="dropdown-item" href="/enterprise/settings"><i class="fas fa-cog me-1"></i>账户设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-1"></i>退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mt-4">
        <!-- 消息提示 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-chart-pie me-2"></i>配额管理</h1>
            <div>
                <a href="/enterprise/transactions/create" class="btn btn-success">
                    <i class="fas fa-shopping-cart me-1"></i>购买配额
                </a>
            </div>
        </div>

        <!-- 配额概览 -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-header gradient-custom">
                        <h5 class="card-title mb-0"><i class="fas fa-chart-pie me-2"></i>2025年配额概览</h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-4">
                            <div class="display-4 fw-bold text-success">{{ quota.remaining|default(0) }}</div>
                            <p class="text-muted">剩余配额 (tCO2e)</p>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between mb-1">
                                <span>配额使用率</span>
                                <span>{{ (quota.used / quota.total * 100)|round|int if quota and quota.total else 0 }}%</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: {{ (quota.used / quota.total * 100)|round|int if quota and quota.total else 0 }}%" aria-valuenow="{{ (quota.used / quota.total * 100)|round|int if quota and quota.total else 0 }}" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                总配额
                                <span class="badge bg-primary rounded-pill">{{ quota.total|default(0) }} tCO2e</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                已使用
                                <span class="badge bg-warning rounded-pill">{{ quota.used|default(0) }} tCO2e</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                剩余
                                <span class="badge bg-success rounded-pill">{{ quota.remaining|default(0) }} tCO2e</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-8">
                <div class="card h-100">
                    <div class="card-header gradient-custom">
                        <h5 class="card-title mb-0"><i class="fas fa-history me-2"></i>配额变动历史</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>日期</th>
                                        <th>类型</th>
                                        <th>数量 (tCO2e)</th>
                                        <th>来源/去向</th>
                                        <th>状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if quota_history %}
                                        {% for history in quota_history %}
                                            <tr>
                                                <td>{{ history.date }}</td>
                                                <td>
                                                    {% if history.type == 'allocation' %}
                                                        <span class="badge bg-primary">初始分配</span>
                                                    {% elif history.type == 'purchase' %}
                                                        <span class="badge bg-success">购买</span>
                                                    {% elif history.type == 'sale' %}
                                                        <span class="badge bg-warning">出售</span>
                                                    {% elif history.type == 'usage' %}
                                                        <span class="badge bg-danger">使用</span>
                                                    {% else %}
                                                        <span class="badge bg-secondary">{{ history.type }}</span>
                                                    {% endif %}
                                                </td>
                                                <td>{{ history.amount }}</td>
                                                <td>{{ history.source_destination }}</td>
                                                <td>
                                                    {% if history.status == 'completed' %}
                                                        <span class="badge bg-success">已完成</span>
                                                    {% elif history.status == 'pending' %}
                                                        <span class="badge bg-warning">处理中</span>
                                                    {% elif history.status == 'failed' %}
                                                        <span class="badge bg-danger">失败</span>
                                                    {% else %}
                                                        <span class="badge bg-secondary">{{ history.status }}</span>
                                                    {% endif %}
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="5" class="text-center">暂无配额变动历史</td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 配额交易市场 -->
        <div class="card mb-4">
            <div class="card-header gradient-custom">
                <h5 class="card-title mb-0"><i class="fas fa-store me-2"></i>配额交易市场</h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">当前市场价格</h5>
                                <p class="display-5 text-success">¥ 45.8</p>
                                <p class="text-muted">每吨CO2当量</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">24小时变化</h5>
                                <p class="display-5 text-danger">-2.3%</p>
                                <p class="text-muted"><i class="fas fa-arrow-down text-danger"></i> ¥1.1</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">7日平均价格</h5>
                                <p class="display-5 text-primary">¥ 46.2</p>
                                <p class="text-muted">每吨CO2当量</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">交易量</h5>
                                <p class="display-5 text-info">12.5K</p>
                                <p class="text-muted">吨CO2当量/日</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0">卖单列表</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>卖方</th>
                                                <th>数量 (tCO2e)</th>
                                                <th>单价 (¥)</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% if sell_orders %}
                                                {% for order in sell_orders %}
                                                    <tr>
                                                        <td>{{ order.seller_name }}</td>
                                                        <td>{{ order.amount }}</td>
                                                        <td>{{ order.price }}</td>
                                                        <td>
                                                            <button type="button" class="btn btn-sm btn-success buy-btn" data-order-id="{{ order.id }}">
                                                                <i class="fas fa-shopping-cart me-1"></i>购买
                                                            </button>
                                                        </td>
                                                    </tr>
                                                {% endfor %}
                                            {% else %}
                                                <tr>
                                                    <td colspan="4" class="text-center">暂无卖单</td>
                                                </tr>
                                            {% endif %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0">我的卖单</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>数量 (tCO2e)</th>
                                                <th>单价 (¥)</th>
                                                <th>发布日期</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% if my_sell_orders %}
                                                {% for order in my_sell_orders %}
                                                    <tr>
                                                        <td>{{ order.amount }}</td>
                                                        <td>{{ order.price }}</td>
                                                        <td>{{ order.date }}</td>
                                                        <td>
                                                            {% if order.status == 'active' %}
                                                                <span class="badge bg-success">活跃</span>
                                                            {% elif order.status == 'completed' %}
                                                                <span class="badge bg-primary">已完成</span>
                                                            {% elif order.status == 'cancelled' %}
                                                                <span class="badge bg-secondary">已取消</span>
                                                            {% endif %}
                                                        </td>
                                                        <td>
                                                            {% if order.status == 'active' %}
                                                                <button type="button" class="btn btn-sm btn-danger cancel-btn" data-order-id="{{ order.id }}">
                                                                    <i class="fas fa-times me-1"></i>取消
                                                                </button>
                                                            {% endif %}
                                                        </td>
                                                    </tr>
                                                {% endfor %}
                                            {% else %}
                                                <tr>
                                                    <td colspan="5" class="text-center">暂无卖单</td>
                                                </tr>
                                            {% endif %}
                                        </tbody>
                                    </table>
                                </div>
                                <div class="d-grid gap-2 mt-3">
                                    <button type="button" class="btn btn-success" id="createSellOrderBtn">
                                        <i class="fas fa-plus me-1"></i>发布卖单
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 发布卖单模态框 -->
    <div class="modal fade" id="sellOrderModal" tabindex="-1" aria-labelledby="sellOrderModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="sellOrderModalLabel">发布卖单</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="sellOrderForm">
                        <div class="mb-3">
                            <label for="sellAmount" class="form-label">数量 (tCO2e)</label>
                            <input type="number" class="form-control" id="sellAmount" name="amount" min="1" max="{{ quota.remaining|default(0) }}" required>
                            <div class="form-text">可用配额: {{ quota.remaining|default(0) }} tCO2e</div>
                        </div>
                        <div class="mb-3">
                            <label for="sellPrice" class="form-label">单价 (¥)</label>
                            <input type="number" class="form-control" id="sellPrice" name="price" min="1" step="0.1" required>
                            <div class="form-text">当前市场价格: ¥45.8/tCO2e</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-success" id="submitSellOrderBtn">发布</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">© 2025 碳排放核查系统 | 基于区块链技术</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/api.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 发布卖单按钮点击事件
            document.getElementById('createSellOrderBtn').addEventListener('click', function() {
                const sellOrderModal = new bootstrap.Modal(document.getElementById('sellOrderModal'));
                sellOrderModal.show();
            });

            // 提交卖单按钮点击事件
            document.getElementById('submitSellOrderBtn').addEventListener('click', function() {
                const form = document.getElementById('sellOrderForm');
                if (form.checkValidity()) {
                    const amount = document.getElementById('sellAmount').value;
                    const price = document.getElementById('sellPrice').value;
                    
                    // 这里可以添加创建卖单的API调用
                    alert(`卖单已发布: ${amount} tCO2e, 单价 ¥${price}`);
                    
                    // 关闭模态框
                    bootstrap.Modal.getInstance(document.getElementById('sellOrderModal')).hide();
                    
                    // 刷新页面
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    form.reportValidity();
                }
            });

            // 购买按钮点击事件
            document.querySelectorAll('.buy-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const orderId = this.getAttribute('data-order-id');
                    
                    // 这里可以添加购买配额的API调用
                    alert(`正在购买订单 #${orderId}`);
                    
                    // 刷新页面
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                });
            });

            // 取消卖单按钮点击事件
            document.querySelectorAll('.cancel-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const orderId = this.getAttribute('data-order-id');
                    
                    if (confirm('确定要取消此卖单吗？')) {
                        // 这里可以添加取消卖单的API调用
                        alert(`卖单 #${orderId} 已取消`);
                        
                        // 刷新页面
                        setTimeout(() => {
                            location.reload();
                        }, 1000);
                    }
                });
            });
        });
    </script>
</body>
</html>
