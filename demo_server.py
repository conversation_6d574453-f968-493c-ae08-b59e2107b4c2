"""
演示服务器
用于提供静态页面和API接口，方便系统演示
"""

from dotenv import load_dotenv
from flask import Flask, render_template, jsonify, request, send_from_directory

from db_utils import get_db_connection

# 加载环境变量
load_dotenv()

app = Flask(__name__)


@app.route('/login')
def login():
    """登录页面"""
    return render_template('demo/login.html')


@app.route('/register')
def register():
    """注册页面"""
    return render_template('demo/register.html')


@app.route('/')
@app.route('/home')
def home():
    """系统介绍首页"""
    return render_template('demo/home_unified.html')


@app.route('/dashboard')
def index():
    """企业用户仪表板"""
    return render_template('demo/index_unified.html')


@app.route('/calculator')
def calculator():
    """碳计算器页面"""
    return render_template('demo/calculator.html')


@app.route('/admin')
def admin_dashboard():
    """管理员仪表板"""
    return render_template('demo/admin_unified.html')


@app.route('/verifier')
def verifier_dashboard():
    """核查机构仪表板"""
    return render_template('demo/verifier_unified.html')


@app.route('/emissions')
def emissions():
    """排放数据页面"""
    return render_template('demo/emissions.html')


@app.route('/emission_submit')
def emission_submit():
    """排放数据提交页面"""
    return render_template('demo/emission_submit.html')


@app.route('/transactions')
def transactions():
    """交易页面"""
    return render_template('demo/transactions.html')


@app.route('/predictions')
def predictions():
    """预测分析页面"""
    return render_template('demo/predictions.html')


@app.route('/reports')
def reports():
    """报告生成页面"""
    return render_template('demo/reports.html')


@app.route('/verifications')
def verifications():
    """核查记录页面"""
    return render_template('demo/verifications.html')


@app.route('/emission_detail')
def emission_detail():
    """排放数据详情页面"""
    return render_template('demo/emission_detail.html')


# 核查机构相关路由
@app.route('/verifier_tasks')
def verifier_tasks():
    """核查机构待核查任务页面"""
    return render_template('demo/verifier_tasks.html')


@app.route('/verifier_records')
def verifier_records():
    """核查机构核查记录页面"""
    return render_template('demo/verifier_records.html')


@app.route('/verifier_enterprises')
def verifier_enterprises():
    """核查机构企业管理页面"""
    return render_template('demo/verifier_enterprises.html')


@app.route('/verification_detail')
def verification_detail():
    """核查详情页面"""
    return render_template('demo/verification_detail.html')


@app.route('/verifier_reports')
def verifier_reports():
    """核查机构报告生成页面"""
    return render_template('demo/verifier_reports.html')


@app.route('/enterprise_blockchain_config')
def enterprise_blockchain_config():
    """企业区块链配置页面"""
    return render_template('demo/enterprise_blockchain_config_new.html')


@app.route('/verifier_blockchain_config')
def verifier_blockchain_config():
    """核查机构区块链配置页面"""
    return render_template('demo/verifier_blockchain_config_new.html')


@app.route('/reports/<path:report_path>')
def report_templates(report_path):
    """报告模板页面"""
    return render_template(f'reports/{report_path}')


@app.route('/transaction_detail')
def transaction_detail():
    """交易详情页面"""
    return render_template('demo/transaction_detail.html')


@app.route('/enterprise_detail')
def enterprise_detail():
    """企业详情页面"""
    return render_template('demo/enterprise_detail.html')


# 管理员相关路由
@app.route('/admin_users')
def admin_users():
    """管理员用户管理页面"""
    return render_template('demo/admin_users.html')


@app.route('/admin_user_detail')
def admin_user_detail():
    """管理员用户详情页面"""
    return render_template('demo/admin_user_detail.html')


@app.route('/admin_quotas')
def admin_quotas():
    """管理员配额管理页面"""
    return render_template('demo/admin_quotas.html')


@app.route('/admin_quota_detail')
def admin_quota_detail():
    """管理员配额详情页面"""
    return render_template('demo/admin_quota_detail.html')


@app.route('/admin_verifications')
def admin_verifications():
    """管理员核查管理页面"""
    return render_template('demo/admin_verifications.html')


@app.route('/admin_transactions')
def admin_transactions():
    """管理员交易管理页面"""
    return render_template('demo/admin_transactions.html')


@app.route('/admin_settings')
def admin_settings():
    """管理员系统配置页面"""
    return render_template('demo/admin_settings.html')


@app.route('/admin_blockchain_config')
def admin_blockchain_config():
    """管理员区块链配置页面"""
    return render_template('demo/admin_blockchain_config_new.html')


@app.route('/admin_logs')
def admin_logs():
    """管理员日志查看页面"""
    return render_template('demo/admin_logs.html')


@app.route('/admin_reports')
def admin_reports():
    """管理员系统报告页面"""
    return render_template('demo/admin_reports.html')


@app.route('/api/dashboard')
def dashboard():
    """仪表板数据"""
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)

    # 获取企业ID（演示用固定ID）
    enterprise_id = 1

    # 获取总排放量
    cursor.execute("""
        SELECT SUM(emission_amount) as total_emission
        FROM emission_data
        WHERE enterprise_id = %s AND status = 'verified'
    """, (enterprise_id,))
    total_emission = cursor.fetchone()['total_emission'] or 0

    # 获取当前配额
    cursor.execute("""
        SELECT current_amount
        FROM carbon_quota
        WHERE enterprise_id = %s AND year = YEAR(CURDATE())
    """, (enterprise_id,))
    quota_result = cursor.fetchone()
    current_quota = quota_result['current_amount'] if quota_result else 0

    # 获取核查通过率
    cursor.execute("""
        SELECT
            COUNT(*) as total,
            SUM(CASE WHEN status = 'verified' THEN 1 ELSE 0 END) as verified
        FROM emission_data
        WHERE enterprise_id = %s
    """, (enterprise_id,))
    verification_result = cursor.fetchone()
    total = verification_result['total'] or 0
    verified = verification_result['verified'] or 0
    verification_rate = (verified / total * 100) if total > 0 else 0

    # 获取排放趋势数据
    cursor.execute("""
        SELECT
            DATE_FORMAT(emission_period_start, '%Y-%m') as period,
            SUM(emission_amount) as amount
        FROM emission_data
        WHERE enterprise_id = %s AND status = 'verified'
        GROUP BY DATE_FORMAT(emission_period_start, '%Y-%m')
        ORDER BY period
        LIMIT 12
    """, (enterprise_id,))
    trend_data = cursor.fetchall()

    # 获取最近排放数据
    cursor.execute("""
        SELECT
            id, emission_source, emission_amount, emission_unit,
            calculation_method, emission_period_start, emission_period_end,
            status, submission_time
        FROM emission_data
        WHERE enterprise_id = %s
        ORDER BY submission_time DESC
        LIMIT 5
    """, (enterprise_id,))
    recent_emissions = cursor.fetchall()

    # 获取最近交易记录
    cursor.execute("""
        SELECT
            t.id, t.amount, t.price, t.total_price, t.transaction_time, t.status,
            u.company_name as counterparty
        FROM transaction t
        JOIN user u ON (t.seller_id = %s AND t.buyer_id = u.id) OR (t.buyer_id = %s AND t.seller_id = u.id)
        ORDER BY t.transaction_time DESC
        LIMIT 5
    """, (enterprise_id, enterprise_id))
    recent_transactions = cursor.fetchall()

    conn.close()

    return jsonify({
        'stats': {
            'total_emission': float(total_emission),
            'current_quota': float(current_quota),
            'verification_rate': float(verification_rate)
        },
        'trend_data': [dict(item) for item in trend_data],
        'recent_emissions': [dict(item) for item in recent_emissions],
        'recent_transactions': [dict(item) for item in recent_transactions]
    })


@app.route('/api/emissions')
def get_emissions():
    """获取排放数据"""
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)

    # 获取企业ID（演示用固定ID）
    enterprise_id = 1

    cursor.execute("""
        SELECT
            id, emission_source, emission_amount, emission_unit,
            calculation_method, emission_period_start, emission_period_end,
            status, submission_time
        FROM emission_data
        WHERE enterprise_id = %s
        ORDER BY submission_time DESC
    """, (enterprise_id,))
    emissions = cursor.fetchall()

    conn.close()

    return jsonify({
        'emissions': [dict(item) for item in emissions]
    })


@app.route('/api/transactions')
def get_transactions():
    """获取交易记录"""
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)

    # 获取企业ID（演示用固定ID）
    enterprise_id = 1

    cursor.execute("""
        SELECT
            t.id, t.amount, t.price, t.total_price, t.transaction_time, t.status,
            u.company_name as counterparty,
            CASE WHEN t.seller_id = %s THEN 'sell' ELSE 'buy' END as type
        FROM transaction t
        JOIN user u ON (t.seller_id = %s AND t.buyer_id = u.id) OR (t.buyer_id = %s AND t.seller_id = u.id)
        ORDER BY t.transaction_time DESC
    """, (enterprise_id, enterprise_id, enterprise_id))
    transactions = cursor.fetchall()

    conn.close()

    return jsonify({
        'transactions': [dict(item) for item in transactions]
    })


@app.route('/api/verifications')
def get_verifications():
    """获取核查记录"""
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)

    # 获取企业ID（演示用固定ID）
    enterprise_id = 1

    cursor.execute("""
        SELECT
            v.id, v.conclusion, v.comments, v.verification_time,
            e.emission_source, e.emission_amount, e.emission_period_start, e.emission_period_end,
            u.company_name as verifier_name
        FROM verification v
        JOIN emission_data e ON v.emission_data_id = e.id
        JOIN user u ON v.verifier_id = u.id
        WHERE e.enterprise_id = %s
        ORDER BY v.verification_time DESC
    """, (enterprise_id,))
    verifications = cursor.fetchall()

    conn.close()

    return jsonify({
        'verifications': [dict(item) for item in verifications]
    })


@app.route('/api/calculator', methods=['POST'])
def calculate_carbon():
    """碳计算器"""
    data = request.json

    # 简单的计算逻辑（实际应用中应该更复杂）
    result = {
        'total_emission': 0,
        'breakdown': {}
    }

    emission_factors = {
        'coal': 2.86,  # 吨CO2e/吨
        'gas': 2.18,  # 吨CO2e/千立方米
        'electricity': 0.785,  # 吨CO2e/MWh
        'gasoline': 2.36,  # 吨CO2e/吨
        'diesel': 2.73  # 吨CO2e/吨
    }

    for source, amount in data.items():
        if source in emission_factors:
            emission = float(amount) * emission_factors[source]
            result['breakdown'][source] = emission
            result['total_emission'] += emission

    return jsonify(result)


@app.route('/api/prediction')
def predict_emissions():
    """排放预测"""
    # 简单的预测数据（实际应用中应该基于真实数据和模型）
    current_year = 2023
    prediction_data = [
        {'year': current_year, 'month': 1, 'amount': 450},
        {'year': current_year, 'month': 2, 'amount': 420},
        {'year': current_year, 'month': 3, 'amount': 480},
        {'year': current_year, 'month': 4, 'amount': 510},
        {'year': current_year, 'month': 5, 'amount': 490},
        {'year': current_year, 'month': 6, 'amount': 520},
        # 预测数据
        {'year': current_year, 'month': 7, 'amount': 530, 'predicted': True},
        {'year': current_year, 'month': 8, 'amount': 550, 'predicted': True},
        {'year': current_year, 'month': 9, 'amount': 570, 'predicted': True},
        {'year': current_year, 'month': 10, 'amount': 590, 'predicted': True},
        {'year': current_year, 'month': 11, 'amount': 610, 'predicted': True},
        {'year': current_year, 'month': 12, 'amount': 630, 'predicted': True}
    ]

    return jsonify({
        'prediction': prediction_data,
        'trend': '上升',
        'annual_total': 6350,
        'confidence': 0.85
    })


@app.route('/api/reports')
def get_reports():
    """获取报告列表"""
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)

    # 获取企业ID（演示用固定ID）
    enterprise_id = 1

    cursor.execute("""
        SELECT id, title, report_type, created_at, period_start, period_end
        FROM report
        WHERE enterprise_id = %s
        ORDER BY created_at DESC
    """, (enterprise_id,))
    reports = cursor.fetchall()

    conn.close()

    return jsonify({
        'reports': [dict(item) for item in reports]
    })


@app.route('/api/reports/generate', methods=['POST'])
def generate_report():
    """生成报告"""
    data = request.json

    # 简单的报告生成逻辑（实际应用中应该更复杂）
    report_type = data.get('report_type', 'emission')
    period_start = data.get('period_start', '2023-01-01')
    period_end = data.get('period_end', '2023-06-30')

    # 生成报告ID（实际应用中应该保存到数据库）
    report_id = 1001

    return jsonify({
        'report_id': report_id,
        'message': '报告生成成功',
        'download_url': f'/api/reports/{report_id}/download'
    })


@app.route('/api/reports/<int:report_id>/download')
def download_report(report_id):
    """下载报告"""
    # 实际应用中应该生成真实的报告文件
    # 这里只是返回一个示例HTML
    return render_template('reports/emission_report.html')


@app.route('/reports/<path:report_path>')
def report_preview(report_path):
    """报告预览页面"""
    return render_template(f'reports/{report_path}')


@app.route('/static/<path:path>')
def serve_static(path):
    """提供静态文件服务"""
    return send_from_directory('static', path)


if __name__ == '__main__':
    app.run(debug=True, port=5000)
