import React, { createContext, useState, useContext, useEffect } from 'react';
import { authAPI } from '../services/api';

const AuthContext = createContext();

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // 检查是否已登录
    const checkLoginStatus = async () => {
      const token = localStorage.getItem('token');
      if (token) {
        try {
          const userData = await authAPI.getProfile();
          setCurrentUser(userData);
        } catch (error) {
          // 如果获取用户信息失败，清除 token
          localStorage.removeItem('token');
        }
      }
      setLoading(false);
    };

    checkLoginStatus();
  }, []);

  const login = async (user) => {
    setCurrentUser(user);
    return user;
  };

  const logout = () => {
    localStorage.removeItem('token');
    setCurrentUser(null);
  };

  const updateProfile = async (data) => {
    const updatedUser = await authAPI.updateProfile(data);
    setCurrentUser(updatedUser.user);
    return updatedUser;
  };

  const value = {
    currentUser,
    login,
    logout,
    updateProfile,
    isAdmin: currentUser?.role === 'admin',
    isEnterprise: currentUser?.role === 'enterprise',
    isVerifier: currentUser?.role === 'verifier'
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};
