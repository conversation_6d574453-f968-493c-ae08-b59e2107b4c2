<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放核查系统 - 企业仪表板</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container">
            <a class="navbar-brand" href="/"><i class="fas fa-leaf me-2"></i>碳排放核查系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise"><i class="fas fa-tachometer-alt me-1"></i>仪表板</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise/emissions"><i class="fas fa-cloud me-1"></i>排放数据</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise/quotas"><i class="fas fa-chart-pie me-1"></i>配额管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise/transactions"><i class="fas fa-exchange-alt me-1"></i>交易记录</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise/reports"><i class="fas fa-file-alt me-1"></i>报告管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise/blockchain/config"><i class="fas fa-link me-1"></i>区块链配置</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>{{ current_user.get('username', '企业用户') }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="/enterprise/profile"><i class="fas fa-id-card me-1"></i>企业资料</a></li>
                            <li><a class="dropdown-item" href="/enterprise/settings"><i class="fas fa-cog me-1"></i>账户设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-1"></i>退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mt-4">
        <!-- 消息提示 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- 错误信息 -->
        {% if error %}
            <div class="alert alert-danger">
                <h4 class="alert-heading">发生错误!</h4>
                <p>{{ error }}</p>
            </div>
        {% endif %}

        <!-- 仪表板标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-tachometer-alt me-2"></i>企业仪表板</h1>
            <div>
                <span class="badge bg-primary">当前时间: <span id="current-time"></span></span>
            </div>
        </div>

        <!-- 企业信息 -->
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0"><i class="fas fa-building me-2"></i>企业信息</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>企业名称:</strong> {{ current_user.get('company_name', '未知企业') }}</p>
                        <p><strong>用户名:</strong> {{ current_user.get('username', '未知用户') }}</p>
                        <p><strong>角色:</strong> <span class="badge bg-success">企业用户</span></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>账户状态:</strong> <span class="badge bg-success">正常</span></p>
                        <p><strong>登录时间:</strong> {{ current_user.get('login_time', '未知') }}</p>
                        <p><strong>区块链地址:</strong> <span class="text-muted">{{ user.blockchain_address if user and user.blockchain_address else '未配置' }}</span></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 配额信息 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0"><i class="fas fa-chart-pie me-2"></i>配额信息 ({{ quota_data.year|default('2025') }}年)</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center mb-3">
                            <div class="col-md-4">
                                <h5>总配额</h5>
                                <h2 class="text-primary">{{ quota_data.total|default(0) }}</h2>
                                <p class="text-muted">tCO2e</p>
                            </div>
                            <div class="col-md-4">
                                <h5>已使用</h5>
                                <h2 class="text-danger">{{ quota_data.used|default(0) }}</h2>
                                <p class="text-muted">tCO2e</p>
                            </div>
                            <div class="col-md-4">
                                <h5>剩余</h5>
                                <h2 class="text-success">{{ quota_data.remaining|default(0) }}</h2>
                                <p class="text-muted">tCO2e</p>
                            </div>
                        </div>
                        <div class="progress" style="height: 25px;">
                            {% set percentage = (quota_data.used|default(0) / quota_data.total|default(1) * 100)|round|int %}
                            <div class="progress-bar bg-success" role="progressbar" style="width: {{ percentage }}%;" aria-valuenow="{{ percentage }}" aria-valuemin="0" aria-valuemax="100">{{ percentage }}%</div>
                        </div>
                    </div>
                    <div class="card-footer bg-light">
                        <a href="/enterprise/quotas" class="btn btn-sm btn-primary">查看详情</a>
                        <a href="/enterprise/transactions/new" class="btn btn-sm btn-success float-end">交易配额</a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0"><i class="fas fa-cloud me-2"></i>排放数据概览</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="emissionChart" width="400" height="200"></canvas>
                    </div>
                    <div class="card-footer bg-light">
                        <a href="/enterprise/emissions" class="btn btn-sm btn-primary">查看详情</a>
                        <a href="/enterprise/emissions/add" class="btn btn-sm btn-success float-end">提交数据</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最近活动 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0"><i class="fas fa-cloud me-2"></i>最近排放数据</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>来源</th>
                                        <th>数量</th>
                                        <th>日期</th>
                                        <th>状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if emission_data %}
                                        {% for emission in emission_data %}
                                            <tr>
                                                <td>{{ emission.source }}</td>
                                                <td>{{ emission.amount }} {{ emission.unit }}</td>
                                                <td>{{ emission.date }}</td>
                                                <td>
                                                    {% if emission.status == '已核查' %}
                                                        <span class="badge bg-success">{{ emission.status }}</span>
                                                    {% elif emission.status == '待核查' %}
                                                        <span class="badge bg-warning">{{ emission.status }}</span>
                                                    {% else %}
                                                        <span class="badge bg-secondary">{{ emission.status }}</span>
                                                    {% endif %}
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="4" class="text-center">暂无数据</td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0"><i class="fas fa-exchange-alt me-2"></i>最近交易记录</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>类型</th>
                                        <th>数量</th>
                                        <th>价格</th>
                                        <th>日期</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if transaction_data %}
                                        {% for transaction in transaction_data %}
                                            <tr>
                                                <td>
                                                    {% if transaction.type == '购买' %}
                                                        <span class="badge bg-success">{{ transaction.type }}</span>
                                                    {% else %}
                                                        <span class="badge bg-danger">{{ transaction.type }}</span>
                                                    {% endif %}
                                                </td>
                                                <td>{{ transaction.amount }} {{ transaction.unit }}</td>
                                                <td>¥{{ transaction.price }}/tCO2e</td>
                                                <td>{{ transaction.date }}</td>
                                            </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="4" class="text-center">暂无数据</td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0"><i class="fas fa-bolt me-2"></i>快速操作</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <a href="/enterprise/emissions/add" class="btn btn-primary w-100">
                                    <i class="fas fa-cloud-upload-alt me-2"></i>提交排放数据
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="/enterprise/transactions/new" class="btn btn-success w-100">
                                    <i class="fas fa-exchange-alt me-2"></i>交易配额
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="/enterprise/reports/generate" class="btn btn-info w-100">
                                    <i class="fas fa-file-alt me-2"></i>生成报告
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="/enterprise/calculator" class="btn btn-warning w-100">
                                    <i class="fas fa-calculator me-2"></i>碳排放计算器
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">© 2025 碳排放核查系统 | 基于区块链技术</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // 显示当前时间
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleString('zh-CN');
        }
        updateTime();
        setInterval(updateTime, 1000);

        // 排放数据图表
        const ctx = document.getElementById('emissionChart').getContext('2d');
        const emissionChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['电力消耗', '燃料燃烧', '工业过程', '废弃物处理'],
                datasets: [{
                    label: '碳排放量 (tCO2e)',
                    data: [1200, 800, 500, 300],
                    backgroundColor: [
                        'rgba(75, 192, 192, 0.2)',
                        'rgba(54, 162, 235, 0.2)',
                        'rgba(255, 206, 86, 0.2)',
                        'rgba(255, 99, 132, 0.2)'
                    ],
                    borderColor: [
                        'rgba(75, 192, 192, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(255, 99, 132, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    </script>
</body>
</html>
