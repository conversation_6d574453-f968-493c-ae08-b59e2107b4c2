{% extends 'demo/layouts/admin_base.html' %}

{% block title %}碳排放核查系统 - 管理员演示{% endblock %}

{% block head %}
<style>
    .dashboard {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
    }
    .stat-card {
        text-align: center;
        padding: 30px 20px;
    }
    .stat-value {
        font-size: 36px;
        font-weight: bold;
        background: linear-gradient(to right, #4CAF50, #2E7D32);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin: 15px 0;
        text-shadow: 0 2px 5px rgba(0,0,0,0.05);
    }
    .stat-label {
        color: #7f8c8d;
    }
    .chart-container {
        height: 300px;
        margin-top: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .status {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        display: inline-block;
    }
    .status-active {
        background: linear-gradient(to right, #2ecc71, #27ae60);
        color: white;
    }
    .status-inactive {
        background: linear-gradient(to right, #e74c3c, #c0392b);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
    <h1>系统概览</h1>

    <div class="dashboard">
        <div class="card stat-card">
            <div class="stat-label">企业用户</div>
            <div class="stat-value">15</div>
            <div class="stat-label">个</div>
        </div>
        <div class="card stat-card">
            <div class="stat-label">核查机构</div>
            <div class="stat-value">5</div>
            <div class="stat-label">个</div>
        </div>
        <div class="card stat-card">
            <div class="stat-label">总排放量</div>
            <div class="stat-value">25,450</div>
            <div class="stat-label">吨CO2e</div>
        </div>
        <div class="card stat-card">
            <div class="stat-label">交易总额</div>
            <div class="stat-value">1,250,000</div>
            <div class="stat-label">元</div>
        </div>
    </div>

    <div class="card">
        <div class="card-title">系统活动</div>
        <div class="chart-container">
            <canvas id="activityChart"></canvas>
        </div>
        <table>
            <thead>
                <tr>
                    <th>时间</th>
                    <th>用户</th>
                    <th>活动类型</th>
                    <th>详情</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>2025-05-12 09:45:23</td>
                    <td>enterprise1</td>
                    <td>提交排放数据</td>
                    <td>提交了2025年4月的排放数据</td>
                </tr>
                <tr>
                    <td>2025-05-12 10:15:47</td>
                    <td>verifier1</td>
                    <td>核查排放数据</td>
                    <td>核查了enterprise1的排放数据</td>
                </tr>
                <tr>
                    <td>2025-05-12 11:30:12</td>
                    <td>enterprise2</td>
                    <td>交易</td>
                    <td>向enterprise3购买了500吨配额</td>
                </tr>
                <tr>
                    <td>2025-05-12 13:20:05</td>
                    <td>admin</td>
                    <td>系统配置</td>
                    <td>修改了碳价格配置</td>
                </tr>
                <tr>
                    <td>2025-05-12 14:45:33</td>
                    <td>enterprise3</td>
                    <td>生成报告</td>
                    <td>生成了2025年第一季度排放报告</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="card">
        <div class="card-title">用户列表</div>
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>用户名</th>
                    <th>角色</th>
                    <th>公司名称</th>
                    <th>注册时间</th>
                    <th>状态</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>1</td>
                    <td>admin</td>
                    <td>管理员</td>
                    <td>系统管理</td>
                    <td>2025-01-01</td>
                    <td><span class="status status-active">活跃</span></td>
                    <td><a href="/admin_user_detail?id=1" class="btn btn-primary">查看</a></td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>enterprise1</td>
                    <td>企业</td>
                    <td>北京碳排放科技有限公司</td>
                    <td>2025-01-15</td>
                    <td><span class="status status-active">活跃</span></td>
                    <td><a href="/admin_user_detail?id=2" class="btn btn-primary">查看</a></td>
                </tr>
                <tr>
                    <td>3</td>
                    <td>enterprise2</td>
                    <td>企业</td>
                    <td>上海绿色能源有限公司</td>
                    <td>2025-01-20</td>
                    <td><span class="status status-active">活跃</span></td>
                    <td><a href="/admin_user_detail?id=3" class="btn btn-primary">查看</a></td>
                </tr>
                <tr>
                    <td>4</td>
                    <td>verifier1</td>
                    <td>核查机构</td>
                    <td>国家碳排放核查中心</td>
                    <td>2025-02-01</td>
                    <td><span class="status status-active">活跃</span></td>
                    <td><a href="/admin_user_detail?id=4" class="btn btn-primary">查看</a></td>
                </tr>
            </tbody>
        </table>
    </div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 添加系统活动图表
        const activityCtx = document.getElementById('activityChart').getContext('2d');
        const activityChart = new Chart(activityCtx, {
            type: 'line',
            data: {
                labels: ['5月6日', '5月7日', '5月8日', '5月9日', '5月10日', '5月11日', '5月12日'],
                datasets: [
                    {
                        label: '排放数据提交',
                        data: [5, 7, 4, 6, 8, 9, 12],
                        borderColor: 'rgba(46, 204, 113, 1)',
                        backgroundColor: 'rgba(46, 204, 113, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    },
                    {
                        label: '核查活动',
                        data: [3, 4, 2, 5, 6, 7, 8],
                        borderColor: 'rgba(52, 152, 219, 1)',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    },
                    {
                        label: '交易活动',
                        data: [2, 3, 1, 4, 3, 5, 6],
                        borderColor: 'rgba(155, 89, 182, 1)',
                        backgroundColor: 'rgba(155, 89, 182, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '活动数量'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: '最近7天系统活动'
                    }
                }
            }
        });
    });
</script>
{% endblock %}
