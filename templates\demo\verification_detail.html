<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放管理系统 - 核查详情</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background: linear-gradient(to right, #1B5E20, #388E3C);
            color: white;
            padding: 20px 0;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-name {
            margin-right: 15px;
        }
        .logout-btn {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 30px;
            cursor: pointer;
            text-decoration: none;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .logout-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        nav {
            background: linear-gradient(to right, #2E7D32, #43A047);
            padding: 10px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
        }
        .nav-item {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            margin-right: 5px;
            border-radius: 30px;
            transition: all 0.3s ease;
        }
        .nav-item:hover, .nav-item.active {
            background: linear-gradient(to right, #1B5E20, #2E7D32);
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        .card {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            transform: translateY(-5px);
        }
        .card-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        .btn-primary {
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            color: white;
        }
        .btn-success {
            background: linear-gradient(to right, #2ecc71, #27ae60);
            color: white;
        }
        .btn-danger {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
        }
        .detail-section {
            margin-bottom: 30px;
            background-color: #f9f9f9;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.05);
            border-left: 5px solid #4CAF50;
        }
        .detail-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2E7D32;
            border-bottom: 1px solid #A5D6A7;
            padding-bottom: 10px;
        }
        .detail-row {
            display: flex;
            margin-bottom: 15px;
            border-bottom: 1px dashed rgba(0,0,0,0.05);
            padding-bottom: 10px;
        }
        .detail-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        .detail-label {
            width: 180px;
            font-weight: bold;
            color: #388E3C;
        }
        .detail-value {
            flex: 1;
        }
        .status {
            display: inline-block;
            padding: 8px 15px;
            border-radius: 30px;
            font-size: 12px;
            font-weight: bold;
            color: white;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        .status-pending {
            background: linear-gradient(to right, #f39c12, #e67e22);
        }
        .status-verified {
            background: linear-gradient(to right, #2ecc71, #27ae60);
        }
        .status-rejected {
            background: linear-gradient(to right, #e74c3c, #c0392b);
        }
        .blockchain-info {
            background-color: #E8F5E9;
            padding: 20px;
            border-radius: 10px;
            margin-top: 10px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.05);
        }
        .blockchain-hash {
            font-family: monospace;
            background-color: rgba(76, 175, 80, 0.1);
            padding: 5px 10px;
            border-radius: 5px;
            color: #2E7D32;
            word-break: break-all;
        }
        .verification-info {
            background-color: #E8F5E9;
            padding: 20px;
            border-radius: 10px;
            margin-top: 10px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.05);
        }
        .verification-comments {
            margin-top: 10px;
            padding: 15px;
            background-color: #fff;
            border: 1px solid #A5D6A7;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            line-height: 1.6;
        }
        .actions {
            display: flex;
            justify-content: flex-end;
            gap: 15px;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px dashed rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo">碳排放管理系统</div>
            <div class="user-info">
                <span class="user-name">国家碳排放核查中心</span>
                <a href="/login" class="logout-btn">退出登录</a>
            </div>
        </div>
    </header>

    <nav>
        <div class="nav-content">
            <a href="/verifier" class="nav-item">仪表板</a>
            <a href="/verifier_tasks" class="nav-item">待核查任务</a>
            <a href="/verifier_records" class="nav-item active">核查记录</a>
            <a href="/verifier_enterprises" class="nav-item">企业管理</a>
            <a href="/verifier_reports" class="nav-item">报告生成</a>
        </div>
    </nav>

    <div class="container">
        <div class="card-title">
            <h1>核查详情</h1>
            <div>
                <a href="/verifier_records" class="btn btn-primary">返回列表</a>
            </div>
        </div>

        <div class="card">
            <div class="detail-section">
                <div class="detail-title">基本信息</div>

                <div class="detail-row">
                    <div class="detail-label">核查ID</div>
                    <div class="detail-value">1001</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">排放数据ID</div>
                    <div class="detail-value">1001</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">企业</div>
                    <div class="detail-value">北京碳排放科技有限公司</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">排放源</div>
                    <div class="detail-value">燃煤锅炉</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">排放量</div>
                    <div class="detail-value">450 吨CO2e</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">计算方法</div>
                    <div class="detail-value">排放因子法</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">排放期间</div>
                    <div class="detail-value">2023-01-01 至 2023-01-31</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">提交时间</div>
                    <div class="detail-value">2023-02-05 10:30:45</div>
                </div>
            </div>

            <div class="detail-section">
                <div class="detail-title">核查信息</div>

                <div class="detail-row">
                    <div class="detail-label">核查结论</div>
                    <div class="detail-value">
                        <span class="status status-verified">通过</span>
                    </div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">核查时间</div>
                    <div class="detail-value">2023-02-15 14:20:30</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">核查人员</div>
                    <div class="detail-value">张三</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">核查意见</div>
                    <div class="detail-value">
                        <div class="verification-comments">
                            经核查，该排放数据计算方法正确，活动数据来源可靠，排放因子选择合理，计算结果准确。建议企业进一步完善能源消耗记录，提高数据质量。
                        </div>
                    </div>
                </div>
            </div>

            <div class="detail-section">
                <div class="detail-title">区块链记录</div>

                <div class="blockchain-info">
                    <div class="detail-row">
                        <div class="detail-label">交易哈希</div>
                        <div class="detail-value">
                            <span class="blockchain-hash">0xa1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4e5f6a1b2</span>
                        </div>
                    </div>

                    <div class="detail-row">
                        <div class="detail-label">区块号</div>
                        <div class="detail-value">15790123</div>
                    </div>

                    <div class="detail-row">
                        <div class="detail-label">时间戳</div>
                        <div class="detail-value">2023-02-15 14:25:12</div>
                    </div>

                    <div class="detail-row">
                        <div class="detail-label">数据哈希</div>
                        <div class="detail-value">
                            <span class="blockchain-hash">0xf6e5d4c3b2a1f6e5d4c3b2a1f6e5d4c3b2a1f6e5d4c3b2a1f6e5d4c3b2a1f6e5</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="actions">
                <a href="/verifier_records" class="btn btn-primary">返回列表</a>
                <a href="/reports/verification_report.html" target="_blank" class="btn btn-success">生成报告</a>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 导航切换 - 不需要额外的事件监听器，因为我们使用的是链接
            // 根据当前URL设置活动导航项
            const currentPath = window.location.pathname;
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                if (item.getAttribute('href') === currentPath) {
                    navItems.forEach(i => i.classList.remove('active'));
                    item.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>
