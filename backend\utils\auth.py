"""
认证工具模块
用于提供认证和授权相关的工具函数
"""

from functools import wraps
from flask import request, jsonify, current_app, g
from flask_jwt_extended import verify_jwt_in_request, get_jwt_identity

def admin_required(f):
    """
    管理员权限装饰器
    用于保护只有管理员才能访问的路由
    """
    @wraps(f)
    def decorated(*args, **kwargs):
        try:
            # 验证JWT令牌
            verify_jwt_in_request()
            
            # 获取用户身份
            user_id = get_jwt_identity()
            
            # 从数据库获取用户信息
            from backend.models.user import User
            user = User.query.get(user_id)
            
            # 检查用户是否是管理员
            if not user or user.role != 'admin':
                return jsonify({"msg": "需要管理员权限"}), 403
            
            # 将用户信息存储在g对象中，以便在视图函数中使用
            g.user = user
            
            return f(*args, **kwargs)
        except Exception as e:
            return jsonify({"msg": f"认证失败: {str(e)}"}), 401
    
    return decorated

def enterprise_required(f):
    """
    企业用户权限装饰器
    用于保护只有企业用户才能访问的路由
    """
    @wraps(f)
    def decorated(*args, **kwargs):
        try:
            # 验证JWT令牌
            verify_jwt_in_request()
            
            # 获取用户身份
            user_id = get_jwt_identity()
            
            # 从数据库获取用户信息
            from backend.models.user import User
            user = User.query.get(user_id)
            
            # 检查用户是否是企业用户
            if not user or user.role != 'enterprise':
                return jsonify({"msg": "需要企业用户权限"}), 403
            
            # 将用户信息存储在g对象中，以便在视图函数中使用
            g.user = user
            
            return f(*args, **kwargs)
        except Exception as e:
            return jsonify({"msg": f"认证失败: {str(e)}"}), 401
    
    return decorated

def verifier_required(f):
    """
    核查机构权限装饰器
    用于保护只有核查机构才能访问的路由
    """
    @wraps(f)
    def decorated(*args, **kwargs):
        try:
            # 验证JWT令牌
            verify_jwt_in_request()
            
            # 获取用户身份
            user_id = get_jwt_identity()
            
            # 从数据库获取用户信息
            from backend.models.user import User
            user = User.query.get(user_id)
            
            # 检查用户是否是核查机构
            if not user or user.role != 'verifier':
                return jsonify({"msg": "需要核查机构权限"}), 403
            
            # 将用户信息存储在g对象中，以便在视图函数中使用
            g.user = user
            
            return f(*args, **kwargs)
        except Exception as e:
            return jsonify({"msg": f"认证失败: {str(e)}"}), 401
    
    return decorated

def login_required(f):
    """
    登录要求装饰器
    用于保护需要登录才能访问的路由
    """
    @wraps(f)
    def decorated(*args, **kwargs):
        try:
            # 验证JWT令牌
            verify_jwt_in_request()
            
            # 获取用户身份
            user_id = get_jwt_identity()
            
            # 从数据库获取用户信息
            from backend.models.user import User
            user = User.query.get(user_id)
            
            # 检查用户是否存在
            if not user:
                return jsonify({"msg": "用户不存在"}), 403
            
            # 将用户信息存储在g对象中，以便在视图函数中使用
            g.user = user
            
            return f(*args, **kwargs)
        except Exception as e:
            return jsonify({"msg": f"认证失败: {str(e)}"}), 401
    
    return decorated
