<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放管理系统运行报告</title>
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            margin: 0;
            padding: 0;
            color: #333;
            background-color: #fff;
            line-height: 1.6;
        }
        .container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20mm 15mm;
            box-sizing: border-box;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            position: relative;
        }
        .header:after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(to right, #1B5E20, #4CAF50, #8BC34A);
            border-radius: 5px;
        }
        .title {
            font-size: 28pt;
            font-weight: bold;
            margin-bottom: 10px;
            color: #1B5E20;
        }
        .subtitle {
            font-size: 16pt;
            margin-bottom: 10px;
            color: #2E7D32;
        }
        .info {
            margin: 30px 0;
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        .info-card {
            flex: 1;
            min-width: 200px;
            background-color: #f1f8e9;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            border-left: 5px solid #4CAF50;
        }
        .info-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #2E7D32;
            font-size: 14pt;
        }
        .info-value {
            font-size: 24pt;
            font-weight: bold;
            color: #1B5E20;
            margin-bottom: 5px;
        }
        .info-desc {
            font-size: 10pt;
            color: #555;
        }
        h1 {
            font-size: 20pt;
            color: #1B5E20;
            margin-top: 40px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #4CAF50;
        }
        h2 {
            font-size: 16pt;
            color: #2E7D32;
            margin-top: 30px;
            margin-bottom: 15px;
        }
        h3 {
            font-size: 14pt;
            color: #388E3C;
            margin-top: 20px;
            margin-bottom: 10px;
        }
        p {
            margin-bottom: 15px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
        }
        th {
            background: linear-gradient(to right, #2E7D32, #4CAF50);
            color: white;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 12pt;
        }
        tr:nth-child(even) {
            background-color: #f1f8e9;
        }
        tr:hover {
            background-color: #e8f5e9;
        }
        td {
            border-bottom: 1px solid #ddd;
        }
        .chart-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 30px 0;
        }
        .chart {
            flex: 1;
            min-width: 300px;
            height: 300px;
            background-color: #f1f8e9;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            padding: 15px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        .chart-title {
            font-weight: bold;
            color: #2E7D32;
            margin-bottom: 10px;
            font-size: 14pt;
        }
        .chart img {
            max-width: 100%;
            max-height: 250px;
        }
        .footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 2px solid #4CAF50;
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
        }
        .footer-left {
            flex: 2;
        }
        .footer-right {
            flex: 1;
            text-align: right;
        }
        .footer-title {
            font-weight: bold;
            color: #2E7D32;
            margin-bottom: 10px;
        }
        .footer-info {
            margin-bottom: 5px;
            font-size: 10pt;
            color: #555;
        }
        .stamp {
            width: 120px;
            height: 120px;
            margin-left: auto;
        }
        .stamp img {
            width: 100%;
            height: 100%;
        }
        .highlight {
            background-color: #e8f5e9;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .highlight-title {
            font-weight: bold;
            color: #2E7D32;
            margin-bottom: 10px;
            font-size: 14pt;
        }
        .trend-indicator {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 15px;
            font-size: 10pt;
            font-weight: bold;
            margin-left: 10px;
        }
        .trend-up {
            background-color: #e8f5e9;
            color: #2E7D32;
        }
        .trend-down {
            background-color: #ffebee;
            color: #c62828;
        }
        .trend-stable {
            background-color: #e3f2fd;
            color: #1565c0;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .status-good {
            background-color: #4CAF50;
        }
        .status-warning {
            background-color: #FFC107;
        }
        .status-error {
            background-color: #F44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">碳排放管理系统运行报告</div>
            <div class="subtitle">2025年第一季度系统运行情况</div>
        </div>

        <div class="info">
            <div class="info-card">
                <div class="info-title">注册企业数</div>
                <div class="info-value">128</div>
                <div class="info-desc">较上季度增长12.3%</div>
            </div>
            <div class="info-card">
                <div class="info-title">核查机构数</div>
                <div class="info-value">15</div>
                <div class="info-desc">较上季度增长7.1%</div>
            </div>
            <div class="info-card">
                <div class="info-title">系统可用率</div>
                <div class="info-value">99.8%</div>
                <div class="info-desc">较上季度提高0.2%</div>
            </div>
            <div class="info-card">
                <div class="info-title">区块链同步率</div>
                <div class="info-value">100%</div>
                <div class="info-desc">所有交易已同步</div>
            </div>
        </div>

        <h1>1. 系统概况</h1>
        <p>本报告汇总了碳排放管理系统2025年第一季度的运行情况，包括用户活跃度、数据处理情况、系统性能、安全状况以及区块链同步情况等内容。</p>

        <h2>1.1 用户活跃度</h2>
        <div class="chart-container">
            <div class="chart">
                <div class="chart-title">用户登录次数</div>
                <img src="/static/images/user_login_stats.png" alt="用户登录统计图" onerror="this.src='data:image/svg+xml;charset=utf-8,%3Csvg xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22 width%3D%22300%22 height%3D%22250%22%3E%3Crect width%3D%22300%22 height%3D%22250%22 fill%3D%22%23f1f8e9%22%3E%3C%2Frect%3E%3Ctext x%3D%2250%22 y%3D%22125%22 font-family%3D%22Arial%22 font-size%3D%2220%22 fill%3D%22%234CAF50%22%3E用户登录统计图%3C%2Ftext%3E%3C%2Fsvg%3E'">
            </div>
            <div class="chart">
                <div class="chart-title">用户类型分布</div>
                <img src="/static/images/user_type_distribution.png" alt="用户类型分布图" onerror="this.src='data:image/svg+xml;charset=utf-8,%3Csvg xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22 width%3D%22300%22 height%3D%22250%22%3E%3Crect width%3D%22300%22 height%3D%22250%22 fill%3D%22%23f1f8e9%22%3E%3C%2Frect%3E%3Ctext x%3D%2250%22 y%3D%22125%22 font-family%3D%22Arial%22 font-size%3D%2220%22 fill%3D%22%234CAF50%22%3E用户类型分布图%3C%2Ftext%3E%3C%2Fsvg%3E'">
            </div>
        </div>
        <p>2025年第一季度，系统日均活跃用户数为85人，较上季度增长15.3%。企业用户活跃度最高，占总活跃用户的68%，核查机构占25%，管理员占7%。用户登录高峰期主要集中在工作日上午9:00-11:00和下午14:00-16:00。</p>

        <h2>1.2 数据处理情况</h2>
        <table>
            <thead>
                <tr>
                    <th>数据类型</th>
                    <th>处理量</th>
                    <th>同比变化</th>
                    <th>处理成功率</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>排放数据提交</td>
                    <td>256条</td>
                    <td><span class="trend-indicator trend-up">+18.5%</span></td>
                    <td>99.6%</td>
                </tr>
                <tr>
                    <td>核查记录</td>
                    <td>185条</td>
                    <td><span class="trend-indicator trend-up">+12.8%</span></td>
                    <td>100%</td>
                </tr>
                <tr>
                    <td>交易记录</td>
                    <td>128条</td>
                    <td><span class="trend-indicator trend-up">+25.5%</span></td>
                    <td>100%</td>
                </tr>
                <tr>
                    <td>报告生成</td>
                    <td>95份</td>
                    <td><span class="trend-indicator trend-up">+15.9%</span></td>
                    <td>100%</td>
                </tr>
                <tr>
                    <td>区块链交易</td>
                    <td>325条</td>
                    <td><span class="trend-indicator trend-up">+22.6%</span></td>
                    <td>99.7%</td>
                </tr>
            </tbody>
        </table>

        <h1>2. 系统性能</h1>

        <h2>2.1 响应时间</h2>
        <div class="chart-container">
            <div class="chart">
                <div class="chart-title">平均响应时间</div>
                <img src="/static/images/response_time.png" alt="响应时间图" onerror="this.src='data:image/svg+xml;charset=utf-8,%3Csvg xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22 width%3D%22300%22 height%3D%22250%22%3E%3Crect width%3D%22300%22 height%3D%22250%22 fill%3D%22%23f1f8e9%22%3E%3C%2Frect%3E%3Ctext x%3D%2250%22 y%3D%22125%22 font-family%3D%22Arial%22 font-size%3D%2220%22 fill%3D%22%234CAF50%22%3E响应时间图%3C%2Ftext%3E%3C%2Fsvg%3E'">
            </div>
            <div class="chart">
                <div class="chart-title">服务器负载</div>
                <img src="/static/images/server_load.png" alt="服务器负载图" onerror="this.src='data:image/svg+xml;charset=utf-8,%3Csvg xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22 width%3D%22300%22 height%3D%22250%22%3E%3Crect width%3D%22300%22 height%3D%22250%22 fill%3D%22%23f1f8e9%22%3E%3C%2Frect%3E%3Ctext x%3D%2250%22 y%3D%22125%22 font-family%3D%22Arial%22 font-size%3D%2220%22 fill%3D%22%234CAF50%22%3E服务器负载图%3C%2Ftext%3E%3C%2Fsvg%3E'">
            </div>
        </div>

        <div class="highlight">
            <div class="highlight-title">性能分析</div>
            <p>系统平均响应时间为235毫秒，较上季度提升12.3%。95%的请求响应时间在500毫秒以内，99%的请求响应时间在800毫秒以内。</p>
            <p>服务器平均CPU利用率为42%，峰值为68%，内存利用率平均为56%，峰值为75%，均在合理范围内。</p>
            <p>数据库查询平均响应时间为85毫秒，较上季度提升8.5%，主要得益于数据库索引优化和查询语句优化。</p>
        </div>

        <h2>2.2 系统可用性</h2>
        <table>
            <thead>
                <tr>
                    <th>月份</th>
                    <th>计划内停机时间</th>
                    <th>计划外停机时间</th>
                    <th>可用率</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>1月</td>
                    <td>2小时</td>
                    <td>0</td>
                    <td>99.7%</td>
                </tr>
                <tr>
                    <td>2月</td>
                    <td>1.5小时</td>
                    <td>0.5小时</td>
                    <td>99.7%</td>
                </tr>
                <tr>
                    <td>3月</td>
                    <td>1小时</td>
                    <td>0</td>
                    <td>99.9%</td>
                </tr>
                <tr>
                    <td><strong>季度总计</strong></td>
                    <td><strong>4.5小时</strong></td>
                    <td><strong>0.5小时</strong></td>
                    <td><strong>99.8%</strong></td>
                </tr>
            </tbody>
        </table>

        <h1>3. 安全状况</h1>

        <h2>3.1 安全事件统计</h2>
        <table>
            <thead>
                <tr>
                    <th>事件类型</th>
                    <th>数量</th>
                    <th>处理状态</th>
                    <th>影响程度</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>登录尝试失败</td>
                    <td>256次</td>
                    <td>已处理</td>
                    <td>无影响</td>
                </tr>
                <tr>
                    <td>SQL注入尝试</td>
                    <td>15次</td>
                    <td>已处理</td>
                    <td>无影响</td>
                </tr>
                <tr>
                    <td>XSS攻击尝试</td>
                    <td>8次</td>
                    <td>已处理</td>
                    <td>无影响</td>
                </tr>
                <tr>
                    <td>DDoS攻击尝试</td>
                    <td>2次</td>
                    <td>已处理</td>
                    <td>轻微影响</td>
                </tr>
                <tr>
                    <td>未授权访问尝试</td>
                    <td>35次</td>
                    <td>已处理</td>
                    <td>无影响</td>
                </tr>
            </tbody>
        </table>

        <h2>3.2 系统漏洞修复</h2>
        <p>本季度共发现系统漏洞5个，其中高危漏洞1个，中危漏洞2个，低危漏洞2个。所有漏洞均已修复，平均修复时间为1.5天。</p>
        <p>主要漏洞包括：</p>
        <ul>
            <li>文件上传验证不严格（高危）- 已修复</li>
            <li>会话管理机制存在缺陷（中危）- 已修复</li>
            <li>部分API缺少访问控制（中危）- 已修复</li>
            <li>日志记录不完整（低危）- 已修复</li>
            <li>密码策略不够严格（低危）- 已修复</li>
        </ul>

        <h1>4. 区块链同步情况</h1>
        <div class="highlight">
            <div class="highlight-title">区块链同步状态</div>
            <p><span class="status-indicator status-good"></span> <strong>同步状态：正常</strong></p>
            <p>本季度共同步区块325个，包含交易记录325条，同步成功率为99.7%。</p>
            <p>区块链网络平均确认时间为15秒，较上季度提升10.5%。</p>
            <p>区块链存储的数据包括：排放数据哈希值、核查结果哈希值、交易记录和配额分配记录。</p>
        </div>

        <table>
            <thead>
                <tr>
                    <th>数据类型</th>
                    <th>同步数量</th>
                    <th>同步成功率</th>
                    <th>平均确认时间</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>排放数据哈希</td>
                    <td>128个</td>
                    <td>100%</td>
                    <td>12秒</td>
                </tr>
                <tr>
                    <td>核查结果哈希</td>
                    <td>85个</td>
                    <td>100%</td>
                    <td>13秒</td>
                </tr>
                <tr>
                    <td>交易记录</td>
                    <td>95条</td>
                    <td>98.9%</td>
                    <td>18秒</td>
                </tr>
                <tr>
                    <td>配额分配记录</td>
                    <td>17条</td>
                    <td>100%</td>
                    <td>15秒</td>
                </tr>
            </tbody>
        </table>

        <h1>5. 系统改进建议</h1>
        <ol>
            <li>优化数据库查询性能，特别是大数据量报表生成时的查询效率</li>
            <li>增强系统监控能力，实现更精细化的性能监控和预警</li>
            <li>提升区块链交易处理能力，减少确认时间</li>
            <li>完善系统安全防护机制，特别是针对DDoS攻击的防护</li>
            <li>优化用户界面，提升用户体验，特别是移动端的适配性</li>
        </ol>

        <div class="footer">
            <div class="footer-left">
                <div class="footer-title">碳排放管理系统运维团队</div>
                <div class="footer-info">联系人：吴宏</div>
                <div class="footer-info">电话：010-12345678</div>
                <div class="footer-info">邮箱：<EMAIL></div>
            </div>
            <div class="footer-right">
                <div class="footer-info">报告编号：SYS-RPT-2023-Q1</div>
                <div class="footer-info">生成日期：2025年5月14日</div>
                <div class="stamp">
                    <img src="/static/images/admin_stamp.png" alt="管理员公章" onerror="this.src='data:image/svg+xml;charset=utf-8,%3Csvg xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22 width%3D%22120%22 height%3D%22120%22%3E%3Ccircle cx%3D%2260%22 cy%3D%2260%22 r%3D%2255%22 fill%3D%22none%22 stroke%3D%22%23c0392b%22 stroke-width%3D%222%22%3E%3C%2Fcircle%3E%3Ctext x%3D%2240%22 y%3D%2250%22 font-family%3D%22SimSun%22 font-size%3D%2210%22 fill%3D%22%23c0392b%22%3E碳排放管理%3C%2Ftext%3E%3Ctext x%3D%2240%22 y%3D%2270%22 font-family%3D%22SimSun%22 font-size%3D%2210%22 fill%3D%22%23c0392b%22%3E系统管理员%3C%2Ftext%3E%3Ctext x%3D%2245%22 y%3D%2290%22 font-family%3D%22SimSun%22 font-size%3D%2210%22 fill%3D%22%23c0392b%22%3E公章%3C%2Ftext%3E%3C%2Fsvg%3E'">
                </div>
            </div>
        </div>
    </div>
</body>
</html>
