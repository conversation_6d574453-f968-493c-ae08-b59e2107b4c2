@echo off
echo 初始化测试环境...
echo.

echo 步骤1: 检查Ganache是否运行...
python -c "from web3 import Web3; w3 = Web3(Web3.HTTPProvider('http://127.0.0.1:8545')); print('Ganache状态: ' + ('运行中' if w3.is_connected() else '未运行'))"
if %ERRORLEVEL% NEQ 0 (
    echo 请确保Ganache已启动并运行在http://127.0.0.1:8545
    echo 您可以从https://trufflesuite.com/ganache/下载Ganache
    pause
    exit /b 1
)

echo.
echo 步骤2: 初始化测试数据...
python tests\init_test_data.py
if %ERRORLEVEL% NEQ 0 (
    echo 初始化测试数据失败
    pause
    exit /b 1
)

echo.
echo 步骤3: 测试区块链连接...
python tests\test_blockchain_connection.py
if %ERRORLEVEL% NEQ 0 (
    echo 测试区块链连接失败
    pause
    exit /b 1
)

echo.
echo 测试环境初始化完成！
echo.
echo 您现在可以启动系统并使用以下测试账户登录:
echo 管理员账户: admin / password123
echo 企业账户1: enterprise1 / password123
echo 企业账户2: enterprise2 / password123
echo 企业账户3: enterprise3 / password123
echo 核查机构账户1: verifier1 / password123
echo 核查机构账户2: verifier2 / password123
echo.

echo 启动系统...
start python run.py

echo.
echo 请在浏览器中访问: http://localhost:5000
echo.
pause
