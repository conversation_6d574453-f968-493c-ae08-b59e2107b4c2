<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放核查系统 - 添加排放数据</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <style>
        .gradient-custom {
            background: linear-gradient(135deg, #43a047 0%, #1de9b6 100%);
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            margin-bottom: 20px;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }
        .card-header {
            border-radius: 15px 15px 0 0 !important;
            background: linear-gradient(135deg, #43a047 0%, #1de9b6 100%) !important;
            color: white;
            font-weight: 600;
            padding: 1rem 1.5rem;
            border: none;
        }
        .btn-success {
            background: linear-gradient(135deg, #43a047 0%, #1de9b6 100%);
            border: none;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #388e3c 0%, #00bfa5 100%);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }
        .form-control:focus {
            border-color: #43a047;
            box-shadow: 0 0 0 0.25rem rgba(67, 160, 71, 0.25);
        }
        .form-label {
            font-weight: 500;
            color: #444;
        }
        .form-text {
            color: #666;
        }
        .calculation-methods {
            display: none;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container">
            <a class="navbar-brand" href="/"><i class="fas fa-leaf me-2"></i>碳排放核查系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise"><i class="fas fa-tachometer-alt me-1"></i>仪表板</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/enterprise/emissions"><i class="fas fa-cloud me-1"></i>排放管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise/transactions"><i class="fas fa-exchange-alt me-1"></i>交易管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise/reports"><i class="fas fa-file-alt me-1"></i>报告管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise/blockchain/config"><i class="fas fa-link me-1"></i>区块链配置</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>{{ current_user.get('username', '企业用户') }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="/enterprise/profile"><i class="fas fa-id-card me-1"></i>企业资料</a></li>
                            <li><a class="dropdown-item" href="/enterprise/settings"><i class="fas fa-cog me-1"></i>账户设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-1"></i>退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mt-4">
        <!-- 消息提示 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-plus-circle me-2"></i>添加排放数据</h1>
            <a href="/enterprise/emissions" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>返回列表
            </a>
        </div>

        <!-- 添加排放数据表单 -->
        <div class="card">
            <div class="card-header gradient-custom">
                <h5 class="card-title mb-0"><i class="fas fa-edit me-2"></i>排放数据表单</h5>
            </div>
            <div class="card-body">
                <form id="emissionForm" method="post" action="/enterprise/emissions/add" enctype="multipart/form-data">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="emission_source" class="form-label">排放源类型 <span class="text-danger">*</span></label>
                            <select class="form-select" id="emission_source" name="emission_source" required>
                                <option value="">请选择排放源类型</option>
                                <option value="电力消耗">电力消耗</option>
                                <option value="燃料燃烧">燃料燃烧</option>
                                <option value="工业过程">工业过程</option>
                                <option value="废弃物处理">废弃物处理</option>
                                <option value="交通运输">交通运输</option>
                                <option value="其他">其他</option>
                            </select>
                            <div class="form-text">选择排放源的类型</div>
                        </div>
                        <div class="col-md-6">
                            <label for="calculation_method" class="form-label">计算方法 <span class="text-danger">*</span></label>
                            <select class="form-select" id="calculation_method" name="calculation_method" required>
                                <option value="">请选择计算方法</option>
                                <option value="排放因子法">排放因子法</option>
                                <option value="物料平衡法">物料平衡法</option>
                                <option value="直接测量法">直接测量法</option>
                                <option value="其他">其他</option>
                            </select>
                            <div class="form-text">选择排放量的计算方法</div>
                        </div>
                    </div>

                    <!-- 排放因子法计算表单 -->
                    <div id="emission_factor_method" class="calculation-methods">
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="activity_data" class="form-label">活动数据 <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="activity_data" name="activity_data" step="0.01">
                                <div class="form-text">例如：消耗的电量、燃料量等</div>
                            </div>
                            <div class="col-md-4">
                                <label for="activity_unit" class="form-label">活动数据单位 <span class="text-danger">*</span></label>
                                <select class="form-select" id="activity_unit" name="activity_unit">
                                    <option value="kWh">千瓦时 (kWh)</option>
                                    <option value="MWh">兆瓦时 (MWh)</option>
                                    <option value="L">升 (L)</option>
                                    <option value="kg">千克 (kg)</option>
                                    <option value="t">吨 (t)</option>
                                    <option value="m³">立方米 (m³)</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="emission_factor" class="form-label">排放因子 <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="emission_factor" name="emission_factor" step="0.0001">
                                <div class="form-text">单位：tCO2e/活动单位</div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="emission_amount" class="form-label">排放量 <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="emission_amount" name="emission_amount" step="0.01" required>
                            <div class="form-text">排放量数值</div>
                        </div>
                        <div class="col-md-6">
                            <label for="emission_unit" class="form-label">排放单位 <span class="text-danger">*</span></label>
                            <select class="form-select" id="emission_unit" name="emission_unit" required>
                                <option value="tCO2e">吨二氧化碳当量 (tCO2e)</option>
                                <option value="kgCO2e">千克二氧化碳当量 (kgCO2e)</option>
                            </select>
                            <div class="form-text">排放量的计量单位</div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="emission_period_start" class="form-label">排放起始日期 <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="emission_period_start" name="emission_period_start" required>
                            <div class="form-text">排放数据的起始日期</div>
                        </div>
                        <div class="col-md-6">
                            <label for="emission_period_end" class="form-label">排放结束日期 <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="emission_period_end" name="emission_period_end" required>
                            <div class="form-text">排放数据的结束日期</div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="proof_file" class="form-label">证明文件</label>
                        <input type="file" class="form-control" id="proof_file" name="proof_file">
                        <div class="form-text">上传支持排放数据的证明文件（如电费单、燃料购买发票等）</div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">描述</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        <div class="form-text">添加关于此排放数据的额外说明</div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-outline-secondary" onclick="window.location.href='/enterprise/emissions'">
                            <i class="fas fa-times me-1"></i>取消
                        </button>
                        <div>
                            <button type="submit" class="btn btn-primary me-2" name="action" value="save">
                                <i class="fas fa-save me-1"></i>保存为草稿
                            </button>
                            <button type="submit" class="btn btn-success" name="action" value="submit">
                                <i class="fas fa-paper-plane me-1"></i>保存并提交
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">© 2025 碳排放核查系统 | 基于区块链技术</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 计算方法选择事件
            const calculationMethodSelect = document.getElementById('calculation_method');
            calculationMethodSelect.addEventListener('change', function() {
                // 隐藏所有计算方法表单
                document.querySelectorAll('.calculation-methods').forEach(form => {
                    form.style.display = 'none';
                });
                
                // 显示选中的计算方法表单
                if (this.value === '排放因子法') {
                    document.getElementById('emission_factor_method').style.display = 'block';
                }
            });
            
            // 排放因子法自动计算
            const activityDataInput = document.getElementById('activity_data');
            const emissionFactorInput = document.getElementById('emission_factor');
            const emissionAmountInput = document.getElementById('emission_amount');
            
            function calculateEmission() {
                const activityData = parseFloat(activityDataInput.value) || 0;
                const emissionFactor = parseFloat(emissionFactorInput.value) || 0;
                const emissionAmount = activityData * emissionFactor;
                
                if (activityData > 0 && emissionFactor > 0) {
                    emissionAmountInput.value = emissionAmount.toFixed(2);
                }
            }
            
            activityDataInput.addEventListener('input', calculateEmission);
            emissionFactorInput.addEventListener('input', calculateEmission);
            
            // 设置默认日期
            const today = new Date();
            const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
            const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
            
            document.getElementById('emission_period_start').value = firstDayOfMonth.toISOString().split('T')[0];
            document.getElementById('emission_period_end').value = lastDayOfMonth.toISOString().split('T')[0];
        });
    </script>
</body>
</html>
