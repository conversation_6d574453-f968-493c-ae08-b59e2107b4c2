import React, { useState, useEffect } from 'react';
import axios from 'axios';
import '../styles/BlockchainVerification.css';

/**
 * 区块链数据验证组件
 * 用于验证数据库中的数据与区块链上的数据是否一致
 */
const BlockchainVerification = ({ dataId, dataType }) => {
  const [verificationResult, setVerificationResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [expanded, setExpanded] = useState(false);

  useEffect(() => {
    if (dataId && dataType) {
      verifyData();
    }
  }, [dataId, dataType]);

  const verifyData = async () => {
    setLoading(true);
    setError('');
    
    try {
      const response = await axios.get(`/api/blockchain/verify`, {
        params: { data_id: dataId, data_type: dataType },
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      
      setVerificationResult(response.data);
    } catch (err) {
      setError('数据验证失败');
      console.error('数据验证失败:', err);
    } finally {
      setLoading(false);
    }
  };

  // 获取验证状态图标和颜色
  const getStatusIcon = () => {
    if (!verificationResult) return { icon: 'fa-question-circle', color: '#9e9e9e' };
    
    if (verificationResult.verified) {
      return { icon: 'fa-check-circle', color: '#4caf50' };
    } else {
      return { icon: 'fa-times-circle', color: '#f44336' };
    }
  };

  const statusIcon = getStatusIcon();

  // 获取数据类型标签
  const getDataTypeLabel = () => {
    switch (dataType) {
      case 'emission':
        return '排放数据';
      case 'verification':
        return '核查结果';
      case 'transaction':
        return '交易记录';
      case 'penalty':
        return '惩罚记录';
      default:
        return '未知类型';
    }
  };

  return (
    <div className="blockchain-verification">
      <div className="verification-header" onClick={() => setExpanded(!expanded)}>
        <div className="verification-status">
          {loading ? (
            <i className="fas fa-spinner fa-spin"></i>
          ) : (
            <i className={`fas ${statusIcon.icon}`} style={{ color: statusIcon.color }}></i>
          )}
        </div>
        <div className="verification-title">
          <span>区块链数据验证</span>
          <span className="verification-data-type">{getDataTypeLabel()}</span>
        </div>
        <div className="verification-toggle">
          <i className={`fas ${expanded ? 'fa-chevron-up' : 'fa-chevron-down'}`}></i>
        </div>
      </div>
      
      {expanded && (
        <div className="verification-details">
          {error && (
            <div className="verification-error">
              <i className="fas fa-exclamation-triangle"></i>
              <span>{error}</span>
            </div>
          )}
          
          {verificationResult && (
            <>
              <div className="verification-result">
                <span className="result-label">验证结果:</span>
                <span className="result-value" style={{ color: verificationResult.verified ? '#4caf50' : '#f44336' }}>
                  {verificationResult.verified ? '数据一致' : '数据不一致'}
                </span>
              </div>
              
              {verificationResult.blockchain_data && (
                <div className="blockchain-data">
                  <h4>区块链数据</h4>
                  <div className="data-table">
                    {Object.entries(verificationResult.blockchain_data).map(([key, value]) => (
                      <div className="data-row" key={key}>
                        <span className="data-key">{key}:</span>
                        <span className="data-value">
                          {typeof value === 'object' ? JSON.stringify(value) : value.toString()}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              {verificationResult.differences && verificationResult.differences.length > 0 && (
                <div className="data-differences">
                  <h4>数据差异</h4>
                  <ul>
                    {verificationResult.differences.map((diff, index) => (
                      <li key={index}>
                        <span className="diff-field">{diff.field}:</span>
                        <span className="diff-db">数据库: {diff.db_value}</span>
                        <span className="diff-blockchain">区块链: {diff.blockchain_value}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </>
          )}
          
          <div className="verification-actions">
            <button className="btn-verify" onClick={verifyData} disabled={loading}>
              {loading ? '验证中...' : '重新验证'}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default BlockchainVerification;
