"""
报告模型
"""

import json
from datetime import datetime
from backend import db
from backend.models.user import User

class Report(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    enterprise_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>('user.id'))
    title = db.Column(db.String(200), nullable=False)
    report_type = db.Column(db.String(50), nullable=False)  # emission, trading, compliance
    content = db.Column(db.Text)  # 存储为JSON字符串或HTML
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    period_start = db.Column(db.Date)
    period_end = db.Column(db.Date)
    file_path = db.Column(db.String(255))

    enterprise = db.relationship('User', backref=db.backref('reports', lazy=True))

    def set_content(self, data):
        if isinstance(data, dict):
            self.content = json.dumps(data)
        else:
            self.content = data

    def get_content(self):
        try:
            return json.loads(self.content)
        except:
            return self.content

    def to_dict(self):
        return {
            'id': self.id,
            'enterprise_id': self.enterprise_id,
            'enterprise_name': self.enterprise.company_name if self.enterprise else None,
            'title': self.title,
            'report_type': self.report_type,
            'content': self.get_content(),
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'period_start': self.period_start.isoformat() if self.period_start else None,
            'period_end': self.period_end.isoformat() if self.period_end else None,
            'file_path': self.file_path
        }
