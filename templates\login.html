{% extends 'base.html' %}

{% block title %}碳排放核查系统 - 登录{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">系统登录</h4>
                </div>
                <div class="card-body">
                    {% if error_message %}
                    <div class="alert alert-danger" role="alert">
                        {{ error_message }}
                    </div>
                    {% endif %}
                    
                    <form method="post" action="/login">
                        <div class="mb-3">
                            <label for="username" class="form-label">用户名</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">密码</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">登录</button>
                        </div>
                    </form>
                    
                    <div class="mt-3 text-center">
                        <p>没有账号？<a href="/register">注册</a></p>
                    </div>
                    
                    <div class="mt-4">
                        <div class="alert alert-info" role="alert">
                            <h5 class="alert-heading">测试账户</h5>
                            <p><strong>管理员账户：</strong> 用户名: admin, 密码: admin123</p>
                            <p><strong>企业账户：</strong> 用户名: enterprise1, 密码: enterprise123</p>
                            <p><strong>核查机构账户：</strong> 用户名: verifier1, 密码: verifier123</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
