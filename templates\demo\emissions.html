<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放管理系统 - 排放数据</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/unified-nav.css') }}">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background: linear-gradient(to right, #1B5E20, #388E3C);
            color: white;
            padding: 20px 0;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-name {
            margin-right: 15px;
        }
        .logout-btn {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 30px;
            cursor: pointer;
            text-decoration: none;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .logout-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        nav {
            background: linear-gradient(to right, #2E7D32, #43A047);
            padding: 10px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
        }
        .nav-item {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            margin-right: 5px;
            border-radius: 30px;
            transition: all 0.3s ease;
        }
        .nav-item:hover, .nav-item.active {
            background: linear-gradient(to right, #1B5E20, #2E7D32);
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        .card {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            transform: translateY(-5px);
        }
        .card-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        .btn-primary {
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            color: white;
        }
        .btn-success {
            background: linear-gradient(to right, #2ecc71, #27ae60);
            color: white;
        }
        .btn-danger {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            display: inline-block;
        }
        .status-pending {
            background: linear-gradient(to right, #f39c12, #e67e22);
            color: white;
        }
        .status-verified {
            background: linear-gradient(to right, #2ecc71, #27ae60);
            color: white;
        }
        .status-rejected {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
        }
        .blockchain-info {
            font-size: 12px;
            color: #7f8c8d;
            margin-top: 5px;
        }
        .blockchain-hash {
            font-family: monospace;
            background: linear-gradient(to right, #f1f8e9, #dcedc8);
            padding: 3px 8px;
            border-radius: 15px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            display: inline-block;
        }
        .filters {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }
        .filter-group {
            display: flex;
            align-items: center;
        }
        .filter-group label {
            margin-right: 5px;
            font-weight: bold;
        }
        .filter-group select {
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 30px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .filter-group select:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 2px 10px rgba(76, 175, 80, 0.2);
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo">碳排放管理系统</div>
            <div class="user-info">
                <span class="user-name">北京碳排放科技有限公司</span>
                <a href="/login" class="logout-btn">退出登录</a>
            </div>
        </div>
    </header>

    <div class="demo-nav">
        <div class="nav-content">
            <a href="/dashboard" class="nav-item"><i class="fas fa-tachometer-alt mr-2"></i>仪表板</a>
            <a href="/emissions" class="nav-item active"><i class="fas fa-cloud mr-2"></i>排放数据</a>
            <a href="/verifications" class="nav-item"><i class="fas fa-clipboard-check mr-2"></i>核查记录</a>
            <a href="/transactions" class="nav-item"><i class="fas fa-exchange-alt mr-2"></i>碳交易</a>
            <a href="/calculator" class="nav-item"><i class="fas fa-calculator mr-2"></i>碳计算器</a>
            <a href="/predictions" class="nav-item"><i class="fas fa-chart-line mr-2"></i>预测分析</a>
            <a href="/reports" class="nav-item"><i class="fas fa-file-alt mr-2"></i>报告生成</a>
            <a href="/enterprise_blockchain_config" class="nav-item"><i class="fas fa-link mr-2"></i>区块链配置</a>
        </div>
    </div>

    <div class="container">
        <h1>排放数据管理</h1>

        <div class="filters">
            <div class="filter-group">
                <label for="year-filter">年份:</label>
                <select id="year-filter">
                    <option value="all">全部</option>
                    <option value="2023" selected>2023</option>
                    <option value="2022">2022</option>
                    <option value="2021">2021</option>
                </select>
            </div>
            <div class="filter-group">
                <label for="status-filter">状态:</label>
                <select id="status-filter">
                    <option value="all">全部</option>
                    <option value="pending">待核查</option>
                    <option value="verified">已核查</option>
                    <option value="rejected">已拒绝</option>
                </select>
            </div>
            <div class="filter-group">
                <label for="source-filter">排放源:</label>
                <select id="source-filter">
                    <option value="all">全部</option>
                    <option value="coal">燃煤锅炉</option>
                    <option value="gas">天然气锅炉</option>
                    <option value="process">工业生产过程</option>
                    <option value="transport">交通运输</option>
                    <option value="electricity">电力消耗</option>
                </select>
            </div>
        </div>

        <div class="card">
            <div class="card-title">
                <span>排放数据列表</span>
                <a href="/emission_submit" class="btn btn-primary">提交新数据</a>
            </div>
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>排放源</th>
                        <th>排放量</th>
                        <th>计算方法</th>
                        <th>排放期间</th>
                        <th>提交时间</th>
                        <th>状态</th>
                        <th>区块链记录</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1001</td>
                        <td>燃煤锅炉</td>
                        <td>450 吨CO2e</td>
                        <td>排放因子法</td>
                        <td>2023-01-01 至 2023-01-31</td>
                        <td>2023-02-05</td>
                        <td><span class="status status-verified">已核查</span></td>
                        <td>
                            <div class="blockchain-info">
                                哈希: <span class="blockchain-hash">0x8f7d...</span>
                                <br>
                                区块: #15782364
                            </div>
                        </td>
                        <td><a href="/emission_detail?id=1001" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>1002</td>
                        <td>天然气锅炉</td>
                        <td>320 吨CO2e</td>
                        <td>排放因子法</td>
                        <td>2023-02-01 至 2023-02-28</td>
                        <td>2023-03-10</td>
                        <td><span class="status status-pending">待核查</span></td>
                        <td>
                            <div class="blockchain-info">
                                哈希: <span class="blockchain-hash">0x7e6c...</span>
                                <br>
                                区块: #15796421
                            </div>
                        </td>
                        <td><a href="/emission_detail?id=1002" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>1003</td>
                        <td>工业生产过程</td>
                        <td>780 吨CO2e</td>
                        <td>物料平衡法</td>
                        <td>2023-03-01 至 2023-03-31</td>
                        <td>2023-04-08</td>
                        <td><span class="status status-rejected">已拒绝</span></td>
                        <td>
                            <div class="blockchain-info">
                                哈希: <span class="blockchain-hash">0x9a3b...</span>
                                <br>
                                区块: #15823789
                            </div>
                        </td>
                        <td><a href="/emission_detail?id=1003" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>1004</td>
                        <td>交通运输</td>
                        <td>250 吨CO2e</td>
                        <td>排放因子法</td>
                        <td>2023-04-01 至 2023-04-30</td>
                        <td>2023-05-05</td>
                        <td><span class="status status-verified">已核查</span></td>
                        <td>
                            <div class="blockchain-info">
                                哈希: <span class="blockchain-hash">0xb2c5...</span>
                                <br>
                                区块: #15847632
                            </div>
                        </td>
                        <td><a href="/emission_detail?id=1004" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>1005</td>
                        <td>电力消耗</td>
                        <td>520 吨CO2e</td>
                        <td>排放因子法</td>
                        <td>2023-04-01 至 2023-04-30</td>
                        <td>2023-05-10</td>
                        <td><span class="status status-pending">待核查</span></td>
                        <td>
                            <div class="blockchain-info">
                                哈希: <span class="blockchain-hash">0xc4d7...</span>
                                <br>
                                区块: #15862145
                            </div>
                        </td>
                        <td><a href="/emission_detail?id=1005" class="btn btn-primary">查看</a></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 筛选功能
            const yearFilter = document.getElementById('year-filter');
            const statusFilter = document.getElementById('status-filter');
            const sourceFilter = document.getElementById('source-filter');

            // 这里只是演示，实际应用中应该根据筛选条件过滤数据
            yearFilter.addEventListener('change', function() {
                console.log('年份筛选:', this.value);
            });

            statusFilter.addEventListener('change', function() {
                console.log('状态筛选:', this.value);
            });

            sourceFilter.addEventListener('change', function() {
                console.log('排放源筛选:', this.value);
            });

            // 添加排放数据图表
            const chartContainer = document.createElement('div');
            chartContainer.style.height = '300px';
            chartContainer.style.marginBottom = '30px';
            chartContainer.innerHTML = '<canvas id="emissionsChart"></canvas>';

            // 将图表插入到筛选器下方
            const filters = document.querySelector('.filters');
            filters.parentNode.insertBefore(chartContainer, filters.nextSibling);

            // 创建图表
            const ctx = document.getElementById('emissionsChart').getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月'],
                    datasets: [
                        {
                            label: '排放量 (吨CO2e)',
                            data: [450, 320, 780, 250, 520],
                            backgroundColor: 'rgba(46, 204, 113, 0.8)',
                            borderColor: 'rgba(46, 204, 113, 1)',
                            borderWidth: 1
                        },
                        {
                            label: '核查状态',
                            data: [1, 0, -1, 1, 0],
                            type: 'line',
                            backgroundColor: 'rgba(0, 0, 0, 0)',
                            borderColor: 'rgba(52, 152, 219, 1)',
                            borderWidth: 2,
                            pointBackgroundColor: function(context) {
                                const value = context.dataset.data[context.dataIndex];
                                if (value === 1) return 'rgba(46, 204, 113, 1)'; // 已核查
                                if (value === 0) return 'rgba(243, 156, 18, 1)'; // 待核查
                                return 'rgba(231, 76, 60, 1)'; // 已拒绝
                            },
                            pointRadius: 6,
                            pointHoverRadius: 8,
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '排放量 (吨CO2e)'
                            }
                        },
                        y1: {
                            position: 'right',
                            min: -1.5,
                            max: 1.5,
                            ticks: {
                                callback: function(value) {
                                    if (value === 1) return '已核查';
                                    if (value === 0) return '待核查';
                                    if (value === -1) return '已拒绝';
                                    return '';
                                }
                            },
                            grid: {
                                drawOnChartArea: false
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: '2023年排放数据统计',
                            font: {
                                size: 16
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const datasetIndex = context.datasetIndex;
                                    const dataIndex = context.dataIndex;
                                    const value = context.raw;

                                    if (datasetIndex === 0) {
                                        return `排放量: ${value} 吨CO2e`;
                                    } else {
                                        if (value === 1) return '状态: 已核查';
                                        if (value === 0) return '状态: 待核查';
                                        return '状态: 已拒绝';
                                    }
                                }
                            }
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>
