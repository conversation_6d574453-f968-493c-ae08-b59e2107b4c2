<!--
此文件已废弃，请使用 templates/blockchain_config.html 替代
此文件可以安全删除
-->

<template>
  <div class="blockchain-config">
    <div class="card shadow">
      <div class="card-header bg-primary text-white">
        <h4 class="mb-0">区块链配置</h4>
      </div>
      <div class="card-body">
        <div v-if="successMessage" class="alert alert-success" role="alert">
          {{ successMessage }}
        </div>

        <div v-if="errorMessage" class="alert alert-danger" role="alert">
          {{ errorMessage }}
        </div>

        <form @submit.prevent="saveConfig">
          <div class="mb-4">
            <h5 class="border-bottom pb-2">区块链连接信息</h5>
            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="ethereum_node_url" class="form-label">以太坊节点URL</label>
                  <input type="text" class="form-control" id="ethereum_node_url" v-model="config.ethereum_node_url" required>
                  <div class="form-text">Ganache默认URL: http://127.0.0.1:8545</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="contract_address" class="form-label">合约地址</label>
                  <input type="text" class="form-control" id="contract_address" v-model="config.contract_address" placeholder="部署合约后自动填充">
                  <div class="form-text">部署合约后会自动更新</div>
                </div>
              </div>
            </div>
          </div>

          <div class="mb-4">
            <h5 class="border-bottom pb-2">管理员账户</h5>
            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="admin_address" class="form-label">管理员地址</label>
                  <input type="text" class="form-control" id="admin_address" v-model="config.admin_address" required>
                  <div class="form-text">Ganache中第1个账户的地址</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="admin_private_key" class="form-label">管理员私钥</label>
                  <input type="text" class="form-control" id="admin_private_key" v-model="config.admin_private_key" required>
                  <div class="form-text">Ganache中第1个账户的私钥</div>
                </div>
              </div>
            </div>
          </div>

          <div class="mb-4">
            <h5 class="border-bottom pb-2">企业账户</h5>

            <!-- 企业1 -->
            <div class="row mb-3">
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="enterprise_1_address" class="form-label">企业1地址</label>
                  <input type="text" class="form-control" id="enterprise_1_address" v-model="config.enterprise_1_address" required>
                  <div class="form-text">Ganache中第2个账户的地址</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="enterprise_1_key" class="form-label">企业1私钥</label>
                  <input type="text" class="form-control" id="enterprise_1_key" v-model="config.enterprise_1_key" required>
                  <div class="form-text">Ganache中第2个账户的私钥</div>
                </div>
              </div>
            </div>

            <!-- 企业2 -->
            <div class="row mb-3">
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="enterprise_2_address" class="form-label">企业2地址</label>
                  <input type="text" class="form-control" id="enterprise_2_address" v-model="config.enterprise_2_address" required>
                  <div class="form-text">Ganache中第3个账户的地址</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="enterprise_2_key" class="form-label">企业2私钥</label>
                  <input type="text" class="form-control" id="enterprise_2_key" v-model="config.enterprise_2_key" required>
                  <div class="form-text">Ganache中第3个账户的私钥</div>
                </div>
              </div>
            </div>

            <!-- 企业3 -->
            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="enterprise_3_address" class="form-label">企业3地址</label>
                  <input type="text" class="form-control" id="enterprise_3_address" v-model="config.enterprise_3_address" required>
                  <div class="form-text">Ganache中第4个账户的地址</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="enterprise_3_key" class="form-label">企业3私钥</label>
                  <input type="text" class="form-control" id="enterprise_3_key" v-model="config.enterprise_3_key" required>
                  <div class="form-text">Ganache中第4个账户的私钥</div>
                </div>
              </div>
            </div>
          </div>

          <div class="mb-4">
            <h5 class="border-bottom pb-2">核查机构账户</h5>

            <!-- 核查机构1 -->
            <div class="row mb-3">
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="verifier_1_address" class="form-label">核查机构1地址</label>
                  <input type="text" class="form-control" id="verifier_1_address" v-model="config.verifier_1_address" required>
                  <div class="form-text">Ganache中第5个账户的地址</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="verifier_1_key" class="form-label">核查机构1私钥</label>
                  <input type="text" class="form-control" id="verifier_1_key" v-model="config.verifier_1_key" required>
                  <div class="form-text">Ganache中第5个账户的私钥</div>
                </div>
              </div>
            </div>

            <!-- 核查机构2 -->
            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="verifier_2_address" class="form-label">核查机构2地址</label>
                  <input type="text" class="form-control" id="verifier_2_address" v-model="config.verifier_2_address" required>
                  <div class="form-text">Ganache中第6个账户的地址</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="verifier_2_key" class="form-label">核查机构2私钥</label>
                  <input type="text" class="form-control" id="verifier_2_key" v-model="config.verifier_2_key" required>
                  <div class="form-text">Ganache中第6个账户的私钥</div>
                </div>
              </div>
            </div>
          </div>

          <div class="d-flex justify-content-between">
            <button type="submit" class="btn btn-primary">保存配置</button>
            <div>
              <button type="button" class="btn btn-info me-2" @click="fetchGanacheAccounts">获取Ganache账户</button>
              <button type="button" class="btn btn-success" @click="deployContract">部署智能合约</button>
            </div>
          </div>
        </form>
      </div>
    </div>

    <div v-if="ganacheAccounts.length > 0" class="card shadow mt-4">
      <div class="card-header bg-info text-white">
        <h4 class="mb-0">Ganache账户信息</h4>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>账户</th>
                <th>地址</th>
                <th>余额</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(account, index) in ganacheAccounts" :key="index">
                <td>账户 {{ index + 1 }}</td>
                <td>{{ account.address }}</td>
                <td>{{ account.balance }} ETH</td>
                <td>
                  <button class="btn btn-sm btn-outline-primary" @click="copyAddress(account.address)">
                    复制地址
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="alert alert-warning mt-3">
          <strong>注意:</strong> Ganache提供的账户私钥可以在Ganache UI界面中查看。请从Ganache界面获取私钥并填写到上面的表单中。
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'BlockchainConfig',
  data() {
    return {
      config: {
        ethereum_node_url: 'http://127.0.0.1:8545',
        contract_address: '',
        admin_address: '',
        admin_private_key: '',
        enterprise_1_address: '',
        enterprise_1_key: '',
        enterprise_2_address: '',
        enterprise_2_key: '',
        enterprise_3_address: '',
        enterprise_3_key: '',
        verifier_1_address: '',
        verifier_1_key: '',
        verifier_2_address: '',
        verifier_2_key: ''
      },
      ganacheAccounts: [],
      successMessage: '',
      errorMessage: '',
      loading: false
    };
  },
  mounted() {
    this.fetchConfig();
  },
  methods: {
    async fetchConfig() {
      try {
        this.loading = true;
        const response = await axios.get('/api/blockchain/config');

        // 转换API响应格式为组件格式
        this.config = {
          ethereum_node_url: response.data.ETHEREUM_NODE_URL || 'http://127.0.0.1:8545',
          contract_address: response.data.CONTRACT_ADDRESS || '',
          admin_address: response.data.ADMIN_ADDRESS || '',
          admin_private_key: response.data.ADMIN_PRIVATE_KEY || '',
          enterprise_1_address: response.data.ENTERPRISE_1_ADDRESS || '',
          enterprise_1_key: response.data.ENTERPRISE_1_KEY || '',
          enterprise_2_address: response.data.ENTERPRISE_2_ADDRESS || '',
          enterprise_2_key: response.data.ENTERPRISE_2_KEY || '',
          enterprise_3_address: response.data.ENTERPRISE_3_ADDRESS || '',
          enterprise_3_key: response.data.ENTERPRISE_3_KEY || '',
          verifier_1_address: response.data.VERIFIER_1_ADDRESS || '',
          verifier_1_key: response.data.VERIFIER_1_KEY || '',
          verifier_2_address: response.data.VERIFIER_2_ADDRESS || '',
          verifier_2_key: response.data.VERIFIER_2_KEY || ''
        };

        this.loading = false;
      } catch (error) {
        this.errorMessage = '获取配置失败: ' + (error.response?.data?.error || error.message);
        this.loading = false;
      }
    },
    async saveConfig() {
      try {
        this.loading = true;
        this.successMessage = '';
        this.errorMessage = '';

        await axios.post('/api/blockchain/config', this.config);

        this.successMessage = '区块链配置已成功保存';
        this.loading = false;
      } catch (error) {
        this.errorMessage = '保存配置失败: ' + (error.response?.data?.error || error.message);
        this.loading = false;
      }
    },
    async fetchGanacheAccounts() {
      try {
        this.loading = true;
        this.successMessage = '';
        this.errorMessage = '';

        const response = await axios.get('/api/blockchain/accounts');
        this.ganacheAccounts = response.data.accounts;

        this.loading = false;
      } catch (error) {
        this.errorMessage = '获取Ganache账户失败: ' + (error.response?.data?.error || error.message);
        this.loading = false;
      }
    },
    async deployContract() {
      try {
        this.loading = true;
        this.successMessage = '';
        this.errorMessage = '';

        const response = await axios.post('/api/blockchain/deploy');

        this.successMessage = '智能合约部署成功';
        this.config.contract_address = response.data.contract_address;

        this.loading = false;
      } catch (error) {
        this.errorMessage = '部署智能合约失败: ' + (error.response?.data?.error || error.message);
        this.loading = false;
      }
    },
    copyAddress(address) {
      navigator.clipboard.writeText(address).then(() => {
        this.$notify({
          title: '成功',
          message: '地址已复制到剪贴板',
          type: 'success'
        });
      });
    }
  }
};
</script>

<style scoped>
.blockchain-config {
  margin-bottom: 2rem;
}
</style>
