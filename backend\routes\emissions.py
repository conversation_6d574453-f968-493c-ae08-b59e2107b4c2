"""
排放数据相关路由
"""

import hashlib
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity

from backend import db
from backend.models.user import User
from backend.models.emission import EmissionData
from backend.models.activity import Activity
from backend.utils.auth import enterprise_required

emissions_bp = Blueprint('emissions', __name__)

@emissions_bp.route('', methods=['POST'])
@enterprise_required
def create_emission():
    data = request.get_json()

    # 验证必填字段
    required_fields = ['emission_source', 'emission_amount', 'emission_unit',
                      'calculation_method', 'emission_period_start', 'emission_period_end']
    for field in required_fields:
        if field not in data:
            return jsonify({'error': f'缺少必填字段: {field}'}), 400

    # 创建排放数据
    emission = EmissionData(
        enterprise_id=get_jwt_identity(),
        emission_source=data['emission_source'],
        emission_amount=data['emission_amount'],
        emission_unit=data['emission_unit'],
        calculation_method=data['calculation_method'],
        emission_period_start=datetime.strptime(data['emission_period_start'], '%Y-%m-%d').date(),
        emission_period_end=datetime.strptime(data['emission_period_end'], '%Y-%m-%d').date(),
        status='pending',
        submission_time=datetime.now(),
        proof_file_path=data.get('proof_file_path')
    )

    db.session.add(emission)
    db.session.commit()

    # 生成哈希值
    hash_value = hashlib.sha256(f"{emission.id}_{emission.enterprise_id}_{emission.emission_amount}_{emission.submission_time.isoformat()}".encode()).hexdigest()

    # 提交到区块链
    blockchain_result = current_app.blockchain_client.submit_emission_data(
        emission.id,
        emission.enterprise_id,
        emission.emission_amount,
        emission.submission_time.isoformat(),
        hash_value
    )

    # 更新区块链信息
    emission.blockchain_hash = blockchain_result['tx_hash']
    emission.blockchain_block = blockchain_result['block_number']
    db.session.commit()

    # 记录活动
    activity = Activity(
        user_id=get_jwt_identity(),
        activity_type='create_emission',
        description=f'企业提交了排放数据，ID: {emission.id}'
    )
    db.session.add(activity)
    db.session.commit()

    return jsonify({
        'message': '排放数据提交成功',
        'emission': emission.to_dict()
    }), 201

@emissions_bp.route('', methods=['GET'])
@jwt_required()
def get_emissions():
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)

    if not user:
        return jsonify({'error': '用户不存在'}), 404

    # 根据用户角色过滤排放数据
    if user.role == 'admin':
        # 管理员可以查看所有排放数据
        emissions = EmissionData.query.all()
    elif user.role == 'enterprise':
        # 企业用户只能查看自己的排放数据
        emissions = EmissionData.query.filter_by(enterprise_id=user.id).all()
    elif user.role == 'verifier':
        # 核查机构可以查看所有待核查的排放数据
        emissions = EmissionData.query.filter_by(status='pending').all()
    else:
        return jsonify({'error': '无效的用户角色'}), 400

    return jsonify({
        'emissions': [emission.to_dict() for emission in emissions]
    }), 200

@emissions_bp.route('/<int:emission_id>', methods=['GET'])
@jwt_required()
def get_emission(emission_id):
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)

    if not user:
        return jsonify({'error': '用户不存在'}), 404

    emission = EmissionData.query.get(emission_id)
    if not emission:
        return jsonify({'error': '排放数据不存在'}), 404

    # 检查权限
    if user.role == 'enterprise' and emission.enterprise_id != user.id:
        return jsonify({'error': '无权访问此排放数据'}), 403

    return jsonify({
        'emission': emission.to_dict()
    }), 200

@emissions_bp.route('/<int:emission_id>', methods=['PUT'])
@enterprise_required
def update_emission(emission_id):
    emission = EmissionData.query.get(emission_id)
    if not emission:
        return jsonify({'error': '排放数据不存在'}), 404

    # 检查权限
    if emission.enterprise_id != get_jwt_identity():
        return jsonify({'error': '无权修改此排放数据'}), 403

    # 检查状态
    if emission.status != 'pending':
        return jsonify({'error': '只能修改待核查的排放数据'}), 400

    data = request.get_json()

    # 更新排放数据
    if 'emission_source' in data:
        emission.emission_source = data['emission_source']

    if 'emission_amount' in data:
        emission.emission_amount = data['emission_amount']

    if 'emission_unit' in data:
        emission.emission_unit = data['emission_unit']

    if 'calculation_method' in data:
        emission.calculation_method = data['calculation_method']

    if 'emission_period_start' in data:
        emission.emission_period_start = datetime.strptime(data['emission_period_start'], '%Y-%m-%d').date()

    if 'emission_period_end' in data:
        emission.emission_period_end = datetime.strptime(data['emission_period_end'], '%Y-%m-%d').date()

    if 'proof_file_path' in data:
        emission.proof_file_path = data['proof_file_path']

    db.session.commit()

    # 记录活动
    activity = Activity(
        user_id=get_jwt_identity(),
        activity_type='update_emission',
        description=f'企业更新了排放数据，ID: {emission.id}'
    )
    db.session.add(activity)
    db.session.commit()

    return jsonify({
        'message': '排放数据更新成功',
        'emission': emission.to_dict()
    }), 200
