import React, { useState, useEffect } from 'react';
import axios from 'axios';
import '../styles/ReportManagement.css';

function ReportManagement() {
  const [reports, setReports] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('all');
  const [sortField, setSortField] = useState('createTime');
  const [sortDirection, setSortDirection] = useState('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedItems, setSelectedItems] = useState([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    type: 'daily',
    description: '',
    schedule: {
      frequency: 'daily',
      time: '00:00',
      days: []
    }
  });

  const itemsPerPage = 10;

  useEffect(() => {
    fetchReports();
  }, [currentPage, sortField, sortDirection, selectedType, searchTerm]);

  const fetchReports = async () => {
    try {
      const response = await axios.get('/api/reports', {
        params: {
          page: currentPage,
          limit: itemsPerPage,
          sort: sortField,
          direction: sortDirection,
          type: selectedType,
          search: searchTerm
        },
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      setReports(response.data.items);
      setTotalPages(Math.ceil(response.data.total / itemsPerPage));
      setLoading(false);
    } catch (err) {
      setError('获取报告列表失败');
      setLoading(false);
    }
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const handleTypeChange = (e) => {
    setSelectedType(e.target.value);
    setCurrentPage(1);
  };

  const handleSort = (field) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleSelectItem = (id) => {
    setSelectedItems(prev =>
      prev.includes(id)
        ? prev.filter(itemId => itemId !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    setSelectedItems(prev =>
      prev.length === reports.length
        ? []
        : reports.map(item => item.id)
    );
  };

  const handleDelete = async () => {
    if (!window.confirm('确定要删除选中的报告吗？')) return;

    try {
      await axios.delete('/api/reports', {
        data: { ids: selectedItems },
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      setSelectedItems([]);
      fetchReports();
    } catch (err) {
      setError('删除报告失败');
    }
  };

  const handleDownload = async () => {
    try {
      const response = await axios.get('/api/reports/download', {
        params: { ids: selectedItems },
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` },
        responseType: 'blob'
      });

      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'reports.zip');
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (err) {
      setError('下载报告失败');
    }
  };

  const handleCreate = async () => {
    try {
      await axios.post('/api/reports', formData, {
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      setShowCreateModal(false);
      setFormData({
        title: '',
        type: 'daily',
        description: '',
        schedule: {
          frequency: 'daily',
          time: '00:00',
          days: []
        }
      });
      fetchReports();
    } catch (err) {
      setError('创建报告失败');
    }
  };

  const handleFormChange = (e) => {
    const { name, value } = e.target;
    if (name.startsWith('schedule.')) {
      const scheduleField = name.split('.')[1];
      setFormData(prev => ({
        ...prev,
        schedule: {
          ...prev.schedule,
          [scheduleField]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleDayChange = (day) => {
    setFormData(prev => ({
      ...prev,
      schedule: {
        ...prev.schedule,
        days: prev.schedule.days.includes(day)
          ? prev.schedule.days.filter(d => d !== day)
          : [...prev.schedule.days, day]
      }
    }));
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) return <div className="loading">加载中...</div>;

  return (
    <div className="report-management">
      <div className="report-header">
        <h2>报告管理</h2>
        <div className="report-actions">
          <button
            className="btn btn-primary"
            onClick={() => setShowCreateModal(true)}
          >
            创建报告
          </button>
          <button
            className="btn btn-secondary"
            onClick={handleDownload}
            disabled={selectedItems.length === 0}
          >
            下载选中
          </button>
          <button
            className="btn btn-danger"
            onClick={handleDelete}
            disabled={selectedItems.length === 0}
          >
            删除选中
          </button>
        </div>
      </div>

      {error && <div className="alert alert-danger">{error}</div>}

      <div className="report-filters">
        <div className="search-box">
          <input
            type="text"
            placeholder="搜索报告..."
            value={searchTerm}
            onChange={handleSearch}
            className="form-control"
          />
        </div>
        <div className="filter-box">
          <select
            value={selectedType}
            onChange={handleTypeChange}
            className="form-control"
          >
            <option value="all">所有类型</option>
            <option value="daily">日报</option>
            <option value="weekly">周报</option>
            <option value="monthly">月报</option>
            <option value="custom">自定义</option>
          </select>
        </div>
      </div>

      <div className="report-table">
        <table>
          <thead>
            <tr>
              <th>
                <input
                  type="checkbox"
                  checked={selectedItems.length === reports.length}
                  onChange={handleSelectAll}
                />
              </th>
              <th onClick={() => handleSort('title')}>
                标题
                {sortField === 'title' && (
                  <span className="sort-icon">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
              <th onClick={() => handleSort('type')}>
                类型
                {sortField === 'type' && (
                  <span className="sort-icon">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
              <th onClick={() => handleSort('status')}>
                状态
                {sortField === 'status' && (
                  <span className="sort-icon">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
              <th onClick={() => handleSort('createTime')}>
                创建时间
                {sortField === 'createTime' && (
                  <span className="sort-icon">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            {reports.map(report => (
              <tr key={report.id}>
                <td>
                  <input
                    type="checkbox"
                    checked={selectedItems.includes(report.id)}
                    onChange={() => handleSelectItem(report.id)}
                  />
                </td>
                <td>{report.title}</td>
                <td>
                  <span className={`type-tag ${report.type}`}>
                    {report.type === 'daily' ? '日报' :
                     report.type === 'weekly' ? '周报' :
                     report.type === 'monthly' ? '月报' : '自定义'}
                  </span>
                </td>
                <td>
                  <span className={`status-tag ${report.status}`}>
                    {report.status === 'active' ? '运行中' :
                     report.status === 'paused' ? '已暂停' :
                     report.status === 'completed' ? '已完成' : '失败'}
                  </span>
                </td>
                <td>{formatDate(report.createTime)}</td>
                <td>
                  <div className="action-buttons">
                    <button
                      className="btn btn-sm btn-secondary"
                      onClick={() => window.open(report.downloadUrl)}
                    >
                      下载
                    </button>
                    <button
                      className="btn btn-sm btn-danger"
                      onClick={() => handleDelete([report.id])}
                    >
                      删除
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="pagination">
        <button
          className="btn btn-sm"
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          上一页
        </button>
        <span className="page-info">
          第 {currentPage} 页，共 {totalPages} 页
        </span>
        <button
          className="btn btn-sm"
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          下一页
        </button>
      </div>

      {showCreateModal && (
        <div className="modal">
          <div className="modal-content">
            <h3>创建报告</h3>
            <div className="create-form">
              <div className="form-group">
                <label htmlFor="title">报告标题</label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleFormChange}
                  className="form-control"
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="type">报告类型</label>
                <select
                  id="type"
                  name="type"
                  value={formData.type}
                  onChange={handleFormChange}
                  className="form-control"
                  required
                >
                  <option value="daily">日报</option>
                  <option value="weekly">周报</option>
                  <option value="monthly">月报</option>
                  <option value="custom">自定义</option>
                </select>
              </div>
              <div className="form-group">
                <label htmlFor="description">报告描述</label>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleFormChange}
                  className="form-control"
                  rows="3"
                />
              </div>
              <div className="form-group">
                <label>调度设置</label>
                <div className="schedule-settings">
                  <div className="schedule-item">
                    <label htmlFor="frequency">频率</label>
                    <select
                      id="frequency"
                      name="schedule.frequency"
                      value={formData.schedule.frequency}
                      onChange={handleFormChange}
                      className="form-control"
                    >
                      <option value="daily">每天</option>
                      <option value="weekly">每周</option>
                      <option value="monthly">每月</option>
                    </select>
                  </div>
                  <div className="schedule-item">
                    <label htmlFor="time">时间</label>
                    <input
                      type="time"
                      id="time"
                      name="schedule.time"
                      value={formData.schedule.time}
                      onChange={handleFormChange}
                      className="form-control"
                    />
                  </div>
                  {formData.schedule.frequency === 'weekly' && (
                    <div className="schedule-item">
                      <label>选择日期</label>
                      <div className="day-selector">
                        {['周一', '周二', '周三', '周四', '周五', '周六', '周日'].map((day, index) => (
                          <label key={day} className="day-checkbox">
                            <input
                              type="checkbox"
                              checked={formData.schedule.days.includes(index + 1)}
                              onChange={() => handleDayChange(index + 1)}
                            />
                            {day}
                          </label>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
              <div className="modal-actions">
                <button
                  className="btn btn-primary"
                  onClick={handleCreate}
                >
                  创建
                </button>
                <button
                  className="btn btn-secondary"
                  onClick={() => setShowCreateModal(false)}
                >
                  取消
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default ReportManagement; 