<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放管理系统 - 首页</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            color: #333;
            min-height: 100vh;
        }
        .hero {
            background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('https://via.placeholder.com/1920x1080?text=Carbon+Management');
            background-size: cover;
            background-position: center;
            color: white;
            padding: 100px 0;
            text-align: center;
        }
        .hero h1 {
            font-size: 48px;
            margin-bottom: 20px;
        }
        .hero p {
            font-size: 20px;
            max-width: 800px;
            margin: 0 auto 30px;
        }
        .btn {
            display: inline-block;
            padding: 12px 30px;
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            color: white;
            text-decoration: none;
            border-radius: 30px;
            font-weight: bold;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            border: none;
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 20px rgba(0, 0, 0, 0.3);
            background: linear-gradient(to right, #2E7D32, #4CAF50);
        }
        .btn-secondary {
            background: transparent;
            border: 2px solid white;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
        }
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-3px);
            box-shadow: 0 7px 20px rgba(0, 0, 0, 0.25);
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background: linear-gradient(to right, #1B5E20, #388E3C);
            color: white;
            padding: 20px 0;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        .nav-links {
            display: flex;
        }
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            transition: color 0.3s;
        }
        .nav-links a:hover {
            color: #3498db;
        }
        .features {
            padding: 80px 0;
            text-align: center;
        }
        .features h2 {
            font-size: 36px;
            margin-bottom: 60px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
        }
        .feature-item {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,0.05);
        }
        .feature-item:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        .feature-icon {
            font-size: 48px;
            margin-bottom: 20px;
            color: #4CAF50;
        }
        .feature-title {
            font-size: 24px;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .feature-desc {
            color: #7f8c8d;
            line-height: 1.6;
        }
        .about {
            background-color: #f9f9f9;
            padding: 80px 0;
        }
        .about-content {
            display: flex;
            align-items: center;
            gap: 50px;
        }
        .about-text {
            flex: 1;
        }
        .about-text h2 {
            font-size: 36px;
            margin-bottom: 20px;
            color: #2c3e50;
        }
        .about-text p {
            color: #7f8c8d;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        .about-image {
            flex: 1;
        }
        .about-image img {
            width: 100%;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .cta {
            background: linear-gradient(135deg, #4CAF50, #1B5E20);
            color: white;
            padding: 80px 0;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .cta h2 {
            font-size: 36px;
            margin-bottom: 20px;
        }
        .cta p {
            font-size: 18px;
            max-width: 800px;
            margin: 0 auto 30px;
        }
        .cta .btn {
            background-color: white;
            color: #4CAF50;
        }
        .cta .btn:hover {
            background-color: #f5f5f5;
        }
        footer {
            background: linear-gradient(to right, #1B5E20, #388E3C);
            color: white;
            padding: 40px 0;
            text-align: center;
            box-shadow: 0 -4px 10px rgba(0,0,0,0.1);
        }
        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .footer-links {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }
        .footer-links a {
            color: white;
            text-decoration: none;
            margin: 0 15px;
        }
        .footer-links a:hover {
            text-decoration: underline;
        }
        .copyright {
            color: #95a5a6;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo">碳排放管理系统</div>
            <div class="nav-links">
                <a href="/home">首页</a>
                <a href="#features">功能</a>
                <a href="#about">关于</a>
                <a href="/login">登录</a>
                <a href="/register">注册</a>
            </div>
        </div>
    </header>

    <section class="hero">
        <h1>碳排放管理系统</h1>
        <p>基于区块链技术的碳排放数据管理、核查和交易平台，助力企业实现碳中和目标</p>
        <div>
            <a href="/login" class="btn">立即登录</a>
            <a href="/register" class="btn btn-secondary">注册账号</a>
        </div>
    </section>

    <section class="features" id="features">
        <div class="container">
            <h2>系统功能</h2>
            <div class="feature-grid">
                <div class="feature-item">
                    <div class="feature-icon">📊</div>
                    <h3 class="feature-title">排放数据管理</h3>
                    <p class="feature-desc">便捷录入、管理和分析企业碳排放数据，支持多种排放源和计算方法</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">✅</div>
                    <h3 class="feature-title">第三方核查</h3>
                    <p class="feature-desc">专业核查机构对排放数据进行审核，确保数据准确性和可靠性</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">💰</div>
                    <h3 class="feature-title">碳配额交易</h3>
                    <p class="feature-desc">企业间直接进行碳配额交易，降低减排成本，提高减排效率</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🔗</div>
                    <h3 class="feature-title">区块链技术</h3>
                    <p class="feature-desc">利用区块链技术确保数据不可篡改，提高系统可信度和透明度</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🧮</div>
                    <h3 class="feature-title">碳计算器</h3>
                    <p class="feature-desc">多种活动的碳排放计算工具，帮助企业精确计算碳足迹</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">📈</div>
                    <h3 class="feature-title">预测分析</h3>
                    <p class="feature-desc">基于历史数据预测未来排放趋势，辅助企业制定减排策略</p>
                </div>
            </div>
        </div>
    </section>

    <section class="about" id="about">
        <div class="container">
            <div class="about-content">
                <div class="about-text">
                    <h2>关于系统</h2>
                    <p>碳排放管理系统是一个基于区块链技术的综合性平台，旨在帮助企业实现碳排放数据的精确管理、核查和交易。</p>
                    <p>系统采用前后端分离架构，结合区块链技术，确保数据的安全性、透明性和不可篡改性，为企业碳中和目标的实现提供有力支持。</p>
                    <p>系统支持企业用户、核查机构和管理员三种角色，满足不同用户的需求，实现碳排放管理的全流程数字化。</p>
                </div>
                <div class="about-image">
                    <img src="https://via.placeholder.com/600x400?text=Carbon+Management+System" alt="碳排放管理系统">
                </div>
            </div>
        </div>
    </section>

    <section class="cta">
        <div class="container">
            <h2>开始使用碳排放管理系统</h2>
            <p>立即注册账号，体验全面的碳排放管理功能，助力企业实现碳中和目标</p>
            <a href="/register" class="btn">立即注册</a>
        </div>
    </section>

    <footer>
        <div class="footer-content">
            <div class="footer-links">
                <a href="/home">首页</a>
                <a href="#features">功能</a>
                <a href="#about">关于</a>
                <a href="/login">登录</a>
                <a href="/register">注册</a>
            </div>
            <div class="copyright">
                &copy; 2023 碳排放管理系统 - 毕业设计作品
            </div>
        </div>
    </footer>
</body>
</html>
