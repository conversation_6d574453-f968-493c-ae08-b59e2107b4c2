<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放核查系统 - 核查管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container">
            <a class="navbar-brand" href="/"><i class="fas fa-leaf me-2"></i>碳排放核查系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/verifier"><i class="fas fa-tachometer-alt me-1"></i>仪表板</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/verifier/verifications"><i class="fas fa-clipboard-check me-1"></i>核查管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/verifier/reports"><i class="fas fa-file-alt me-1"></i>报告管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/verifier/enterprises"><i class="fas fa-building me-1"></i>企业管理</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>{{ current_user.get('username', '核查机构') }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="/verifier/profile"><i class="fas fa-id-card me-1"></i>机构资料</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-1"></i>退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mt-4">
        <!-- 消息提示 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-clipboard-check me-2"></i>核查管理</h1>
        </div>

        <!-- 核查统计 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">总核查数</h6>
                                <h2 class="card-text">{{ statistics.total_verifications }}</h2>
                            </div>
                            <i class="fas fa-clipboard-check fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">待核查</h6>
                                <h2 class="card-text">{{ statistics.pending_verifications }}</h2>
                            </div>
                            <i class="fas fa-clock fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">已通过</h6>
                                <h2 class="card-text">{{ statistics.approved_verifications }}</h2>
                            </div>
                            <i class="fas fa-check-circle fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">已拒绝</h6>
                                <h2 class="card-text">{{ statistics.rejected_verifications }}</h2>
                            </div>
                            <i class="fas fa-times-circle fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 待核查列表 -->
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0"><i class="fas fa-clock me-2"></i>待核查列表</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>企业名称</th>
                                <th>排放源</th>
                                <th>排放量 (tCO2e)</th>
                                <th>提交日期</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if pending_verifications %}
                                {% for verification in pending_verifications %}
                                    <tr>
                                        <td>{{ verification.id }}</td>
                                        <td>{{ verification.enterprise_name }}</td>
                                        <td>{{ verification.source }}</td>
                                        <td>{{ verification.amount }}</td>
                                        <td>{{ verification.submission_date }}</td>
                                        <td>
                                            <span class="badge bg-warning">{{ verification.status }}</span>
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="/verifier/verification/{{ verification.id }}" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-clipboard-check me-1"></i>核查
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="7" class="text-center">暂无待核查数据</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 已完成核查列表 -->
        <div class="card">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0"><i class="fas fa-history me-2"></i>已完成核查列表</h5>
            </div>
            <div class="card-body">
                <!-- 筛选 -->
                <form method="get" action="/verifier/verifications" class="row g-3 mb-4">
                    <div class="col-md-3">
                        <label for="enterprise" class="form-label">企业名称</label>
                        <input type="text" class="form-control" id="enterprise" name="enterprise" placeholder="输入企业名称">
                    </div>
                    <div class="col-md-3">
                        <label for="date_from" class="form-label">开始日期</label>
                        <input type="date" class="form-control" id="date_from" name="date_from">
                    </div>
                    <div class="col-md-3">
                        <label for="date_to" class="form-label">结束日期</label>
                        <input type="date" class="form-control" id="date_to" name="date_to">
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">状态</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">全部状态</option>
                            <option value="已通过">已通过</option>
                            <option value="已拒绝">已拒绝</option>
                        </select>
                    </div>
                    <div class="col-12 d-flex justify-content-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>搜索
                        </button>
                    </div>
                </form>

                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>企业名称</th>
                                <th>排放源</th>
                                <th>排放量 (tCO2e)</th>
                                <th>核查日期</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if completed_verifications %}
                                {% for verification in completed_verifications %}
                                    <tr>
                                        <td>{{ verification.id }}</td>
                                        <td>{{ verification.enterprise_name }}</td>
                                        <td>{{ verification.source }}</td>
                                        <td>{{ verification.amount }}</td>
                                        <td>{{ verification.verification_date }}</td>
                                        <td>
                                            {% if verification.status == '已通过' %}
                                                <span class="badge bg-success">已通过</span>
                                            {% elif verification.status == '已拒绝' %}
                                                <span class="badge bg-danger">已拒绝</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="/verifier/verification/{{ verification.id }}" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="/verifier/verification/{{ verification.id }}/report" class="btn btn-sm btn-success">
                                                    <i class="fas fa-file-alt"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="7" class="text-center">暂无已完成核查数据</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1" aria-disabled="true">上一页</a>
                        </li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                        <li class="page-item">
                            <a class="page-link" href="#">下一页</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">© 2025 碳排放核查系统 | 基于区块链技术</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/api.js') }}"></script>
    <script src="{{ url_for('static', filename='js/verifier/verifications.js') }}"></script>
</body>
</html>
