{% extends 'base.html' %}

{% block title %}碳排放核查系统 - 核查机构演示{% endblock %}

{% block head %}
<style>
    body {
        font-family: 'Arial', sans-serif;
        margin: 0;
        padding: 0;
        background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
        color: #333;
        min-height: 100vh;
    }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background: linear-gradient(to right, #1B5E20, #388E3C);
            color: white;
            padding: 20px 0;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-name {
            margin-right: 15px;
        }
        .logout-btn {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 30px;
            cursor: pointer;
            text-decoration: none;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .logout-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        nav {
            background: linear-gradient(to right, #2E7D32, #43A047);
            padding: 10px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
        }
        .nav-item {
            color: white;
            text-decoration: none;
            padding: 15px 20px;
            margin-right: 5px;
            border-radius: 0;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 16px;
            letter-spacing: 0.5px;
            position: relative;
            display: flex;
            align-items: center;
        }
        .nav-item:hover, .nav-item.active {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }
        .nav-item.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 20%;
            right: 20%;
            height: 3px;
            background-color: white;
            border-radius: 3px 3px 0 0;
        }
        .card {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            transform: translateY(-5px);
        }
        .card-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .dashboard {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
        }
        .stat-card {
            text-align: center;
            padding: 30px 20px;
        }
        .stat-value {
            font-size: 36px;
            font-weight: bold;
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin: 15px 0;
            text-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .stat-label {
            color: #7f8c8d;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            display: inline-block;
        }
        .status-pending {
            background: linear-gradient(to right, #f39c12, #e67e22);
            color: white;
        }
        .status-verified {
            background: linear-gradient(to right, #2ecc71, #27ae60);
            color: white;
        }
        .status-rejected {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        .btn-primary {
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            color: white;
        }
        .btn-success {
            background: linear-gradient(to right, #2ecc71, #27ae60);
            color: white;
        }
        .btn-danger {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 30px;
            font-size: 14px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 2px 10px rgba(76, 175, 80, 0.2);
        }
        textarea {
            border-radius: 15px;
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        .modal-content {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 20px;
            width: 500px;
            max-width: 90%;
            padding: 30px;
            box-shadow: 0 15px 50px rgba(0,0,0,0.2);
            border: 1px solid rgba(0,0,0,0.05);
        }
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .modal-title {
            font-size: 18px;
            font-weight: bold;
        }
        .close-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
        }
        .modal-footer {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
        }
        .modal-footer button {
            margin-left: 10px;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="demo-header">
        <div class="header-content">
            <div class="logo">碳排放管理系统</div>
            <div class="user-info">
                <span class="user-name">国家碳排放核查中心</span>
                <a href="/login" class="logout-btn">退出登录</a>
            </div>
        </div>
    </div>

    <div class="demo-nav">
        <div class="nav-content">
            <a href="/verifier" class="nav-item active"><i class="fas fa-tachometer-alt mr-2"></i>仪表板</a>
            <a href="/verifier_tasks" class="nav-item"><i class="fas fa-tasks mr-2"></i>待核查任务</a>
            <a href="/verifier_records" class="nav-item"><i class="fas fa-clipboard-check mr-2"></i>核查记录</a>
            <a href="/verifier_enterprises" class="nav-item"><i class="fas fa-building mr-2"></i>企业管理</a>
            <a href="/verifier_reports" class="nav-item"><i class="fas fa-file-alt mr-2"></i>报告生成</a>
            <a href="/verifier_blockchain_config" class="nav-item"><i class="fas fa-link mr-2"></i>区块链配置</a>
        </div>
    </div>

    <div class="container">
        <h1>核查机构仪表板</h1>

        <div class="dashboard">
            <div class="card stat-card">
                <div class="stat-label">待核查任务</div>
                <div class="stat-value">12</div>
                <div class="stat-label">个</div>
            </div>
            <div class="card stat-card">
                <div class="stat-label">已完成核查</div>
                <div class="stat-value">45</div>
                <div class="stat-label">个</div>
            </div>
            <div class="card stat-card">
                <div class="stat-label">核查通过率</div>
                <div class="stat-value">85%</div>
                <div class="stat-label">本年度</div>
            </div>
        </div>

        <div class="card">
            <div class="card-title">待核查任务</div>
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>企业</th>
                        <th>排放源</th>
                        <th>排放量</th>
                        <th>提交时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1002</td>
                        <td>北京碳排放科技有限公司</td>
                        <td>天然气锅炉</td>
                        <td>320 吨CO2e</td>
                        <td>2023-05-10</td>
                        <td><button class="btn btn-primary verify-btn">核查</button></td>
                    </tr>
                    <tr>
                        <td>1005</td>
                        <td>上海绿色能源有限公司</td>
                        <td>电力消耗</td>
                        <td>450 吨CO2e</td>
                        <td>2023-05-11</td>
                        <td><button class="btn btn-primary verify-btn">核查</button></td>
                    </tr>
                    <tr>
                        <td>1008</td>
                        <td>广州环保科技有限公司</td>
                        <td>工业生产过程</td>
                        <td>680 吨CO2e</td>
                        <td>2023-05-12</td>
                        <td><button class="btn btn-primary verify-btn">核查</button></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="card">
            <div class="card-title">最近核查记录</div>
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>企业</th>
                        <th>排放源</th>
                        <th>排放量</th>
                        <th>核查结果</th>
                        <th>核查时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1001</td>
                        <td>北京碳排放科技有限公司</td>
                        <td>燃煤锅炉</td>
                        <td>450 吨CO2e</td>
                        <td><span class="status status-verified">通过</span></td>
                        <td>2023-05-08</td>
                        <td><button class="btn btn-primary">查看</button></td>
                    </tr>
                    <tr>
                        <td>1003</td>
                        <td>广州环保科技有限公司</td>
                        <td>工业生产过程</td>
                        <td>780 吨CO2e</td>
                        <td><span class="status status-rejected">拒绝</span></td>
                        <td>2023-05-09</td>
                        <td><button class="btn btn-primary">查看</button></td>
                    </tr>
                    <tr>
                        <td>1004</td>
                        <td>上海绿色能源有限公司</td>
                        <td>交通运输</td>
                        <td>250 吨CO2e</td>
                        <td><span class="status status-verified">通过</span></td>
                        <td>2023-05-10</td>
                        <td><button class="btn btn-primary">查看</button></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- 核查模态框 -->
    <div class="modal" id="verifyModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">核查排放数据</div>
                <button class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>企业</label>
                    <div id="verify-company">北京碳排放科技有限公司</div>
                </div>
                <div class="form-group">
                    <label>排放源</label>
                    <div id="verify-source">天然气锅炉</div>
                </div>
                <div class="form-group">
                    <label>排放量</label>
                    <div id="verify-amount">320 吨CO2e</div>
                </div>
                <div class="form-group">
                    <label>计算方法</label>
                    <div id="verify-method">排放因子法</div>
                </div>
                <div class="form-group">
                    <label for="verify-conclusion">核查结论</label>
                    <select id="verify-conclusion">
                        <option value="">请选择核查结论</option>
                        <option value="approved">通过</option>
                        <option value="rejected">拒绝</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="verify-comments">核查意见</label>
                    <textarea id="verify-comments" rows="5" placeholder="请输入核查意见"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-danger">取消</button>
                <button class="btn btn-primary">提交</button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 导航切换 - 不需要额外的事件监听器，因为我们使用的是链接
            // 根据当前URL设置活动导航项
            const currentPath = window.location.pathname;
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                if (item.getAttribute('href') === currentPath) {
                    navItems.forEach(i => i.classList.remove('active'));
                    item.classList.add('active');
                }
            });

            // 核查按钮点击事件
            const verifyBtns = document.querySelectorAll('.verify-btn');
            const verifyModal = document.getElementById('verifyModal');
            const closeBtn = verifyModal.querySelector('.close-btn');
            const cancelBtn = verifyModal.querySelector('.btn-danger');

            verifyBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    verifyModal.style.display = 'flex';
                });
            });

            // 关闭模态框
            function closeModal() {
                verifyModal.style.display = 'none';
            }

            closeBtn.addEventListener('click', closeModal);
            cancelBtn.addEventListener('click', closeModal);

            // 退出登录
            document.querySelector('.logout-btn').addEventListener('click', function() {
                window.location.href = '/login';
            });

            // 添加核查统计图表
            const chartContainer = document.createElement('div');
            chartContainer.style.height = '300px';
            chartContainer.style.marginBottom = '30px';
            chartContainer.innerHTML = '<canvas id="verificationChart"></canvas>';

            // 将图表插入到仪表板下方
            const dashboard = document.querySelector('.dashboard');
            dashboard.parentNode.insertBefore(chartContainer, dashboard.nextSibling);

            // 创建图表
            const ctx = document.getElementById('verificationChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [
                        {
                            label: '核查任务数',
                            data: [8, 12, 15, 18, 20, 25],
                            borderColor: 'rgba(46, 204, 113, 1)',
                            backgroundColor: 'rgba(46, 204, 113, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        },
                        {
                            label: '通过率 (%)',
                            data: [75, 78, 80, 82, 85, 88],
                            borderColor: 'rgba(52, 152, 219, 1)',
                            backgroundColor: 'rgba(52, 152, 219, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4,
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '核查任务数'
                            }
                        },
                        y1: {
                            position: 'right',
                            beginAtZero: false,
                            min: 50,
                            max: 100,
                            title: {
                                display: true,
                                text: '通过率 (%)'
                            },
                            grid: {
                                drawOnChartArea: false
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: '2023年核查任务统计',
                            font: {
                                size: 16
                            }
                        }
                    }
                }
            });
        });
    </script>
{% endblock %}
