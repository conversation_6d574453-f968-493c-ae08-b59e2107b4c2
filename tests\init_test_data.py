"""
初始化测试数据脚本
用于创建测试用户和测试数据
"""

import os
import sys
import pymysql
import logging
from dotenv import load_dotenv
from werkzeug.security import generate_password_hash

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('init_test_data')

# 加载环境变量
load_dotenv()

# 数据库配置
DB_CONFIG = {
    'host': os.getenv('DB_HOST', '***********'),
    'port': int(os.getenv('DB_PORT', 3306)),
    'user': os.getenv('DB_USER', 'wuhong'),
    'password': os.getenv('DB_PASSWORD', 'D7mH8rZ7a7Z2kJa8'),
    'db': os.getenv('DB_NAME', 'ces'),
    'charset': 'utf8mb4'
}

def connect_to_database():
    """连接到数据库"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        logger.info(f"成功连接到数据库 {DB_CONFIG['db']} @ {DB_CONFIG['host']}")
        return connection
    except Exception as e:
        logger.error(f"连接数据库失败: {str(e)}")
        sys.exit(1)

def execute_sql_file(connection, sql_file_path):
    """执行SQL文件"""
    try:
        with open(sql_file_path, 'r', encoding='utf-8') as f:
            sql_script = f.read()
        
        with connection.cursor() as cursor:
            # 分割SQL语句
            statements = sql_script.split(';')
            
            for statement in statements:
                statement = statement.strip()
                if statement:
                    cursor.execute(statement)
            
            connection.commit()
        
        logger.info(f"成功执行SQL脚本: {sql_file_path}")
        return True
    except Exception as e:
        logger.error(f"执行SQL脚本失败: {str(e)}")
        connection.rollback()
        return False

def create_test_users_manually(connection):
    """手动创建测试用户"""
    try:
        # 清空现有用户数据
        with connection.cursor() as cursor:
            cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
            cursor.execute("TRUNCATE TABLE users")
            cursor.execute("TRUNCATE TABLE enterprises")
            cursor.execute("TRUNCATE TABLE verifiers")
            cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
        
        # 创建测试用户
        users = [
            ('admin', 'password123', '<EMAIL>', 'admin'),
            ('enterprise1', 'password123', '<EMAIL>', 'enterprise'),
            ('enterprise2', 'password123', '<EMAIL>', 'enterprise'),
            ('enterprise3', 'password123', '<EMAIL>', 'enterprise'),
            ('verifier1', 'password123', '<EMAIL>', 'verifier'),
            ('verifier2', 'password123', '<EMAIL>', 'verifier')
        ]
        
        with connection.cursor() as cursor:
            for username, password, email, role in users:
                # 生成密码哈希
                password_hash = generate_password_hash(password)
                
                # 插入用户
                cursor.execute(
                    "INSERT INTO users (username, password, email, role, created_at, updated_at) VALUES (%s, %s, %s, %s, NOW(), NOW())",
                    (username, password_hash, email, role)
                )
                
                # 获取用户ID
                cursor.execute("SELECT LAST_INSERT_ID()")
                user_id = cursor.fetchone()[0]
                
                # 如果是企业用户，创建企业信息
                if role == 'enterprise':
                    if username == 'enterprise1':
                        cursor.execute(
                            "INSERT INTO enterprises (user_id, name, industry, address, contact_person, contact_phone, carbon_quota, created_at, updated_at) VALUES (%s, %s, %s, %s, %s, %s, %s, NOW(), NOW())",
                            (user_id, '绿色能源有限公司', '能源', '北京市海淀区中关村大街1号', '张三', '13800138001', 10000.0)
                        )
                    elif username == 'enterprise2':
                        cursor.execute(
                            "INSERT INTO enterprises (user_id, name, industry, address, contact_person, contact_phone, carbon_quota, created_at, updated_at) VALUES (%s, %s, %s, %s, %s, %s, %s, NOW(), NOW())",
                            (user_id, '蓝天制造股份公司', '制造业', '上海市浦东新区张江高科技园区', '李四', '13800138002', 8000.0)
                        )
                    elif username == 'enterprise3':
                        cursor.execute(
                            "INSERT INTO enterprises (user_id, name, industry, address, contact_person, contact_phone, carbon_quota, created_at, updated_at) VALUES (%s, %s, %s, %s, %s, %s, %s, NOW(), NOW())",
                            (user_id, '未来科技集团', '科技', '深圳市南山区科技园', '王五', '13800138003', 5000.0)
                        )
                
                # 如果是核查机构用户，创建核查机构信息
                elif role == 'verifier':
                    if username == 'verifier1':
                        cursor.execute(
                            "INSERT INTO verifiers (user_id, name, qualification, address, contact_person, contact_phone, created_at, updated_at) VALUES (%s, %s, %s, %s, %s, %s, NOW(), NOW())",
                            (user_id, '环保核查中心', '国家认证A级核查机构', '北京市朝阳区建国路', '赵六', '13900139001')
                        )
                    elif username == 'verifier2':
                        cursor.execute(
                            "INSERT INTO verifiers (user_id, name, qualification, address, contact_person, contact_phone, created_at, updated_at) VALUES (%s, %s, %s, %s, %s, %s, NOW(), NOW())",
                            (user_id, '绿色认证联盟', '国际认证核查机构', '上海市静安区南京西路', '钱七', '13900139002')
                        )
        
        connection.commit()
        logger.info("成功创建测试用户")
        return True
    except Exception as e:
        logger.error(f"创建测试用户失败: {str(e)}")
        connection.rollback()
        return False

def create_test_data(connection):
    """创建测试数据"""
    try:
        # 获取用户ID
        user_ids = {}
        with connection.cursor() as cursor:
            cursor.execute("SELECT id, username FROM users")
            for user_id, username in cursor.fetchall():
                user_ids[username] = user_id
        
        # 清空现有数据
        with connection.cursor() as cursor:
            cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
            cursor.execute("TRUNCATE TABLE emission_data")
            cursor.execute("TRUNCATE TABLE verifications")
            cursor.execute("TRUNCATE TABLE transactions")
            cursor.execute("TRUNCATE TABLE penalties")
            cursor.execute("TRUNCATE TABLE activities")
            cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
        
        # 创建排放数据
        emission_data = [
            # 企业1的排放数据
            (user_ids['enterprise1'], '工厂A', 1200.5, '直接测量法', '2025-01-10 08:00:00', '2025-01-11 10:30:00', 'pending', '一季度常规排放', 'proof_files/enterprise1_emission1.pdf'),
            (user_ids['enterprise1'], '工厂B', 800.3, '间接计算法', '2025-02-15 09:00:00', '2025-02-16 14:20:00', 'pending', '二季度常规排放', 'proof_files/enterprise1_emission2.pdf'),
            (user_ids['enterprise1'], '工厂C', 1500.8, '直接测量法', '2025-03-20 10:00:00', '2025-03-21 11:45:00', 'pending', '三季度常规排放', 'proof_files/enterprise1_emission3.pdf'),
            
            # 企业2的排放数据
            (user_ids['enterprise2'], '生产线A', 950.2, '直接测量法', '2025-01-12 08:30:00', '2025-01-13 09:15:00', 'pending', '一季度常规排放', 'proof_files/enterprise2_emission1.pdf'),
            (user_ids['enterprise2'], '生产线B', 1100.7, '间接计算法', '2025-02-18 09:30:00', '2025-02-19 10:40:00', 'pending', '二季度常规排放', 'proof_files/enterprise2_emission2.pdf'),
            
            # 企业3的排放数据
            (user_ids['enterprise3'], '研发中心', 600.1, '直接测量法', '2025-01-15 08:45:00', '2025-01-16 10:20:00', 'pending', '一季度常规排放', 'proof_files/enterprise3_emission1.pdf'),
            (user_ids['enterprise3'], '数据中心', 750.4, '间接计算法', '2025-02-20 09:15:00', '2025-02-21 11:30:00', 'pending', '二季度常规排放', 'proof_files/enterprise3_emission2.pdf')
        ]
        
        emission_ids = []
        with connection.cursor() as cursor:
            for enterprise_id, source, amount, method, emission_time, submission_time, status, notes, proof_file in emission_data:
                cursor.execute(
                    """INSERT INTO emission_data 
                    (enterprise_id, emission_source, emission_amount, calculation_method, emission_time, submission_time, status, notes, proof_file, created_at, updated_at) 
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())""",
                    (enterprise_id, source, amount, method, emission_time, submission_time, status, notes, proof_file)
                )
                cursor.execute("SELECT LAST_INSERT_ID()")
                emission_ids.append(cursor.fetchone()[0])
        
        # 创建核查记录
        verifications = [
            # 核查机构1的核查记录
            (user_ids['verifier1'], emission_ids[0], 'approved', '数据符合标准，排放量在允许范围内', '2025-01-20 14:30:00'),
            (user_ids['verifier1'], emission_ids[2], 'rejected', '数据不完整，需要补充更多证明材料', '2025-03-25 15:45:00'),
            (user_ids['verifier1'], emission_ids[4], 'approved', '数据准确，计算方法正确', '2025-02-25 13:20:00'),
            
            # 核查机构2的核查记录
            (user_ids['verifier2'], emission_ids[1], 'approved', '排放数据准确，符合行业标准', '2025-02-25 10:15:00'),
            (user_ids['verifier2'], emission_ids[3], 'pending', '正在核查中，需要额外信息', '2025-01-25 11:30:00'),
            (user_ids['verifier2'], emission_ids[5], 'approved', '数据完整，排放量符合预期', '2025-01-30 09:45:00')
        ]
        
        with connection.cursor() as cursor:
            for verifier_id, emission_data_id, conclusion, comments, verification_time in verifications:
                cursor.execute(
                    """INSERT INTO verifications 
                    (verifier_id, emission_data_id, conclusion, comments, verification_time, created_at, updated_at) 
                    VALUES (%s, %s, %s, %s, %s, NOW(), NOW())""",
                    (verifier_id, emission_data_id, conclusion, comments, verification_time)
                )
        
        # 创建交易记录
        transactions = [
            # 企业间的交易
            (user_ids['enterprise1'], user_ids['enterprise2'], 500.0, 20.5, '2025-02-01 10:00:00', 'pending', '碳配额交易'),
            (user_ids['enterprise2'], user_ids['enterprise3'], 300.0, 22.0, '2025-02-10 11:30:00', 'completed', '碳配额交易'),
            (user_ids['enterprise3'], user_ids['enterprise1'], 200.0, 21.0, '2025-03-05 09:45:00', 'cancelled', '碳配额交易'),
            (user_ids['enterprise1'], user_ids['enterprise3'], 400.0, 19.5, '2025-03-15 14:20:00', 'pending', '碳配额交易')
        ]
        
        with connection.cursor() as cursor:
            for buyer_id, seller_id, amount, price, transaction_time, status, notes in transactions:
                cursor.execute(
                    """INSERT INTO transactions 
                    (buyer_id, seller_id, amount, price, transaction_time, status, notes, created_at, updated_at) 
                    VALUES (%s, %s, %s, %s, %s, %s, %s, NOW(), NOW())""",
                    (buyer_id, seller_id, amount, price, transaction_time, status, notes)
                )
        
        # 创建惩罚记录
        penalties = [
            (user_ids['enterprise1'], 5000.0, '排放数据造假', '2025-04-01 09:00:00', 'pending'),
            (user_ids['enterprise2'], 3000.0, '未按时提交排放数据', '2025-04-05 10:30:00', 'completed'),
            (user_ids['enterprise3'], 2000.0, '超额排放', '2025-04-10 11:15:00', 'pending')
        ]
        
        with connection.cursor() as cursor:
            for enterprise_id, amount, reason, penalty_time, status in penalties:
                cursor.execute(
                    """INSERT INTO penalties 
                    (enterprise_id, amount, reason, penalty_time, status, created_at, updated_at) 
                    VALUES (%s, %s, %s, %s, %s, NOW(), NOW())""",
                    (enterprise_id, amount, reason, penalty_time, status)
                )
        
        # 创建活动记录
        activities = [
            (user_ids['admin'], 'login', '管理员登录系统'),
            (user_ids['enterprise1'], 'data_submission', '企业提交排放数据'),
            (user_ids['verifier1'], 'verification', '核查机构提交核查结果'),
            (user_ids['enterprise2'], 'transaction', '企业发起碳交易'),
            (user_ids['admin'], 'penalty', '管理员创建惩罚记录'),
            (user_ids['enterprise3'], 'login', '企业登录系统'),
            (user_ids['verifier2'], 'verification', '核查机构提交核查结果')
        ]
        
        with connection.cursor() as cursor:
            for user_id, activity_type, description in activities:
                cursor.execute(
                    """INSERT INTO activities 
                    (user_id, activity_type, description, created_at) 
                    VALUES (%s, %s, %s, NOW())""",
                    (user_id, activity_type, description)
                )
        
        connection.commit()
        logger.info("成功创建测试数据")
        return True
    except Exception as e:
        logger.error(f"创建测试数据失败: {str(e)}")
        connection.rollback()
        return False

def show_test_data_summary(connection):
    """显示测试数据摘要"""
    try:
        tables = ['users', 'enterprises', 'verifiers', 'emission_data', 'verifications', 'transactions', 'penalties', 'activities']
        
        print("\n测试数据摘要:")
        print("=" * 40)
        
        with connection.cursor() as cursor:
            for table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"{table.ljust(20)}: {count} 条记录")
        
        print("=" * 40)
        return True
    except Exception as e:
        logger.error(f"获取数据摘要失败: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("开始初始化测试数据...")
    
    # 连接数据库
    connection = connect_to_database()
    
    # 创建测试用户
    if not create_test_users_manually(connection):
        logger.error("创建测试用户失败，退出程序")
        connection.close()
        sys.exit(1)
    
    # 创建测试数据
    if not create_test_data(connection):
        logger.error("创建测试数据失败，退出程序")
        connection.close()
        sys.exit(1)
    
    # 显示测试数据摘要
    show_test_data_summary(connection)
    
    # 关闭连接
    connection.close()
    
    logger.info("测试数据初始化完成")
    print("\n测试账户信息:")
    print("=" * 40)
    print("管理员账户: admin / password123")
    print("企业账户1: enterprise1 / password123")
    print("企业账户2: enterprise2 / password123")
    print("企业账户3: enterprise3 / password123")
    print("核查机构账户1: verifier1 / password123")
    print("核查机构账户2: verifier2 / password123")
    print("=" * 40)
    print("\n现在您可以使用这些测试账户登录系统，测试各项功能。")

if __name__ == "__main__":
    main()
