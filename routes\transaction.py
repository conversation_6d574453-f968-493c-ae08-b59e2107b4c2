"""
碳交易模块路由
处理碳配额交易相关的API请求
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
import hashlib
from datetime import datetime

from models import db, User, Transaction, CarbonQuota, Activity

transaction_bp = Blueprint('transaction', __name__)

@transaction_bp.route('', methods=['GET'])
@jwt_required()
def get_transactions():
    """获取交易记录列表"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    if not user:
        return jsonify({'error': '用户不存在'}), 404
    
    # 获取查询参数
    status = request.args.get('status')
    
    # 构建查询
    query = Transaction.query.filter(
        (Transaction.buyer_id == current_user_id) | 
        (Transaction.seller_id == current_user_id)
    )
    
    if status:
        query = query.filter_by(status=status)
    
    # 执行查询
    transactions = query.order_by(Transaction.transaction_time.desc()).all()
    
    return jsonify([t.to_dict() for t in transactions]), 200

@transaction_bp.route('', methods=['POST'])
@jwt_required()
def create_transaction():
    """创建交易记录"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    if not user:
        return jsonify({'error': '用户不存在'}), 404
    
    data = request.json
    
    # 验证数据
    if not all(k in data for k in ['seller_id', 'amount', 'price']):
        return jsonify({'error': '缺少必要参数'}), 400
    
    # 验证卖方存在
    seller = User.query.get(data['seller_id'])
    if not seller:
        return jsonify({'error': '卖方用户不存在'}), 404
    
    # 验证买方不能是卖方
    if current_user_id == data['seller_id']:
        return jsonify({'error': '不能与自己交易'}), 400
    
    # 验证卖方有足够的配额
    current_year = datetime.now().year
    seller_quota = CarbonQuota.query.filter_by(
        enterprise_id=data['seller_id'],
        year=current_year
    ).first()
    
    if not seller_quota or seller_quota.current_amount < data['amount']:
        return jsonify({'error': '卖方配额不足'}), 400
    
    # 创建交易记录
    transaction = Transaction(
        seller_id=data['seller_id'],
        buyer_id=current_user_id,
        amount=data['amount'],
        price=data['price'],
        total_price=data['amount'] * data['price'],
        status='pending'
    )
    
    db.session.add(transaction)
    db.session.commit()
    
    # 提交到区块链
    timestamp = int(transaction.transaction_time.timestamp())
    hash_value = hashlib.sha256(f"{transaction.id}:{transaction.seller_id}:{transaction.buyer_id}:{transaction.amount}:{transaction.price}".encode()).hexdigest()
    
    blockchain_client = current_app.blockchain_client
    blockchain_result = blockchain_client.create_transaction(
        transaction.id,
        transaction.seller_id,
        transaction.buyer_id,
        int(transaction.amount * 100),  # 转换为整数
        int(transaction.price * 100),   # 转换为整数
        timestamp,
        hash_value
    )
    
    if blockchain_result['success']:
        # 更新数据库中的区块链信息
        transaction.blockchain_hash = blockchain_result['tx_hash']
        transaction.blockchain_block = blockchain_result['block_number']
        
        # 记录活动
        activity = Activity(
            user_id=current_user_id,
            activity_type='transaction_create',
            description=f"创建了与 {seller.company_name} 的碳配额交易，数量: {transaction.amount}吨，单价: {transaction.price}元/吨"
        )
        db.session.add(activity)
        
        db.session.commit()
        
        return jsonify({
            'message': '交易创建成功',
            'transaction': transaction.to_dict(),
            'blockchain': {
                'tx_hash': blockchain_result.get('tx_hash'),
                'block_number': blockchain_result.get('block_number')
            }
        }), 201
    else:
        # 即使区块链提交失败，我们也保留数据库记录
        # 记录活动
        activity = Activity(
            user_id=current_user_id,
            activity_type='transaction_create',
            description=f"创建了与 {seller.company_name} 的碳配额交易，但区块链提交失败"
        )
        db.session.add(activity)
        db.session.commit()
        
        return jsonify({
            'message': '交易创建成功，但区块链提交失败',
            'transaction': transaction.to_dict(),
            'blockchain_error': blockchain_result.get('error')
        }), 201

@transaction_bp.route('/<int:transaction_id>/confirm', methods=['POST'])
@jwt_required()
def confirm_transaction(transaction_id):
    """确认交易"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    if not user:
        return jsonify({'error': '用户不存在'}), 404
    
    # 获取交易记录
    transaction = Transaction.query.get(transaction_id)
    
    if not transaction:
        return jsonify({'error': '交易记录不存在'}), 404
    
    # 验证当前用户是卖方
    if transaction.seller_id != current_user_id:
        return jsonify({'error': '只有卖方可以确认交易'}), 403
    
    # 验证交易状态
    if transaction.status != 'pending':
        return jsonify({'error': f'交易已经是 {transaction.status} 状态，无法确认'}), 400
    
    # 更新交易状态
    transaction.status = 'completed'
    
    # 更新碳配额
    current_year = datetime.now().year
    
    # 卖方减少配额
    seller_quota = CarbonQuota.query.filter_by(
        enterprise_id=transaction.seller_id,
        year=current_year
    ).first()
    
    if seller_quota:
        seller_quota.current_amount -= transaction.amount
    
    # 买方增加配额
    buyer_quota = CarbonQuota.query.filter_by(
        enterprise_id=transaction.buyer_id,
        year=current_year
    ).first()
    
    if buyer_quota:
        buyer_quota.current_amount += transaction.amount
    else:
        # 如果买方没有当年配额记录，创建一个
        buyer_quota = CarbonQuota(
            enterprise_id=transaction.buyer_id,
            year=current_year,
            initial_amount=transaction.amount,
            current_amount=transaction.amount
        )
        db.session.add(buyer_quota)
    
    # 提交到区块链
    blockchain_client = current_app.blockchain_client
    blockchain_result = blockchain_client.update_transaction_status(
        transaction.id,
        'completed'
    )
    
    if blockchain_result['success']:
        # 记录活动
        activity = Activity(
            user_id=current_user_id,
            activity_type='transaction_confirm',
            description=f"确认了与 {transaction.buyer.company_name} 的碳配额交易，数量: {transaction.amount}吨，总价: {transaction.total_price}元"
        )
        db.session.add(activity)
        
        db.session.commit()
        
        return jsonify({
            'message': '交易确认成功',
            'transaction': transaction.to_dict()
        }), 200
    else:
        # 即使区块链提交失败，我们也更新数据库记录
        db.session.commit()
        
        return jsonify({
            'message': '交易确认成功，但区块链更新失败',
            'transaction': transaction.to_dict(),
            'blockchain_error': blockchain_result.get('error')
        }), 200

@transaction_bp.route('/<int:transaction_id>/cancel', methods=['POST'])
@jwt_required()
def cancel_transaction(transaction_id):
    """取消交易"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    if not user:
        return jsonify({'error': '用户不存在'}), 404
    
    # 获取交易记录
    transaction = Transaction.query.get(transaction_id)
    
    if not transaction:
        return jsonify({'error': '交易记录不存在'}), 404
    
    # 验证当前用户是交易的一方
    if transaction.seller_id != current_user_id and transaction.buyer_id != current_user_id:
        return jsonify({'error': '只有交易双方可以取消交易'}), 403
    
    # 验证交易状态
    if transaction.status != 'pending':
        return jsonify({'error': f'交易已经是 {transaction.status} 状态，无法取消'}), 400
    
    # 更新交易状态
    transaction.status = 'cancelled'
    
    # 提交到区块链
    blockchain_client = current_app.blockchain_client
    blockchain_result = blockchain_client.update_transaction_status(
        transaction.id,
        'cancelled'
    )
    
    if blockchain_result['success']:
        # 记录活动
        other_party = transaction.buyer if current_user_id == transaction.seller_id else transaction.seller
        activity = Activity(
            user_id=current_user_id,
            activity_type='transaction_cancel',
            description=f"取消了与 {other_party.company_name} 的碳配额交易，数量: {transaction.amount}吨"
        )
        db.session.add(activity)
        
        db.session.commit()
        
        return jsonify({
            'message': '交易取消成功',
            'transaction': transaction.to_dict()
        }), 200
    else:
        # 即使区块链提交失败，我们也更新数据库记录
        db.session.commit()
        
        return jsonify({
            'message': '交易取消成功，但区块链更新失败',
            'transaction': transaction.to_dict(),
            'blockchain_error': blockchain_result.get('error')
        }), 200
