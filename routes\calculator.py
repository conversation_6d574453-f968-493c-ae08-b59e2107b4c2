"""
碳足迹计算器模块路由
处理碳足迹计算相关的API请求
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity

from models import db, User, CarbonCalculation, Activity
from utils.carbon_calculator import CarbonCalculator

calculator_bp = Blueprint('calculator', __name__)

@calculator_bp.route('/calculate', methods=['POST'])
@jwt_required()
def calculate_carbon_footprint():
    """计算碳足迹"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    if not user:
        return jsonify({'error': '用户不存在'}), 404
    
    data = request.json
    
    if not data:
        return jsonify({'error': '缺少计算数据'}), 400
    
    try:
        # 计算碳足迹
        result = CarbonCalculator.calculate_total_emission(data)
        
        # 保存计算结果
        calculation = CarbonCalculation(
            enterprise_id=current_user_id,
            result_total=result['total_emission']
        )
        calculation.set_input_data(data)
        calculation.set_result_breakdown(result['breakdown'])
        
        db.session.add(calculation)
        
        # 记录活动
        activity = Activity(
            user_id=current_user_id,
            activity_type='carbon_calculation',
            description=f"进行了碳足迹计算，结果为 {result['total_emission']:.2f} {result['unit']}"
        )
        db.session.add(activity)
        
        db.session.commit()
        
        return jsonify({
            'calculation_id': calculation.id,
            'total_emission': result['total_emission'],
            'unit': result['unit'],
            'breakdown': result['breakdown']
        }), 200
    except Exception as e:
        return jsonify({'error': f'计算错误: {str(e)}'}), 400

@calculator_bp.route('/calculations', methods=['GET'])
@jwt_required()
def get_calculations():
    """获取计算历史"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    if not user:
        return jsonify({'error': '用户不存在'}), 404
    
    # 构建查询
    query = CarbonCalculation.query.filter_by(enterprise_id=current_user_id)
    
    # 执行查询
    calculations = query.order_by(CarbonCalculation.calculation_time.desc()).all()
    
    return jsonify([c.to_dict() for c in calculations]), 200

@calculator_bp.route('/calculations/<int:calculation_id>', methods=['GET'])
@jwt_required()
def get_calculation(calculation_id):
    """获取计算详情"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    if not user:
        return jsonify({'error': '用户不存在'}), 404
    
    # 获取计算记录
    calculation = CarbonCalculation.query.get(calculation_id)
    
    if not calculation:
        return jsonify({'error': '计算记录不存在'}), 404
    
    # 验证权限
    if calculation.enterprise_id != current_user_id and user.role != 'admin':
        return jsonify({'error': '无权访问此计算记录'}), 403
    
    return jsonify(calculation.to_dict()), 200

@calculator_bp.route('/emission-factors', methods=['GET'])
def get_emission_factors():
    """获取排放因子"""
    category = request.args.get('category')
    
    if category and category in CarbonCalculator.EMISSION_FACTORS:
        return jsonify(CarbonCalculator.EMISSION_FACTORS[category]), 200
    
    return jsonify(CarbonCalculator.EMISSION_FACTORS), 200
