.blockchain-verification {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 20px;
  background-color: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.verification-header {
  display: flex;
  align-items: center;
  padding: 15px;
  cursor: pointer;
  background-color: #f9f9f9;
  border-bottom: 1px solid #e0e0e0;
}

.verification-status {
  margin-right: 15px;
  font-size: 20px;
}

.verification-title {
  flex-grow: 1;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.verification-data-type {
  margin-left: 10px;
  font-size: 14px;
  color: #757575;
  background-color: #f1f1f1;
  padding: 2px 8px;
  border-radius: 12px;
}

.verification-toggle {
  color: #9e9e9e;
}

.verification-details {
  padding: 15px;
}

.verification-error {
  background-color: #ffebee;
  color: #d32f2f;
  padding: 10px 15px;
  border-radius: 4px;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.verification-error i {
  margin-right: 10px;
}

.verification-result {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.result-label {
  font-weight: 500;
  margin-right: 10px;
}

.result-value {
  font-weight: 600;
}

.blockchain-data {
  margin-bottom: 20px;
}

.blockchain-data h4 {
  font-size: 16px;
  margin-bottom: 10px;
  color: #333;
}

.data-table {
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 10px;
  font-family: monospace;
  font-size: 14px;
}

.data-row {
  display: flex;
  margin-bottom: 5px;
  padding: 5px 0;
  border-bottom: 1px solid #eee;
}

.data-row:last-child {
  border-bottom: none;
}

.data-key {
  width: 150px;
  color: #555;
  font-weight: 500;
}

.data-value {
  flex-grow: 1;
  word-break: break-all;
}

.data-differences {
  margin-bottom: 20px;
}

.data-differences h4 {
  font-size: 16px;
  margin-bottom: 10px;
  color: #d32f2f;
}

.data-differences ul {
  list-style: none;
  padding: 0;
  margin: 0;
  background-color: #fff8e1;
  border-radius: 4px;
  padding: 10px;
}

.data-differences li {
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px dashed #e0e0e0;
}

.data-differences li:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.diff-field {
  display: block;
  font-weight: 500;
  margin-bottom: 5px;
  color: #333;
}

.diff-db, .diff-blockchain {
  display: block;
  font-family: monospace;
  font-size: 13px;
  padding: 3px 0;
}

.diff-db {
  color: #2196f3;
}

.diff-blockchain {
  color: #ff9800;
}

.verification-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
}

.btn-verify {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.btn-verify:hover {
  background-color: #45a049;
}

.btn-verify:disabled {
  background-color: #9e9e9e;
  cursor: not-allowed;
}
