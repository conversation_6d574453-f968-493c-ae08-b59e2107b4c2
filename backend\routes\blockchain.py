"""
区块链相关路由
提供区块链交易查询、数据验证等功能
"""

from flask import Blueprint, request, jsonify, current_app, session
from flask_jwt_extended import jwt_required, get_jwt_identity
from datetime import datetime
import os
import json
from web3 import Web3
from dotenv import load_dotenv

from backend import db
from backend.models.user import User
from backend.models.emission import EmissionData, Emission
from backend.models.verification import Verification
from backend.models.transaction import Transaction
from backend.models.penalty import Penalty
from backend.models.activity import Activity
from backend.blockchain.deploy_contract import deploy_contract

blockchain_bp = Blueprint('blockchain', __name__)

@blockchain_bp.route('/config', methods=['GET'])
def get_blockchain_config():
    """获取区块链配置"""
    # 检查用户是否已登录
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': '请先登录'}), 401

    # 从环境变量获取配置
    ethereum_node_url = os.environ.get('ETHEREUM_NODE_URL', 'http://127.0.0.1:8545')
    contract_address = os.environ.get('CONTRACT_ADDRESS', '')

    # 根据用户角色获取相应的地址和私钥
    user_address = ''
    user_private_key = ''

    if session.get('role') == 'admin':
        user_address = os.environ.get('ADMIN_ADDRESS', '')
        user_private_key = os.environ.get('ADMIN_PRIVATE_KEY', '')
    elif session.get('role') == 'enterprise':
        # 简单起见，这里假设企业用户ID为2对应企业1，ID为3对应企业2，ID为4对应企业3
        if session.get('user_id') == 2:
            user_address = os.environ.get('ENTERPRISE_1_ADDRESS', '')
            user_private_key = os.environ.get('ENTERPRISE_1_KEY', '')
        elif session.get('user_id') == 3:
            user_address = os.environ.get('ENTERPRISE_2_ADDRESS', '')
            user_private_key = os.environ.get('ENTERPRISE_2_KEY', '')
        elif session.get('user_id') == 4:
            user_address = os.environ.get('ENTERPRISE_3_ADDRESS', '')
            user_private_key = os.environ.get('ENTERPRISE_3_KEY', '')
    elif session.get('role') == 'verifier':
        # 简单起见，这里假设核查机构用户ID为5对应核查机构1，ID为6对应核查机构2
        if session.get('user_id') == 5:
            user_address = os.environ.get('VERIFIER_1_ADDRESS', '')
            user_private_key = os.environ.get('VERIFIER_1_KEY', '')
        elif session.get('user_id') == 6:
            user_address = os.environ.get('VERIFIER_2_ADDRESS', '')
            user_private_key = os.environ.get('VERIFIER_2_KEY', '')

    # 尝试连接区块链
    blockchain_status = {
        'connected': False,
        'network': '本地测试网',
        'block_number': '未知',
        'contract_address': contract_address,
        'user_address': user_address
    }

    try:
        web3 = Web3(Web3.HTTPProvider(ethereum_node_url))
        if web3.is_connected():
            blockchain_status['connected'] = True
            blockchain_status['block_number'] = web3.eth.block_number
    except Exception as e:
        print(f"区块链连接错误: {str(e)}")

    # 构建响应
    response = {
        'success': True,
        'ethereum_node_url': ethereum_node_url,
        'contract_address': contract_address,
        'user_address': user_address,
        'user_private_key': user_private_key,
        'blockchain_status': blockchain_status
    }

    return jsonify(response)

@blockchain_bp.route('/config', methods=['POST'])
def update_blockchain_config():
    """更新区块链配置"""
    # 检查用户是否已登录
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': '请先登录'}), 401

    # 获取请求数据
    data = request.json

    # 验证必填字段
    required_fields = ['ethereum_node_url']
    for field in required_fields:
        if field not in data:
            return jsonify({'success': False, 'message': f'缺少必填字段: {field}'}), 400

    # 根据用户角色更新相应的配置
    env_vars = {}

    # 更新以太坊节点URL
    env_vars['ETHEREUM_NODE_URL'] = data.get('ethereum_node_url')

    # 更新合约地址
    if 'contract_address' in data and data['contract_address']:
        env_vars['CONTRACT_ADDRESS'] = data['contract_address']

    # 根据用户角色更新地址和私钥
    if session.get('role') == 'admin':
        if 'admin_address' in data and data['admin_address']:
            env_vars['ADMIN_ADDRESS'] = data['admin_address']
        if 'admin_private_key' in data and data['admin_private_key']:
            env_vars['ADMIN_PRIVATE_KEY'] = data['admin_private_key']
    elif session.get('role') == 'enterprise':
        # 简单起见，这里假设企业用户ID为2对应企业1，ID为3对应企业2，ID为4对应企业3
        if session.get('user_id') == 2 and 'private_key' in data and data['private_key']:
            env_vars['ENTERPRISE_1_KEY'] = data['private_key']
        elif session.get('user_id') == 3 and 'private_key' in data and data['private_key']:
            env_vars['ENTERPRISE_2_KEY'] = data['private_key']
        elif session.get('user_id') == 4 and 'private_key' in data and data['private_key']:
            env_vars['ENTERPRISE_3_KEY'] = data['private_key']
    elif session.get('role') == 'verifier':
        # 简单起见，这里假设核查机构用户ID为5对应核查机构1，ID为6对应核查机构2
        if session.get('user_id') == 5 and 'private_key' in data and data['private_key']:
            env_vars['VERIFIER_1_KEY'] = data['private_key']
        elif session.get('user_id') == 6 and 'private_key' in data and data['private_key']:
            env_vars['VERIFIER_2_KEY'] = data['private_key']

    # 更新.env文件
    try:
        # 获取.env文件路径
        env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), '.env')

        # 读取现有.env文件内容
        env_content = ""
        if os.path.exists(env_path):
            with open(env_path, 'r') as f:
                env_content = f.read()

        # 更新环境变量
        for key, value in env_vars.items():
            if f'{key}=' in env_content:
                env_content = '\n'.join([line if not line.startswith(f'{key}=') else f'{key}={value}' for line in env_content.split('\n')])
            else:
                env_content += f'\n{key}={value}'

        # 写入.env文件
        with open(env_path, 'w') as f:
            f.write(env_content)

        # 重新加载环境变量
        load_dotenv(env_path)

        # 更新进程中的环境变量
        for key, value in env_vars.items():
            os.environ[key] = value

        return jsonify({'success': True, 'message': '区块链配置已更新'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'更新配置失败: {str(e)}'}), 500

@blockchain_bp.route('/deploy-contract', methods=['POST'])
def deploy_contract_route():
    """部署智能合约"""
    # 检查用户是否已登录
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': '请先登录'}), 401

    # 检查用户是否是管理员
    if session.get('role') != 'admin':
        return jsonify({'success': False, 'message': '只有管理员可以部署合约'}), 403

    # 从环境变量获取以太坊节点URL和管理员私钥
    ethereum_node_url = os.environ.get('ETHEREUM_NODE_URL', 'http://127.0.0.1:8545')
    admin_private_key = os.environ.get('ADMIN_PRIVATE_KEY', '')

    # 部署合约
    result = deploy_contract(ethereum_node_url, admin_private_key)

    return jsonify(result)

@blockchain_bp.route('/test-connection', methods=['GET'])
def test_blockchain_connection():
    """测试区块链连接"""
    # 检查用户是否已登录
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': '请先登录'}), 401

    # 从环境变量获取以太坊节点URL
    ethereum_node_url = os.environ.get('ETHEREUM_NODE_URL', 'http://127.0.0.1:8545')

    # 尝试连接区块链
    try:
        web3 = Web3(Web3.HTTPProvider(ethereum_node_url))
        if web3.is_connected():
            return jsonify({
                'success': True,
                'message': '成功连接到以太坊节点',
                'block_number': web3.eth.block_number,
                'network_id': web3.net.version
            })
        else:
            return jsonify({'success': False, 'message': '无法连接到以太坊节点'}), 500
    except Exception as e:
        return jsonify({'success': False, 'message': f'连接测试失败: {str(e)}'}), 500

@blockchain_bp.route('/transactions', methods=['GET'])
@jwt_required()
def get_transactions():
    """获取区块链交易记录"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)

    if not user:
        return jsonify({'error': '用户不存在'}), 404

    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    limit = request.args.get('limit', 10, type=int)
    entity_id = request.args.get('entity_id', type=int)
    entity_type = request.args.get('entity_type')
    transaction_type = request.args.get('transaction_type')

    # 限制每页最大记录数
    if limit > 50:
        limit = 50

    # 构建查询
    transactions = []

    # 根据用户角色和查询参数获取交易记录
    if user.role == 'admin':
        # 管理员可以查看所有交易记录
        pass
    elif user.role == 'enterprise':
        # 企业用户只能查看自己的交易记录
        entity_id = current_user_id
        entity_type = 'enterprise'
    elif user.role == 'verifier':
        # 核查机构只能查看自己的交易记录
        entity_id = current_user_id
        entity_type = 'verifier'
    else:
        return jsonify({'error': '无效的用户角色'}), 400

    # 获取排放数据交易记录
    if not transaction_type or transaction_type == 'emission_data':
        emission_query = EmissionData.query

        if entity_id and entity_type == 'enterprise':
            emission_query = emission_query.filter_by(enterprise_id=entity_id)

        emissions = emission_query.filter(EmissionData.blockchain_hash.isnot(None)).\
            order_by(EmissionData.submission_time.desc()).\
            offset((page - 1) * limit).\
            limit(limit).\
            all()

        for emission in emissions:
            transactions.append({
                'tx_hash': emission.blockchain_hash,
                'block_number': emission.blockchain_block,
                'timestamp': emission.submission_time.isoformat() if emission.submission_time else None,
                'status': 'confirmed' if emission.blockchain_block else 'pending',
                'transaction_type': 'emission_data',
                'entity_id': emission.enterprise_id,
                'entity_type': 'enterprise',
                'data_id': emission.id
            })

    # 获取核查记录交易记录
    if not transaction_type or transaction_type == 'verification':
        verification_query = Verification.query

        if entity_id:
            if entity_type == 'enterprise':
                # 获取与企业相关的核查记录
                verification_query = verification_query.join(EmissionData).\
                    filter(EmissionData.enterprise_id == entity_id)
            elif entity_type == 'verifier':
                # 获取核查机构的核查记录
                verification_query = verification_query.filter_by(verifier_id=entity_id)

        verifications = verification_query.filter(Verification.blockchain_hash.isnot(None)).\
            order_by(Verification.verification_time.desc()).\
            offset((page - 1) * limit).\
            limit(limit).\
            all()

        for verification in verifications:
            transactions.append({
                'tx_hash': verification.blockchain_hash,
                'block_number': verification.blockchain_block,
                'timestamp': verification.verification_time.isoformat() if verification.verification_time else None,
                'status': 'confirmed' if verification.blockchain_block else 'pending',
                'transaction_type': 'verification',
                'entity_id': verification.verifier_id,
                'entity_type': 'verifier',
                'data_id': verification.id
            })

    # 获取交易记录
    if not transaction_type or transaction_type == 'carbon_transaction':
        transaction_query = Transaction.query

        if entity_id and entity_type == 'enterprise':
            # 获取企业的买卖交易记录
            transaction_query = transaction_query.filter(
                (Transaction.buyer_id == entity_id) | (Transaction.seller_id == entity_id)
            )

        transactions_data = transaction_query.filter(Transaction.blockchain_hash.isnot(None)).\
            order_by(Transaction.transaction_time.desc()).\
            offset((page - 1) * limit).\
            limit(limit).\
            all()

        for transaction in transactions_data:
            transactions.append({
                'tx_hash': transaction.blockchain_hash,
                'block_number': transaction.blockchain_block,
                'timestamp': transaction.transaction_time.isoformat() if transaction.transaction_time else None,
                'status': 'confirmed' if transaction.blockchain_block else 'pending',
                'transaction_type': 'carbon_transaction',
                'entity_id': transaction.buyer_id,
                'entity_type': 'enterprise',
                'data_id': transaction.id
            })

    # 获取惩罚记录
    if not transaction_type or transaction_type == 'penalty':
        penalty_query = Penalty.query

        if entity_id and entity_type == 'enterprise':
            penalty_query = penalty_query.filter_by(enterprise_id=entity_id)

        penalties = penalty_query.filter(Penalty.blockchain_hash.isnot(None)).\
            order_by(Penalty.penalty_time.desc()).\
            offset((page - 1) * limit).\
            limit(limit).\
            all()

        for penalty in penalties:
            transactions.append({
                'tx_hash': penalty.blockchain_hash,
                'block_number': penalty.blockchain_block,
                'timestamp': penalty.penalty_time.isoformat() if penalty.penalty_time else None,
                'status': 'confirmed' if penalty.blockchain_block else 'pending',
                'transaction_type': 'penalty',
                'entity_id': penalty.enterprise_id,
                'entity_type': 'enterprise',
                'data_id': penalty.id
            })

    # 按时间戳排序
    transactions.sort(key=lambda x: x.get('timestamp', ''), reverse=True)

    # 分页
    start = (page - 1) * limit
    end = start + limit
    paginated_transactions = transactions[start:end]

    return jsonify({
        'transactions': paginated_transactions,
        'total': len(transactions),
        'page': page,
        'limit': limit
    }), 200

@blockchain_bp.route('/verify', methods=['GET'])
@jwt_required()
def verify_data():
    """验证数据与区块链上的数据是否一致"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)

    if not user:
        return jsonify({'error': '用户不存在'}), 404

    # 获取查询参数
    data_id = request.args.get('data_id', type=int)
    data_type = request.args.get('data_type')

    if not data_id or not data_type:
        return jsonify({'error': '缺少必要参数'}), 400

    # 根据数据类型获取数据
    db_data = None
    blockchain_data = None

    try:
        if data_type == 'emission':
            # 获取排放数据
            db_data = EmissionData.query.get(data_id)
            if not db_data:
                return jsonify({'error': '排放数据不存在'}), 404

            # 检查权限
            if user.role != 'admin' and db_data.enterprise_id != current_user_id:
                return jsonify({'error': '无权验证此数据'}), 403

            # 从区块链获取数据
            if db_data.blockchain_hash:
                blockchain_data = current_app.blockchain_client.get_emission_data(data_id)

        elif data_type == 'verification':
            # 获取核查记录
            db_data = Verification.query.get(data_id)
            if not db_data:
                return jsonify({'error': '核查记录不存在'}), 404

            # 检查权限
            emission = EmissionData.query.get(db_data.emission_data_id)
            if user.role != 'admin' and db_data.verifier_id != current_user_id and emission.enterprise_id != current_user_id:
                return jsonify({'error': '无权验证此数据'}), 403

            # 从区块链获取数据
            if db_data.blockchain_hash:
                blockchain_data = current_app.blockchain_client.get_verification_result(data_id)

        elif data_type == 'transaction':
            # 获取交易记录
            db_data = Transaction.query.get(data_id)
            if not db_data:
                return jsonify({'error': '交易记录不存在'}), 404

            # 检查权限
            if user.role != 'admin' and db_data.buyer_id != current_user_id and db_data.seller_id != current_user_id:
                return jsonify({'error': '无权验证此数据'}), 403

            # 从区块链获取数据
            if db_data.blockchain_hash:
                blockchain_data = current_app.blockchain_client.get_transaction(data_id)

        elif data_type == 'penalty':
            # 获取惩罚记录
            db_data = Penalty.query.get(data_id)
            if not db_data:
                return jsonify({'error': '惩罚记录不存在'}), 404

            # 检查权限
            if user.role != 'admin' and db_data.enterprise_id != current_user_id:
                return jsonify({'error': '无权验证此数据'}), 403

            # 从区块链获取数据
            if db_data.blockchain_hash:
                blockchain_data = current_app.blockchain_client.get_penalty(data_id)

        else:
            return jsonify({'error': '无效的数据类型'}), 400

        # 如果没有区块链数据
        if not blockchain_data:
            return jsonify({
                'verified': False,
                'message': '数据未上链或无法从区块链获取数据'
            }), 200

        # 比较数据库数据和区块链数据
        differences = []
        verified = True

        # 根据数据类型比较不同字段
        if data_type == 'emission':
            # 比较排放数据
            if str(db_data.emission_amount) != str(blockchain_data.get('emission_amount')):
                differences.append({
                    'field': 'emission_amount',
                    'db_value': str(db_data.emission_amount),
                    'blockchain_value': str(blockchain_data.get('emission_amount'))
                })
                verified = False

        elif data_type == 'verification':
            # 比较核查记录
            if db_data.conclusion != blockchain_data.get('conclusion'):
                differences.append({
                    'field': 'conclusion',
                    'db_value': db_data.conclusion,
                    'blockchain_value': blockchain_data.get('conclusion')
                })
                verified = False

        elif data_type == 'transaction':
            # 比较交易记录
            if str(db_data.amount) != str(blockchain_data.get('amount')):
                differences.append({
                    'field': 'amount',
                    'db_value': str(db_data.amount),
                    'blockchain_value': str(blockchain_data.get('amount'))
                })
                verified = False

            if str(db_data.price) != str(blockchain_data.get('price')):
                differences.append({
                    'field': 'price',
                    'db_value': str(db_data.price),
                    'blockchain_value': str(blockchain_data.get('price'))
                })
                verified = False

        elif data_type == 'penalty':
            # 比较惩罚记录
            if str(db_data.amount) != str(blockchain_data.get('amount')):
                differences.append({
                    'field': 'amount',
                    'db_value': str(db_data.amount),
                    'blockchain_value': str(blockchain_data.get('amount'))
                })
                verified = False

        # 记录活动
        activity = Activity(
            user_id=current_user_id,
            activity_type='blockchain_verification',
            description=f'用户验证了{data_type}类型的数据，ID: {data_id}，结果: {"一致" if verified else "不一致"}'
        )
        db.session.add(activity)
        db.session.commit()

        return jsonify({
            'verified': verified,
            'blockchain_data': blockchain_data,
            'differences': differences if differences else None,
            'message': '数据一致' if verified else '数据不一致'
        }), 200

    except Exception as e:
        return jsonify({
            'verified': False,
            'message': f'验证过程中发生错误: {str(e)}'
        }), 500
