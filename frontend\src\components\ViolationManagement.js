import React, { useState, useEffect } from 'react';
import axios from 'axios';
import '../styles/ViolationManagement.css';

function ViolationManagement() {
  const [violations, setViolations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [formData, setFormData] = useState({
    company: '',
    type: '',
    description: '',
    penalty: '',
    deadline: ''
  });

  useEffect(() => {
    fetchViolations();
  }, []);

  const fetchViolations = async () => {
    try {
      const response = await axios.get('/api/violations', {
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      setViolations(response.data);
      setLoading(false);
    } catch (err) {
      setError('获取违规记录失败');
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await axios.post('/api/violations', formData, {
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      fetchViolations();
      setFormData({
        company: '',
        type: '',
        description: '',
        penalty: '',
        deadline: ''
      });
    } catch (err) {
      setError('创建违规记录失败');
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleStatusChange = async (violationId, newStatus) => {
    try {
      await axios.patch(`/api/violations/${violationId}`, 
        { status: newStatus },
        { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } }
      );
      fetchViolations();
    } catch (err) {
      setError('更新违规状态失败');
    }
  };

  if (loading) return <div className="loading">加载中...</div>;

  return (
    <div className="violation-management">
      <h2>违规管理</h2>
      {error && <div className="alert alert-danger">{error}</div>}
      
      <div className="violation-form">
        <h3>创建新违规记录</h3>
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="company">违规企业</label>
            <input
              type="text"
              id="company"
              name="company"
              value={formData.company}
              onChange={handleChange}
              required
              className="form-control"
            />
          </div>
          <div className="form-group">
            <label htmlFor="type">违规类型</label>
            <select
              id="type"
              name="type"
              value={formData.type}
              onChange={handleChange}
              required
              className="form-control"
            >
              <option value="">请选择类型</option>
              <option value="emission_exceed">排放超标</option>
              <option value="data_fraud">数据造假</option>
              <option value="report_delay">报告延迟</option>
              <option value="other">其他违规</option>
            </select>
          </div>
          <div className="form-group">
            <label htmlFor="description">违规描述</label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              required
              className="form-control"
              rows="3"
            />
          </div>
          <div className="form-group">
            <label htmlFor="penalty">处罚措施</label>
            <textarea
              id="penalty"
              name="penalty"
              value={formData.penalty}
              onChange={handleChange}
              required
              className="form-control"
              rows="3"
            />
          </div>
          <div className="form-group">
            <label htmlFor="deadline">整改期限</label>
            <input
              type="date"
              id="deadline"
              name="deadline"
              value={formData.deadline}
              onChange={handleChange}
              required
              className="form-control"
            />
          </div>
          <button type="submit" className="btn btn-primary">
            创建记录
          </button>
        </form>
      </div>

      <div className="violation-list">
        <h3>违规记录</h3>
        <div className="table-responsive">
          <table className="table">
            <thead>
              <tr>
                <th>违规企业</th>
                <th>违规类型</th>
                <th>违规描述</th>
                <th>处罚措施</th>
                <th>整改期限</th>
                <th>状态</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              {violations.map(violation => (
                <tr key={violation.id}>
                  <td>{violation.company}</td>
                  <td>
                    <span className={`type ${violation.type}`}>
                      {violation.type === 'emission_exceed' ? '排放超标' :
                       violation.type === 'data_fraud' ? '数据造假' :
                       violation.type === 'report_delay' ? '报告延迟' : '其他违规'}
                    </span>
                  </td>
                  <td>{violation.description}</td>
                  <td>{violation.penalty}</td>
                  <td>{new Date(violation.deadline).toLocaleDateString()}</td>
                  <td>
                    <span className={`status ${violation.status}`}>
                      {violation.status === 'pending' ? '待处理' :
                       violation.status === 'processing' ? '处理中' :
                       violation.status === 'resolved' ? '已解决' : '已关闭'}
                    </span>
                  </td>
                  <td>
                    <select
                      value={violation.status}
                      onChange={(e) => handleStatusChange(violation.id, e.target.value)}
                      className="status-select"
                    >
                      <option value="pending">待处理</option>
                      <option value="processing">处理中</option>
                      <option value="resolved">已解决</option>
                      <option value="closed">已关闭</option>
                    </select>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default ViolationManagement; 