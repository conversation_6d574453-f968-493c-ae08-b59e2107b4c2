## 6.4 安全测试

### 6.4.1 测试方法

安全测试采用多种工具和方法，对系统的安全性进行全面评估。测试方法如表6-14所示。

**表6-14 安全测试方法**

| 测试类型 | 测试工具 | 测试目标 | 测试内容 |
| --- | --- | --- | --- |
| 漏洞扫描 | OWASP ZAP | Web应用漏洞 | SQL注入、XSS、CSRF等常见Web漏洞 |
| 渗透测试 | Metasploit | 系统安全漏洞 | 服务器漏洞、配置错误、权限提升等 |
| 代码审计 | SonarQube | 代码安全问题 | 代码质量、安全漏洞、最佳实践 |
| 智能合约审计 | Mythril | 智能合约漏洞 | 重入攻击、整数溢出、权限控制等 |
| 安全配置检查 | 自定义脚本 | 系统配置安全 | 服务器配置、数据库配置、网络配置等 |

### 6.4.2 Web应用安全测试结果

Web应用安全测试结果如表6-15所示。

**表6-15 Web应用安全测试结果**

| 漏洞类型 | 严重程度 | 发现数量 | 修复数量 | 修复率(%) |
| --- | --- | --- | --- | --- |
| SQL注入 | 高 | 0 | 0 | 100 |
| 跨站脚本(XSS) | 高 | 2 | 2 | 100 |
| 跨站请求伪造(CSRF) | 中 | 1 | 1 | 100 |
| 敏感信息泄露 | 中 | 3 | 3 | 100 |
| 不安全的直接对象引用 | 中 | 2 | 2 | 100 |
| 安全配置错误 | 低 | 5 | 5 | 100 |
| 未验证的重定向 | 低 | 1 | 1 | 100 |

### 6.4.3 智能合约安全测试结果

智能合约安全测试结果如表6-16所示。

**表6-16 智能合约安全测试结果**

| 漏洞类型 | 严重程度 | 发现数量 | 修复数量 | 修复率(%) |
| --- | --- | --- | --- | --- |
| 重入攻击 | 高 | 0 | 0 | 100 |
| 整数溢出 | 高 | 1 | 1 | 100 |
| 权限控制不当 | 高 | 0 | 0 | 100 |
| Gas限制问题 | 中 | 2 | 2 | 100 |
| 时间戳依赖 | 中 | 1 | 1 | 100 |
| 随机数生成问题 | 中 | 0 | 0 | 100 |
| 未检查的外部调用 | 低 | 2 | 2 | 100 |

### 6.4.4 安全配置检查结果

安全配置检查结果如表6-17所示。

**表6-17 安全配置检查结果**

| 配置项 | 检查结果 | 是否符合要求 |
| --- | --- | --- |
| HTTPS配置 | 已启用HTTPS，使用TLS 1.3 | 是 |
| 密码策略 | 强制使用强密码，定期更换 | 是 |
| 会话管理 | 安全的会话ID，适当的超时设置 | 是 |
| 访问控制 | 基于角色的访问控制，最小权限原则 | 是 |
| 错误处理 | 不泄露敏感信息的错误消息 | 是 |
| 日志记录 | 记录关键操作，保存足够时间 | 是 |
| 数据库安全 | 参数化查询，最小权限账户 | 是 |
| 文件上传 | 验证文件类型，限制文件大小 | 是 |
| 区块链私钥管理 | 安全存储私钥，不在代码中硬编码 | 是 |

### 6.4.5 安全测试结果分析

通过对系统的安全测试，我们得到以下结论：

1. **Web应用安全**：系统在Web应用安全方面表现良好，未发现高危SQL注入漏洞，发现的XSS、CSRF等漏洞已全部修复。系统采用了参数化查询、输入验证、CSRF令牌等安全措施，有效防止了常见的Web攻击。

2. **智能合约安全**：智能合约安全测试未发现重入攻击和权限控制不当等高危漏洞，发现的整数溢出、Gas限制等问题已全部修复。系统采用了SafeMath库、权限控制修饰器等安全措施，确保了智能合约的安全性。

3. **安全配置**：系统的安全配置符合要求，已启用HTTPS、强密码策略、安全的会话管理、基于角色的访问控制等安全措施，确保了系统的整体安全性。

4. **数据安全**：系统对敏感数据（如密码、私钥）进行了加密存储，采用了参数化查询防止SQL注入，实施了最小权限原则，确保了数据的安全性。

5. **区块链安全**：系统安全存储区块链私钥，不在代码中硬编码敏感信息，采用了权限控制确保只有授权用户能够执行特定操作，确保了区块链部分的安全性。

总体而言，系统在安全性方面表现良好，已采取了多种安全措施防止常见的安全威胁，能够满足碳排放核查系统的安全需求。

## 6.5 兼容性测试

### 6.5.1 测试方法

兼容性测试主要测试系统在不同浏览器、操作系统和设备上的兼容性。测试方法如表6-18所示。

**表6-18 兼容性测试方法**

| 测试类型 | 测试环境 | 测试内容 |
| --- | --- | --- |
| 浏览器兼容性 | Chrome、Firefox、Safari、Edge | 页面布局、功能正常、性能表现 |
| 操作系统兼容性 | Windows、macOS、Linux、iOS、Android | 系统功能、性能表现 |
| 设备兼容性 | 桌面电脑、笔记本电脑、平板电脑、智能手机 | 响应式设计、触摸操作 |
| 分辨率兼容性 | 不同屏幕分辨率 | 页面布局、可读性 |

### 6.5.2 浏览器兼容性测试结果

浏览器兼容性测试结果如表6-19所示。

**表6-19 浏览器兼容性测试结果**

| 浏览器 | 版本 | 页面布局 | 功能正常 | 性能表现 | 测试结论 |
| --- | --- | --- | --- | --- | --- |
| Chrome | 93.0 | 正常 | 正常 | 良好 | 通过 |
| Firefox | 92.0 | 正常 | 正常 | 良好 | 通过 |
| Safari | 15.0 | 正常 | 正常 | 良好 | 通过 |
| Edge | 93.0 | 正常 | 正常 | 良好 | 通过 |
| IE | 11.0 | 轻微偏差 | 基本正常 | 一般 | 基本通过 |

### 6.5.3 设备兼容性测试结果

设备兼容性测试结果如表6-20所示。

**表6-20 设备兼容性测试结果**

| 设备类型 | 操作系统 | 浏览器 | 页面布局 | 功能正常 | 性能表现 | 测试结论 |
| --- | --- | --- | --- | --- | --- | --- |
| 桌面电脑 | Windows 10 | Chrome 93.0 | 正常 | 正常 | 良好 | 通过 |
| 笔记本电脑 | macOS 11.5 | Safari 15.0 | 正常 | 正常 | 良好 | 通过 |
| 平板电脑 | iOS 15.0 | Safari 15.0 | 正常 | 正常 | 良好 | 通过 |
| 平板电脑 | Android 11 | Chrome 93.0 | 正常 | 正常 | 良好 | 通过 |
| 智能手机 | iOS 15.0 | Safari 15.0 | 正常 | 正常 | 良好 | 通过 |
| 智能手机 | Android 11 | Chrome 93.0 | 正常 | 正常 | 良好 | 通过 |

### 6.5.4 兼容性测试结果分析

通过对系统的兼容性测试，我们得到以下结论：

1. **浏览器兼容性**：系统在Chrome、Firefox、Safari、Edge等主流浏览器上表现良好，页面布局正常，功能正常，性能良好。在IE 11上存在轻微的页面布局偏差，但基本功能正常，可以接受。

2. **操作系统兼容性**：系统在Windows、macOS、Linux、iOS、Android等主流操作系统上表现良好，功能正常，性能良好。

3. **设备兼容性**：系统在桌面电脑、笔记本电脑、平板电脑、智能手机等不同设备上表现良好，响应式设计能够适应不同屏幕尺寸，触摸操作正常。

4. **分辨率兼容性**：系统在不同屏幕分辨率下表现良好，页面布局自适应，内容可读性良好。

总体而言，系统在兼容性方面表现良好，能够在各种主流浏览器、操作系统和设备上正常运行，满足用户在不同环境下的使用需求。

## 6.6 用户体验测试

### 6.6.1 测试方法

用户体验测试采用用户调研和任务完成测试的方法，评估系统的易用性、满意度和效率。测试方法如表6-21所示。

**表6-21 用户体验测试方法**

| 测试类型 | 测试对象 | 测试内容 | 评估指标 |
| --- | --- | --- | --- |
| 任务完成测试 | 企业用户、核查机构用户、管理员 | 完成典型任务的过程 | 完成时间、错误次数、成功率 |
| 满意度调查 | 企业用户、核查机构用户、管理员 | 用户对系统的满意度 | 满意度评分（1-5分） |
| 易用性评估 | 企业用户、核查机构用户、管理员 | 系统的易用性 | 易用性评分（1-5分） |
| 界面评估 | 企业用户、核查机构用户、管理员 | 界面设计的美观性和一致性 | 界面评分（1-5分） |

### 6.6.2 任务完成测试结果

任务完成测试结果如表6-22所示。

**表6-22 任务完成测试结果**

| 用户角色 | 任务 | 平均完成时间(s) | 平均错误次数 | 成功率(%) |
| --- | --- | --- | --- | --- |
| 企业用户 | 注册账户 | 120 | 0.5 | 100 |
| 企业用户 | 登录系统 | 15 | 0.1 | 100 |
| 企业用户 | 创建排放数据 | 180 | 0.8 | 95 |
| 企业用户 | 提交排放数据 | 30 | 0.2 | 100 |
| 企业用户 | 创建交易 | 90 | 0.5 | 98 |
| 核查机构用户 | 登录系统 | 15 | 0.1 | 100 |
| 核查机构用户 | 查看核查任务 | 20 | 0.0 | 100 |
| 核查机构用户 | 提交核查结果 | 150 | 0.6 | 97 |
| 管理员 | 登录系统 | 15 | 0.0 | 100 |
| 管理员 | 管理用户 | 60 | 0.3 | 100 |
| 管理员 | 分配核查任务 | 45 | 0.2 | 100 |

### 6.6.3 满意度调查结果

满意度调查结果如表6-23所示。

**表6-23 满意度调查结果**

| 评估项目 | 企业用户评分 | 核查机构用户评分 | 管理员评分 | 平均评分 |
| --- | --- | --- | --- | --- |
| 系统整体满意度 | 4.5 | 4.3 | 4.7 | 4.5 |
| 功能完整性 | 4.6 | 4.4 | 4.8 | 4.6 |
| 操作便捷性 | 4.3 | 4.2 | 4.5 | 4.3 |
| 响应速度 | 4.4 | 4.3 | 4.6 | 4.4 |
| 界面设计 | 4.5 | 4.4 | 4.7 | 4.5 |
| 帮助与支持 | 4.2 | 4.1 | 4.4 | 4.2 |
| 数据可视化 | 4.6 | 4.5 | 4.8 | 4.6 |
| 区块链集成 | 4.4 | 4.3 | 4.6 | 4.4 |

### 6.6.4 用户体验测试结果分析

通过对系统的用户体验测试，我们得到以下结论：

1. **任务完成效率**：用户完成各项任务的平均时间在15-180秒之间，平均错误次数在0-0.8次之间，成功率在95-100%之间，表明系统操作流程清晰，用户能够高效地完成任务。

2. **用户满意度**：系统的整体满意度评分为4.5分（满分5分），各项评分均在4.2分以上，表明用户对系统的满意度较高。

3. **易用性**：操作便捷性评分为4.3分，表明系统的易用性良好，用户能够轻松上手使用系统。

4. **界面设计**：界面设计评分为4.5分，表明系统的界面设计美观、一致，用户体验良好。

5. **功能完整性**：功能完整性评分为4.6分，表明系统的功能能够满足用户的需求。

6. **区块链集成**：区块链集成评分为4.4分，表明系统的区块链功能设计合理，用户能够理解和使用区块链相关功能。

总体而言，系统在用户体验方面表现良好，用户能够高效地完成任务，对系统的满意度较高，系统的易用性、界面设计和功能完整性都得到了用户的认可。

## 6.7 本章小结

本章对基于区块链的碳排放核查系统进行了全面测试，包括功能测试、性能测试、安全测试、兼容性测试和用户体验测试，验证了系统的功能完整性、性能表现、安全性、兼容性和用户体验。

通过功能测试，我们验证了系统的各项功能符合需求规格说明书中的要求，能够满足碳排放核查的业务需求；通过性能测试，我们验证了系统在各种负载条件下都表现出良好的性能，能够满足碳排放核查系统的性能需求；通过安全测试，我们验证了系统在安全性方面表现良好，已采取了多种安全措施防止常见的安全威胁；通过兼容性测试，我们验证了系统能够在各种主流浏览器、操作系统和设备上正常运行；通过用户体验测试，我们验证了系统具有良好的易用性和用户满意度。

测试结果表明，基于区块链的碳排放核查系统能够满足需求分析中提出的各项要求，是一个功能完整、性能良好、安全可靠、兼容性强、用户体验佳的系统。在下一章中，我们将总结研究工作，分析系统的创新点，指出不足与改进方向，展望未来研究方向。
