.role-management {
  padding: 20px;
}

.role-management h2 {
  margin-bottom: 30px;
  color: #333;
}

.role-form {
  background: white;
  padding: 25px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.role-form h3 {
  margin-bottom: 20px;
  color: #444;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #555;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.form-control:focus {
  border-color: #667eea;
  outline: none;
}

.permissions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 10px;
}

.permission-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.permission-item:hover {
  background: #e9ecef;
}

.permission-item input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn-primary {
  background-color: #667eea;
  color: white;
}

.btn-primary:hover {
  background-color: #5a6fd6;
}

.btn-danger {
  background-color: #ef4444;
  color: white;
  padding: 8px 16px;
  font-size: 14px;
}

.btn-danger:hover {
  background-color: #dc2626;
}

.role-list {
  background: white;
  padding: 25px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.role-list h3 {
  margin-bottom: 20px;
  color: #444;
}

.table-responsive {
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #444;
}

.table tr:hover {
  background-color: #f8f9fa;
}

.permissions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.permission-tag {
  padding: 4px 8px;
  background-color: #e9ecef;
  color: #495057;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: 18px;
  color: #666;
}

.alert {
  padding: 12px;
  border-radius: 5px;
  margin-bottom: 20px;
}

.alert-danger {
  background-color: #fee2e2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .role-management {
    padding: 15px;
  }

  .role-form,
  .role-list {
    padding: 15px;
  }

  .permissions-grid {
    grid-template-columns: 1fr;
  }

  .table th,
  .table td {
    padding: 8px;
  }

  .permissions-list {
    flex-direction: column;
  }

  .action-buttons {
    flex-direction: column;
    gap: 5px;
  }

  .btn-danger {
    width: 100%;
  }
} 