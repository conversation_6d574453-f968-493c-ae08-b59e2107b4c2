/**
 * 核查机构核查管理页面脚本
 */

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 加载核查统计信息
    loadVerificationStatistics();
    
    // 加载待核查列表
    loadPendingVerifications();
    
    // 加载已完成核查列表
    loadCompletedVerifications();
    
    // 绑定搜索表单提交事件
    const searchForm = document.querySelector('form[action="/verifier/verifications"]');
    if (searchForm) {
        searchForm.addEventListener('submit', function(event) {
            event.preventDefault();
            loadCompletedVerifications({
                enterprise: document.getElementById('enterprise').value,
                date_from: document.getElementById('date_from').value,
                date_to: document.getElementById('date_to').value,
                status: document.getElementById('status').value
            });
        });
    }
});

/**
 * 加载核查统计信息
 */
function loadVerificationStatistics() {
    API.getVerificationStatistics()
        .then(response => {
            if (response.success) {
                updateStatisticsCards(response.data);
            } else {
                console.error('加载核查统计信息失败:', response.message);
            }
        })
        .catch(error => {
            console.error('加载核查统计信息失败:', error);
        });
}

/**
 * 更新统计卡片
 * @param {object} statistics - 统计信息
 */
function updateStatisticsCards(statistics) {
    // 更新总核查数
    const totalVerificationsCard = document.querySelector('.card.bg-primary .card-text');
    if (totalVerificationsCard) {
        totalVerificationsCard.textContent = statistics.total_verifications;
    }
    
    // 更新待核查数
    const pendingVerificationsCard = document.querySelector('.card.bg-warning .card-text');
    if (pendingVerificationsCard) {
        pendingVerificationsCard.textContent = statistics.pending_verifications;
    }
    
    // 更新已通过数
    const approvedVerificationsCard = document.querySelector('.card.bg-success .card-text');
    if (approvedVerificationsCard) {
        approvedVerificationsCard.textContent = statistics.approved_verifications;
    }
    
    // 更新已拒绝数
    const rejectedVerificationsCard = document.querySelector('.card.bg-danger .card-text');
    if (rejectedVerificationsCard) {
        rejectedVerificationsCard.textContent = statistics.rejected_verifications;
    }
}

/**
 * 加载待核查列表
 */
function loadPendingVerifications() {
    // 显示加载中提示
    const pendingTableBody = document.querySelector('.card:nth-of-type(2) table tbody');
    pendingTableBody.innerHTML = '<tr><td colspan="7" class="text-center">加载中...</td></tr>';
    
    // 调用API获取待核查列表
    API.getPendingVerifications()
        .then(response => {
            if (response.success) {
                renderPendingVerifications(response.data);
            } else {
                pendingTableBody.innerHTML = '<tr><td colspan="7" class="text-center">加载待核查列表失败</td></tr>';
                showAlert('danger', response.message || '加载待核查列表失败');
            }
        })
        .catch(error => {
            console.error('加载待核查列表失败:', error);
            pendingTableBody.innerHTML = '<tr><td colspan="7" class="text-center">加载待核查列表失败</td></tr>';
            showAlert('danger', '加载待核查列表失败: ' + error.message);
        });
}

/**
 * 渲染待核查列表
 * @param {array} verifications - 待核查列表
 */
function renderPendingVerifications(verifications) {
    const pendingTableBody = document.querySelector('.card:nth-of-type(2) table tbody');
    
    if (!verifications || verifications.length === 0) {
        pendingTableBody.innerHTML = '<tr><td colspan="7" class="text-center">暂无待核查数据</td></tr>';
        return;
    }
    
    let html = '';
    verifications.forEach(verification => {
        html += `
            <tr>
                <td>${verification.id}</td>
                <td>${verification.enterprise_name}</td>
                <td>${verification.source}</td>
                <td>${verification.amount} ${verification.unit}</td>
                <td>${verification.submission_date}</td>
                <td>
                    <span class="badge bg-warning">${verification.status}</span>
                </td>
                <td>
                    <div class="btn-group">
                        <a href="/verifier/verification/${verification.id}" class="btn btn-sm btn-primary verify-emission" data-id="${verification.id}">
                            <i class="fas fa-clipboard-check me-1"></i>核查
                        </a>
                    </div>
                </td>
            </tr>
        `;
    });
    
    pendingTableBody.innerHTML = html;
    
    // 绑定核查按钮点击事件
    document.querySelectorAll('.verify-emission').forEach(btn => {
        btn.addEventListener('click', function(event) {
            event.preventDefault();
            const emissionId = this.getAttribute('data-id');
            showVerifyEmissionModal(emissionId);
        });
    });
}

/**
 * 加载已完成核查列表
 * @param {object} params - 查询参数
 */
function loadCompletedVerifications(params = {}) {
    // 显示加载中提示
    const completedTableBody = document.querySelector('.card:nth-of-type(3) table tbody');
    completedTableBody.innerHTML = '<tr><td colspan="7" class="text-center">加载中...</td></tr>';
    
    // 调用API获取已完成核查列表
    API.getVerifications(params)
        .then(response => {
            if (response.success) {
                renderCompletedVerifications(response.data);
            } else {
                completedTableBody.innerHTML = '<tr><td colspan="7" class="text-center">加载已完成核查列表失败</td></tr>';
                showAlert('danger', response.message || '加载已完成核查列表失败');
            }
        })
        .catch(error => {
            console.error('加载已完成核查列表失败:', error);
            completedTableBody.innerHTML = '<tr><td colspan="7" class="text-center">加载已完成核查列表失败</td></tr>';
            showAlert('danger', '加载已完成核查列表失败: ' + error.message);
        });
}

/**
 * 渲染已完成核查列表
 * @param {array} verifications - 已完成核查列表
 */
function renderCompletedVerifications(verifications) {
    const completedTableBody = document.querySelector('.card:nth-of-type(3) table tbody');
    
    if (!verifications || verifications.length === 0) {
        completedTableBody.innerHTML = '<tr><td colspan="7" class="text-center">暂无已完成核查数据</td></tr>';
        return;
    }
    
    let html = '';
    verifications.forEach(verification => {
        html += `
            <tr>
                <td>${verification.id}</td>
                <td>${verification.enterprise_name}</td>
                <td>${verification.emission_data ? verification.emission_data.source : '未知'}</td>
                <td>${verification.emission_data ? verification.emission_data.amount + ' ' + verification.emission_data.unit : '未知'}</td>
                <td>${verification.verification_date}</td>
                <td>
                    ${verification.status === '已通过' ? 
                        '<span class="badge bg-success">已通过</span>' : 
                        '<span class="badge bg-danger">已拒绝</span>'}
                </td>
                <td>
                    <div class="btn-group">
                        <a href="/verifier/verification/${verification.id}" class="btn btn-sm btn-info view-verification" data-id="${verification.id}">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="/verifier/verification/${verification.id}/report" class="btn btn-sm btn-success generate-report" data-id="${verification.id}">
                            <i class="fas fa-file-alt"></i>
                        </a>
                    </div>
                </td>
            </tr>
        `;
    });
    
    completedTableBody.innerHTML = html;
    
    // 绑定查看核查记录按钮点击事件
    document.querySelectorAll('.view-verification').forEach(btn => {
        btn.addEventListener('click', function(event) {
            event.preventDefault();
            const verificationId = this.getAttribute('data-id');
            viewVerificationDetail(verificationId);
        });
    });
    
    // 绑定生成报告按钮点击事件
    document.querySelectorAll('.generate-report').forEach(btn => {
        btn.addEventListener('click', function(event) {
            event.preventDefault();
            const verificationId = this.getAttribute('data-id');
            generateVerificationReport(verificationId);
        });
    });
}

/**
 * 显示核查排放数据模态框
 * @param {number} emissionId - 排放数据ID
 */
function showVerifyEmissionModal(emissionId) {
    // 获取排放数据详情
    API.getEmissionDetail(emissionId)
        .then(response => {
            if (response.success) {
                const emission = response.data;
                
                // 创建模态框
                const modalId = 'verifyEmissionModal';
                const modal = document.createElement('div');
                modal.className = 'modal fade';
                modal.id = modalId;
                modal.tabIndex = '-1';
                modal.setAttribute('aria-labelledby', `${modalId}Label`);
                modal.setAttribute('aria-hidden', 'true');
                
                modal.innerHTML = `
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="${modalId}Label">核查排放数据</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <p><strong>ID:</strong> ${emission.id}</p>
                                        <p><strong>企业:</strong> ${emission.enterprise_name}</p>
                                        <p><strong>排放源:</strong> ${emission.source}</p>
                                        <p><strong>排放量:</strong> ${emission.amount} ${emission.unit}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>提交日期:</strong> ${emission.submission_date}</p>
                                        <p><strong>状态:</strong> <span class="badge bg-warning">${emission.status}</span></p>
                                        <p><strong>区块链哈希:</strong> ${emission.blockchain_hash || '未提交到区块链'}</p>
                                        <p><strong>描述:</strong> ${emission.description || '无'}</p>
                                    </div>
                                </div>
                                
                                <hr>
                                
                                <form id="verifyEmissionForm">
                                    <div class="mb-3">
                                        <label for="conclusion" class="form-label">核查结论</label>
                                        <select class="form-select" id="conclusion" name="conclusion" required>
                                            <option value="">选择结论</option>
                                            <option value="已通过">通过</option>
                                            <option value="已拒绝">拒绝</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="comments" class="form-label">核查意见</label>
                                        <textarea class="form-control" id="comments" name="comments" rows="4" required></textarea>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-primary" id="submitVerificationBtn" data-id="${emission.id}">提交核查结果</button>
                            </div>
                        </div>
                    </div>
                `;
                
                // 添加到文档中
                document.body.appendChild(modal);
                
                // 显示模态框
                const modalInstance = new bootstrap.Modal(modal);
                modalInstance.show();
                
                // 绑定提交核查结果按钮点击事件
                document.getElementById('submitVerificationBtn').addEventListener('click', function() {
                    const form = document.getElementById('verifyEmissionForm');
                    if (form.checkValidity()) {
                        const emissionId = this.getAttribute('data-id');
                        const verificationData = {
                            conclusion: document.getElementById('conclusion').value,
                            comments: document.getElementById('comments').value
                        };
                        
                        verifyEmission(emissionId, verificationData, modalInstance);
                    } else {
                        form.reportValidity();
                    }
                });
                
                // 模态框关闭后移除
                modal.addEventListener('hidden.bs.modal', function() {
                    document.body.removeChild(modal);
                });
            } else {
                showAlert('danger', response.message || '获取排放数据详情失败');
            }
        })
        .catch(error => {
            console.error('获取排放数据详情失败:', error);
            showAlert('danger', '获取排放数据详情失败: ' + error.message);
        });
}

/**
 * 核查排放数据
 * @param {number} emissionId - 排放数据ID
 * @param {object} verificationData - 核查数据
 * @param {object} modalInstance - 模态框实例
 */
function verifyEmission(emissionId, verificationData, modalInstance) {
    API.verifyEmission(emissionId, verificationData)
        .then(response => {
            if (response.success) {
                modalInstance.hide();
                showAlert('success', '核查完成');
                loadVerificationStatistics();
                loadPendingVerifications();
                loadCompletedVerifications();
            } else {
                showAlert('danger', response.message || '核查失败', true);
            }
        })
        .catch(error => {
            console.error('核查失败:', error);
            showAlert('danger', '核查失败: ' + error.message, true);
        });
}

/**
 * 查看核查记录详情
 * @param {number} verificationId - 核查记录ID
 */
function viewVerificationDetail(verificationId) {
    API.getVerificationDetail(verificationId)
        .then(response => {
            if (response.success) {
                showVerificationDetailModal(response.data);
            } else {
                showAlert('danger', response.message || '获取核查记录详情失败');
            }
        })
        .catch(error => {
            console.error('获取核查记录详情失败:', error);
            showAlert('danger', '获取核查记录详情失败: ' + error.message);
        });
}

/**
 * 显示核查记录详情模态框
 * @param {object} verification - 核查记录
 */
function showVerificationDetailModal(verification) {
    // 创建模态框
    const modalId = 'verificationDetailModal';
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = modalId;
    modal.tabIndex = '-1';
    modal.setAttribute('aria-labelledby', `${modalId}Label`);
    modal.setAttribute('aria-hidden', 'true');
    
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="${modalId}Label">核查记录详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p><strong>核查ID:</strong> ${verification.id}</p>
                            <p><strong>企业:</strong> ${verification.enterprise_name}</p>
                            <p><strong>核查机构:</strong> ${verification.verifier_name}</p>
                            <p><strong>核查日期:</strong> ${verification.verification_date}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>状态:</strong> ${verification.status === '已通过' ? 
                                '<span class="badge bg-success">已通过</span>' : 
                                '<span class="badge bg-danger">已拒绝</span>'}</p>
                            <p><strong>区块链哈希:</strong> ${verification.blockchain_hash || '未提交到区块链'}</p>
                            <p><strong>核查意见:</strong> ${verification.comments || '无'}</p>
                        </div>
                    </div>
                    
                    <div class="card mt-3">
                        <div class="card-header bg-light">
                            <h6 class="card-title mb-0">排放数据</h6>
                        </div>
                        <div class="card-body">
                            ${verification.emission_data ? `
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>排放ID:</strong> ${verification.emission_data.id}</p>
                                        <p><strong>排放源:</strong> ${verification.emission_data.source}</p>
                                        <p><strong>排放量:</strong> ${verification.emission_data.amount} ${verification.emission_data.unit}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>提交日期:</strong> ${verification.emission_data.submission_date}</p>
                                        <p><strong>描述:</strong> ${verification.emission_data.description || '无'}</p>
                                        <p><strong>区块链哈希:</strong> ${verification.emission_data.blockchain_hash || '未提交到区块链'}</p>
                                    </div>
                                </div>
                            ` : '<p>排放数据不可用</p>'}
                        </div>
                    </div>
                    
                    ${verification.blockchain_data && Object.keys(verification.blockchain_data).length > 0 ? `
                        <div class="card mt-3">
                            <div class="card-header bg-light">
                                <h6 class="card-title mb-0">区块链数据</h6>
                            </div>
                            <div class="card-body">
                                <pre>${JSON.stringify(verification.blockchain_data, null, 2)}</pre>
                            </div>
                        </div>
                    ` : ''}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <a href="/verifier/verification/${verification.id}/report" class="btn btn-success">
                        <i class="fas fa-file-alt me-1"></i>生成报告
                    </a>
                </div>
            </div>
        </div>
    `;
    
    // 添加到文档中
    document.body.appendChild(modal);
    
    // 显示模态框
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();
    
    // 模态框关闭后移除
    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

/**
 * 生成核查报告
 * @param {number} verificationId - 核查记录ID
 */
function generateVerificationReport(verificationId) {
    // 这里可以直接跳转到报告生成页面
    window.location.href = `/verifier/verification/${verificationId}/report`;
}

/**
 * 显示提示信息
 * @param {string} type - 提示类型
 * @param {string} message - 提示信息
 * @param {boolean} inModal - 是否在模态框中显示
 */
function showAlert(type, message, inModal = false) {
    const alertContainer = inModal ? 
        document.querySelector('.modal-body') : 
        document.querySelector('.container');
    
    if (!alertContainer) return;
    
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    if (inModal) {
        alertContainer.insertBefore(alertDiv, alertContainer.firstChild);
    } else {
        const pageTitle = document.querySelector('.d-flex.justify-content-between');
        alertContainer.insertBefore(alertDiv, pageTitle.nextSibling);
    }
    
    // 5秒后自动关闭
    setTimeout(() => {
        alertDiv.classList.remove('show');
        setTimeout(() => {
            alertContainer.removeChild(alertDiv);
        }, 150);
    }, 5000);
}
