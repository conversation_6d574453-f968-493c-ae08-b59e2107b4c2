import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';
import BlockchainInfo from '../../components/BlockchainInfo';
import BlockchainVerification from '../../components/BlockchainVerification';
import '../../styles/TransactionDetail.css';

/**
 * 交易详情页面
 * 展示交易记录的详细信息，包括区块链信息
 */
const TransactionDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [transaction, setTransaction] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [userRole, setUserRole] = useState('');
  const [userId, setUserId] = useState('');

  useEffect(() => {
    fetchUserInfo();
    fetchTransactionData();
  }, [id]);

  const fetchUserInfo = async () => {
    try {
      const response = await axios.get('/api/auth/profile', {
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      
      setUserRole(response.data.role);
      setUserId(response.data.id);
    } catch (err) {
      console.error('获取用户信息失败:', err);
    }
  };

  const fetchTransactionData = async () => {
    setLoading(true);
    try {
      const response = await axios.get(`/api/transactions/${id}`, {
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      
      setTransaction(response.data.transaction);
      setError('');
    } catch (err) {
      console.error('获取交易记录失败:', err);
      setError('获取交易记录失败');
    } finally {
      setLoading(false);
    }
  };

  // 格式化日期
  const formatDate = (dateString) => {
    if (!dateString) return '';
    
    try {
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch (e) {
      return dateString;
    }
  };

  // 获取状态标签和颜色
  const getStatusLabel = (status) => {
    switch (status) {
      case 'pending':
        return { label: '待确认', color: 'orange' };
      case 'completed':
        return { label: '已完成', color: 'green' };
      case 'cancelled':
        return { label: '已取消', color: 'red' };
      default:
        return { label: status, color: 'gray' };
    }
  };

  // 确认交易
  const confirmTransaction = async () => {
    try {
      await axios.post(`/api/transactions/${id}/confirm`, {}, {
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      
      fetchTransactionData();
    } catch (err) {
      console.error('确认交易失败:', err);
      setError('确认交易失败');
    }
  };

  // 取消交易
  const cancelTransaction = async () => {
    try {
      await axios.post(`/api/transactions/${id}/cancel`, {}, {
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      
      fetchTransactionData();
    } catch (err) {
      console.error('取消交易失败:', err);
      setError('取消交易失败');
    }
  };

  if (loading) {
    return (
      <div className="transaction-detail-loading">
        <i className="fas fa-spinner fa-spin"></i>
        <span>加载中...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="transaction-detail-error">
        <i className="fas fa-exclamation-triangle"></i>
        <span>{error}</span>
        <button onClick={() => navigate('/transactions')} className="btn-back">
          返回列表
        </button>
      </div>
    );
  }

  if (!transaction) {
    return (
      <div className="transaction-detail-not-found">
        <i className="fas fa-search"></i>
        <span>未找到交易记录</span>
        <button onClick={() => navigate('/transactions')} className="btn-back">
          返回列表
        </button>
      </div>
    );
  }

  const statusInfo = getStatusLabel(transaction.status);
  const isBuyer = userId === transaction.buyer_id;
  const isSeller = userId === transaction.seller_id;

  return (
    <div className="transaction-detail">
      <div className="detail-header">
        <h2>交易详情</h2>
        <button onClick={() => navigate('/transactions')} className="btn-back">
          <i className="fas fa-arrow-left"></i> 返回列表
        </button>
      </div>

      {/* 基本信息 */}
      <div className="detail-section">
        <h3 className="section-title">基本信息</h3>
        <div className="detail-grid">
          <div className="detail-item">
            <span className="detail-label">交易ID</span>
            <span className="detail-value">{transaction.id}</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">状态</span>
            <span className="detail-value">
              <span className="status-badge" style={{ backgroundColor: statusInfo.color }}>
                {statusInfo.label}
              </span>
            </span>
          </div>
          <div className="detail-item">
            <span className="detail-label">买方</span>
            <span className="detail-value">
              {transaction.buyer_name}
              {isBuyer && <span className="user-tag">(您)</span>}
            </span>
          </div>
          <div className="detail-item">
            <span className="detail-label">卖方</span>
            <span className="detail-value">
              {transaction.seller_name}
              {isSeller && <span className="user-tag">(您)</span>}
            </span>
          </div>
          <div className="detail-item">
            <span className="detail-label">交易数量</span>
            <span className="detail-value">{transaction.amount} 吨</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">交易价格</span>
            <span className="detail-value">¥{transaction.price} / 吨</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">总金额</span>
            <span className="detail-value">¥{(transaction.amount * transaction.price).toFixed(2)}</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">交易时间</span>
            <span className="detail-value">{formatDate(transaction.transaction_time)}</span>
          </div>
        </div>
        
        {/* 交易操作按钮 */}
        {transaction.status === 'pending' && (
          <div className="transaction-actions">
            {isSeller && (
              <button onClick={confirmTransaction} className="btn-confirm">
                确认交易
              </button>
            )}
            {(isBuyer || isSeller) && (
              <button onClick={cancelTransaction} className="btn-cancel">
                取消交易
              </button>
            )}
          </div>
        )}
      </div>

      {/* 区块链信息 */}
      {transaction.blockchain_hash && (
        <div className="detail-section">
          <h3 className="section-title">区块链信息</h3>
          <BlockchainInfo
            transactionHash={transaction.blockchain_hash}
            blockNumber={transaction.blockchain_block}
            timestamp={transaction.transaction_time}
            status="confirmed"
            type="transaction"
          />
          <BlockchainVerification
            dataId={transaction.id}
            dataType="transaction"
          />
        </div>
      )}
    </div>
  );
};

export default TransactionDetail;
