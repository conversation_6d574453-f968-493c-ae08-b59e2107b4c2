"""
碳排放管理系统后端
"""

import os
from flask import Flask, session, render_template, redirect, url_for
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import J<PERSON><PERSON>anager
from flask_cors import CORS
from datetime import timed<PERSON><PERSON>
from dotenv import load_dotenv
from web3 import Web3

# 加载.env文件
load_dotenv()

# 初始化数据库
db = SQLAlchemy()

def create_app(test_config=None):
    """创建并配置Flask应用"""
    # 创建并配置应用
    template_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'templates'))
    static_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'static'))
    app = Flask(__name__,
                instance_relative_config=True,
                template_folder=template_dir,
                static_folder=static_dir)

    # 默认配置
    app.config.from_mapping(
        SECRET_KEY='carbon-emission-system-secret-key-2025',  # 使用固定的密钥
        SQLALCHEMY_DATABASE_URI=os.environ.get('DATABASE_URL', 'mysql+pymysql://wuhong:D7mH8rZ7a7Z2kJa8@82.157.7.47:3306/ces?charset=utf8mb4'),
        SQLALCHEMY_TRACK_MODIFICATIONS=False,
        SQLALCHEMY_ENGINE_OPTIONS={
            'pool_size': 10,
            'pool_recycle': 3600,
            'pool_pre_ping': True,
            'pool_timeout': 30
        },
        JWT_SECRET_KEY='carbon-emission-system-jwt-secret-key-2025',  # 使用固定的JWT密钥
        JWT_ACCESS_TOKEN_EXPIRES=timedelta(hours=24)
    )

    # 会话配置
    app.config['SESSION_TYPE'] = 'filesystem'  # 使用文件系统存储会话
    app.config['SESSION_PERMANENT'] = True     # 会话是永久的
    app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(days=7)  # 会话有效期为7天
    app.config['SESSION_USE_SIGNER'] = True    # 使用签名保护会话
    app.config['SESSION_KEY_PREFIX'] = 'ces:'  # 会话键前缀

    # 加载测试配置（如果提供）
    if test_config is not None:
        app.config.from_mapping(test_config)

    # 确保实例文件夹存在
    try:
        os.makedirs(app.instance_path)
    except OSError:
        pass

    # 初始化扩展
    db.init_app(app)
    jwt = JWTManager(app)
    CORS(app)

    # 使用默认会话管理
    print("使用Flask默认会话管理")

    # 导入并初始化区块链客户端
    from backend.blockchain.client import BlockchainClient
    blockchain_client = BlockchainClient()
    app.blockchain_client = blockchain_client

    # 打印区块链连接状态
    if blockchain_client.connected:
        print("区块链客户端连接成功")
    else:
        print("警告: 区块链客户端连接失败，将使用模拟模式")

    # 导入并初始化区块链事件监听服务
    from backend.blockchain.event_listener import BlockchainEventListener
    event_listener = BlockchainEventListener(app)
    app.event_listener = event_listener

    # 启动事件监听服务
    if blockchain_client.connected:
        event_listener.start()
        print("区块链事件监听服务已启动")

    # 导入并初始化工具类
    from backend.utils.carbon_calculator import CarbonCalculator
    from backend.utils.prediction import EmissionPredictor
    from backend.utils.report_generator import ReportGenerator
    app.carbon_calculator = CarbonCalculator()
    app.emission_predictor = EmissionPredictor()
    app.report_generator = ReportGenerator()

    # 创建数据库表
    with app.app_context():
        db.create_all()

    # 注册蓝图
    try:
        from backend.routes.auth import auth_bp
        app.register_blueprint(auth_bp, url_prefix='/api/auth')
    except Exception as e:
        print(f"注册auth_bp失败: {str(e)}")

    try:
        from backend.routes.admin import admin_bp
        app.register_blueprint(admin_bp, url_prefix='/api/admin')
    except Exception as e:
        print(f"注册admin_bp失败: {str(e)}")

    try:
        from backend.routes.verification import verification_bp
        app.register_blueprint(verification_bp, url_prefix='/api/verification')
    except Exception as e:
        print(f"注册verification_bp失败: {str(e)}")

    try:
        from backend.routes.dashboard import dashboard_bp
        app.register_blueprint(dashboard_bp, url_prefix='/api/dashboard')
    except Exception as e:
        print(f"注册dashboard_bp失败: {str(e)}")

    try:
        from backend.routes.emission import emission_bp
        app.register_blueprint(emission_bp, url_prefix='/api/emission')
    except Exception as e:
        print(f"注册emission_bp失败: {str(e)}")

    try:
        from backend.routes.transaction import transaction_bp
        app.register_blueprint(transaction_bp, url_prefix='/api/transaction')
    except Exception as e:
        print(f"注册transaction_bp失败: {str(e)}")

    try:
        from backend.routes.calculator import calculator_bp
        app.register_blueprint(calculator_bp, url_prefix='/api/calculator')
    except Exception as e:
        print(f"注册calculator_bp失败: {str(e)}")

    try:
        from backend.routes.prediction import prediction_bp
        app.register_blueprint(prediction_bp, url_prefix='/api/prediction')
    except Exception as e:
        print(f"注册prediction_bp失败: {str(e)}")

    try:
        from backend.routes.report import report_bp
        app.register_blueprint(report_bp, url_prefix='/api/reports')
    except Exception as e:
        print(f"注册report_bp失败: {str(e)}")

    try:
        from backend.routes.penalty import penalty_bp
        app.register_blueprint(penalty_bp, url_prefix='/api/penalties')
    except Exception as e:
        print(f"注册penalty_bp失败: {str(e)}")

    try:
        from backend.routes.blockchain import blockchain_bp
        app.register_blueprint(blockchain_bp, url_prefix='/api/blockchain')
    except Exception as e:
        print(f"注册blockchain_bp失败: {str(e)}")

    try:
        from backend.routes.blockchain_config import blockchain_config_bp
        app.register_blueprint(blockchain_config_bp, url_prefix='')
    except Exception as e:
        print(f"注册blockchain_config_bp失败: {str(e)}")

    # 注册页面路由
    @app.route('/admin/blockchain/config')
    def admin_blockchain_config():
        if 'user_id' not in session:
            return redirect(url_for('login'))
        if session.get('role') != 'admin':
            return redirect(url_for('index'))

        # 从环境变量或配置文件中获取区块链配置
        ethereum_node_url = os.environ.get('ETHEREUM_NODE_URL', 'http://127.0.0.1:8545')
        contract_address = os.environ.get('CONTRACT_ADDRESS', '')
        admin_address = os.environ.get('ADMIN_ADDRESS', '')
        admin_key = os.environ.get('ADMIN_PRIVATE_KEY', '')

        # 区块链状态
        blockchain_status = {
            'connected': False,
            'network': '本地测试网',
            'block_number': '未知',
            'contract_address': contract_address,
            'admin_address': admin_address
        }

        # 尝试连接区块链
        try:
            web3 = Web3(Web3.HTTPProvider(ethereum_node_url))
            if web3.is_connected():
                blockchain_status['connected'] = True
                blockchain_status['block_number'] = web3.eth.block_number

                # 获取账户列表
                accounts = []
                for account in web3.eth.accounts:
                    balance = web3.eth.get_balance(account)
                    accounts.append({
                        'address': account,
                        'balance': web3.from_wei(balance, 'ether')
                    })
                blockchain_status['accounts'] = accounts
        except Exception as e:
            print(f"区块链连接错误: {str(e)}")

        # 区块链配置
        blockchain_config = {
            'ethereum_node_url': ethereum_node_url,
            'contract_address': contract_address,
            'admin_address': admin_address,
            'admin_private_key': admin_key,
            'enterprise_1_address': os.environ.get('ENTERPRISE_1_ADDRESS', ''),
            'enterprise_1_key': os.environ.get('ENTERPRISE_1_KEY', ''),
            'enterprise_2_address': os.environ.get('ENTERPRISE_2_ADDRESS', ''),
            'enterprise_2_key': os.environ.get('ENTERPRISE_2_KEY', ''),
            'enterprise_3_address': os.environ.get('ENTERPRISE_3_ADDRESS', ''),
            'enterprise_3_key': os.environ.get('ENTERPRISE_3_KEY', ''),
            'verifier_1_address': os.environ.get('VERIFIER_1_ADDRESS', ''),
            'verifier_1_key': os.environ.get('VERIFIER_1_KEY', ''),
            'verifier_2_address': os.environ.get('VERIFIER_2_ADDRESS', ''),
            'verifier_2_key': os.environ.get('VERIFIER_2_KEY', '')
        }

        return render_template('admin/blockchain_config.html',
                            blockchain_status=blockchain_status,
                            blockchain_config=blockchain_config,
                            current_user=session)

    @app.route('/enterprise/blockchain/config')
    def enterprise_blockchain_config():
        if 'user_id' not in session:
            return redirect(url_for('login'))
        if session.get('role') != 'enterprise':
            return redirect(url_for('index'))

        # 从环境变量或配置文件中获取区块链配置
        ethereum_node_url = os.environ.get('ETHEREUM_NODE_URL', 'http://127.0.0.1:8545')
        contract_address = os.environ.get('CONTRACT_ADDRESS', '')
        enterprise_address = os.environ.get('ENTERPRISE_1_ADDRESS', '')
        enterprise_key = os.environ.get('ENTERPRISE_1_KEY', '')

        # 区块链状态
        blockchain_status = {
            'connected': False,
            'network': '本地测试网',
            'block_number': '未知',
            'contract_address': contract_address,
            'enterprise_address': enterprise_address
        }

        # 尝试连接区块链
        try:
            web3 = Web3(Web3.HTTPProvider(ethereum_node_url))
            if web3.is_connected():
                blockchain_status['connected'] = True
                blockchain_status['block_number'] = web3.eth.block_number
        except Exception as e:
            print(f"区块链连接错误: {str(e)}")

        # 区块链配置
        blockchain_config = {
            'ethereum_node_url': ethereum_node_url,
            'contract_address': contract_address,
            'private_key': enterprise_key,
            'simulation_mode': True
        }

        # 模拟交易记录
        blockchain_transactions = [
            {
                'hash': '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
                'type': '提交排放数据',
                'block_number': 12345,
                'timestamp': '2025-04-01 10:30:45',
                'status': 'success'
            },
            {
                'hash': '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890',
                'type': '购买碳配额',
                'block_number': 12350,
                'timestamp': '2025-04-02 14:20:15',
                'status': 'success'
            }
        ]

        return render_template('enterprise/blockchain_config.html',
                            blockchain_status=blockchain_status,
                            blockchain_config=blockchain_config,
                            blockchain_transactions=blockchain_transactions,
                            current_user=session)

    @app.route('/verifier/blockchain/config')
    def verifier_blockchain_config():
        if 'user_id' not in session:
            return redirect(url_for('login'))
        if session.get('role') != 'verifier':
            return redirect(url_for('index'))

        # 从环境变量或配置文件中获取区块链配置
        ethereum_node_url = os.environ.get('ETHEREUM_NODE_URL', 'http://127.0.0.1:8545')
        contract_address = os.environ.get('CONTRACT_ADDRESS', '')
        verifier_address = os.environ.get('VERIFIER_1_ADDRESS', '')
        verifier_key = os.environ.get('VERIFIER_1_KEY', '')

        # 区块链状态
        blockchain_status = {
            'connected': False,
            'network': '本地测试网',
            'block_number': '未知',
            'contract_address': contract_address,
            'verifier_address': verifier_address
        }

        # 尝试连接区块链
        try:
            web3 = Web3(Web3.HTTPProvider(ethereum_node_url))
            if web3.is_connected():
                blockchain_status['connected'] = True
                blockchain_status['block_number'] = web3.eth.block_number
        except Exception as e:
            print(f"区块链连接错误: {str(e)}")

        # 区块链配置
        blockchain_config = {
            'ethereum_node_url': ethereum_node_url,
            'contract_address': contract_address,
            'private_key': verifier_key,
            'simulation_mode': True
        }

        # 模拟交易记录
        blockchain_transactions = [
            {
                'hash': '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
                'type': '核查排放数据',
                'block_number': 12345,
                'timestamp': '2025-04-01 10:30:45',
                'status': 'success'
            },
            {
                'hash': '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890',
                'type': '提交核查报告',
                'block_number': 12350,
                'timestamp': '2025-04-02 14:20:15',
                'status': 'success'
            }
        ]

        return render_template('verifier/blockchain_config.html',
                            blockchain_status=blockchain_status,
                            blockchain_config=blockchain_config,
                            blockchain_transactions=blockchain_transactions,
                            current_user=session)

    try:
        from backend.routes.frontend import frontend_bp
        app.register_blueprint(frontend_bp, url_prefix='')
        print("注册frontend_bp成功")
    except Exception as e:
        print(f"注册frontend_bp失败: {str(e)}")

    try:
        from backend.routes.frontend_emissions import emissions_frontend_bp
        app.register_blueprint(emissions_frontend_bp, url_prefix='')
        print("注册emissions_frontend_bp成功")
    except Exception as e:
        print(f"注册emissions_frontend_bp失败: {str(e)}")

    try:
        from backend.routes.blockchain import blockchain_bp
        app.register_blueprint(blockchain_bp, url_prefix='/api/blockchain')
        print("注册blockchain_bp成功")
    except Exception as e:
        print(f"注册blockchain_bp失败: {str(e)}")

    return app
