"""
认证相关路由
"""

from datetime import datetime
from flask import Blueprint, request, jsonify
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity

from backend import db
from backend.models.user import User
from backend.models.activity import Activity

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/register', methods=['POST'])
def register():
    data = request.get_json()
    
    # 验证必填字段
    required_fields = ['username', 'email', 'password', 'role']
    for field in required_fields:
        if field not in data:
            return jsonify({'error': f'缺少必填字段: {field}'}), 400
    
    # 验证角色
    valid_roles = ['enterprise', 'verifier']  # 不允许通过注册创建管理员
    if data['role'] not in valid_roles:
        return jsonify({'error': f'无效的角色: {data["role"]}'}), 400
    
    # 检查用户名和邮箱是否已存在
    if User.query.filter_by(username=data['username']).first():
        return jsonify({'error': '用户名已存在'}), 400
    
    if User.query.filter_by(email=data['email']).first():
        return jsonify({'error': '邮箱已存在'}), 400
    
    # 创建用户
    user = User(
        username=data['username'],
        email=data['email'],
        role=data['role'],
        company_name=data.get('company_name'),
        credit_code=data.get('credit_code'),
        created_at=datetime.now()
    )
    user.set_password(data['password'])
    
    db.session.add(user)
    db.session.commit()
    
    # 记录活动
    activity = Activity(
        user_id=user.id,
        activity_type='register',
        description=f'用户 {user.username} 注册成功'
    )
    db.session.add(activity)
    db.session.commit()
    
    # 创建访问令牌
    access_token = create_access_token(identity=user.id)
    
    return jsonify({
        'message': '注册成功',
        'token': access_token,
        'user': user.to_dict()
    }), 201

@auth_bp.route('/login', methods=['POST'])
def login():
    data = request.get_json()
    
    # 验证必填字段
    if 'username' not in data or 'password' not in data:
        return jsonify({'error': '缺少用户名或密码'}), 400
    
    # 查找用户
    user = User.query.filter_by(username=data['username']).first()
    
    # 验证用户和密码
    if not user or not user.check_password(data['password']):
        return jsonify({'error': '用户名或密码错误'}), 401
    
    # 更新最后登录时间
    user.last_login = datetime.now()
    
    # 记录活动
    activity = Activity(
        user_id=user.id,
        activity_type='login',
        description=f'用户 {user.username} 登录成功'
    )
    db.session.add(activity)
    db.session.commit()
    
    # 创建访问令牌
    access_token = create_access_token(identity=user.id)
    
    return jsonify({
        'message': '登录成功',
        'token': access_token,
        'user': user.to_dict()
    }), 200

@auth_bp.route('/profile', methods=['GET'])
@jwt_required()
def get_profile():
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    if not user:
        return jsonify({'error': '用户不存在'}), 404
    
    return jsonify({
        'user': user.to_dict()
    }), 200

@auth_bp.route('/profile', methods=['PUT'])
@jwt_required()
def update_profile():
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    if not user:
        return jsonify({'error': '用户不存在'}), 404
    
    data = request.get_json()
    
    # 更新用户信息
    if 'email' in data:
        # 检查邮箱是否已被其他用户使用
        existing_user = User.query.filter_by(email=data['email']).first()
        if existing_user and existing_user.id != user.id:
            return jsonify({'error': '邮箱已被使用'}), 400
        user.email = data['email']
    
    if 'company_name' in data:
        user.company_name = data['company_name']
    
    if 'credit_code' in data:
        user.credit_code = data['credit_code']
    
    if 'password' in data:
        user.set_password(data['password'])
    
    db.session.commit()
    
    # 记录活动
    activity = Activity(
        user_id=user.id,
        activity_type='update_profile',
        description=f'用户 {user.username} 更新了个人资料'
    )
    db.session.add(activity)
    db.session.commit()
    
    return jsonify({
        'message': '个人资料更新成功',
        'user': user.to_dict()
    }), 200
