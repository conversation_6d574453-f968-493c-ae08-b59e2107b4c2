import React, { useState, useEffect } from 'react';
import axios from 'axios';
import '../styles/Dashboard.css';

function Dashboard() {
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    totalData: 0,
    totalReports: 0,
    recentActivities: [],
    systemStatus: {
      cpu: 0,
      memory: 0,
      disk: 0,
      network: 0
    }
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchDashboardData();
    const interval = setInterval(fetchDashboardData, 30000); // 每30秒更新一次
    return () => clearInterval(interval);
  }, []);

  const fetchDashboardData = async () => {
    try {
      const response = await axios.get('/api/dashboard', {
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      setStats(response.data);
      setLoading(false);
    } catch (err) {
      setError('获取仪表盘数据失败');
      setLoading(false);
    }
  };

  const formatNumber = (num) => {
    return new Intl.NumberFormat('zh-CN').format(num);
  };

  const formatBytes = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) return <div className="loading">加载中...</div>;

  return (
    <div className="dashboard">
      <h2>仪表盘</h2>
      {error && <div className="alert alert-danger">{error}</div>}

      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon users">
            <i className="fas fa-users"></i>
          </div>
          <div className="stat-content">
            <h3>用户统计</h3>
            <div className="stat-numbers">
              <div className="stat-item">
                <span className="stat-label">总用户数</span>
                <span className="stat-value">{formatNumber(stats.totalUsers)}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">活跃用户</span>
                <span className="stat-value">{formatNumber(stats.activeUsers)}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon data">
            <i className="fas fa-database"></i>
          </div>
          <div className="stat-content">
            <h3>数据统计</h3>
            <div className="stat-numbers">
              <div className="stat-item">
                <span className="stat-label">总数据量</span>
                <span className="stat-value">{formatBytes(stats.totalData)}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">报告数量</span>
                <span className="stat-value">{formatNumber(stats.totalReports)}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon system">
            <i className="fas fa-server"></i>
          </div>
          <div className="stat-content">
            <h3>系统状态</h3>
            <div className="stat-numbers">
              <div className="stat-item">
                <span className="stat-label">CPU使用率</span>
                <div className="progress-bar">
                  <div
                    className="progress"
                    style={{ width: `${stats.systemStatus.cpu}%` }}
                  ></div>
                  <span className="progress-text">{stats.systemStatus.cpu}%</span>
                </div>
              </div>
              <div className="stat-item">
                <span className="stat-label">内存使用率</span>
                <div className="progress-bar">
                  <div
                    className="progress"
                    style={{ width: `${stats.systemStatus.memory}%` }}
                  ></div>
                  <span className="progress-text">{stats.systemStatus.memory}%</span>
                </div>
              </div>
              <div className="stat-item">
                <span className="stat-label">磁盘使用率</span>
                <div className="progress-bar">
                  <div
                    className="progress"
                    style={{ width: `${stats.systemStatus.disk}%` }}
                  ></div>
                  <span className="progress-text">{stats.systemStatus.disk}%</span>
                </div>
              </div>
              <div className="stat-item">
                <span className="stat-label">网络使用率</span>
                <div className="progress-bar">
                  <div
                    className="progress"
                    style={{ width: `${stats.systemStatus.network}%` }}
                  ></div>
                  <span className="progress-text">{stats.systemStatus.network}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="recent-activities">
        <h3>最近活动</h3>
        <div className="activity-list">
          {stats.recentActivities.map((activity, index) => (
            <div key={index} className="activity-item">
              <div className="activity-icon">
                <i className={`fas ${getActivityIcon(activity.type)}`}></i>
              </div>
              <div className="activity-content">
                <div className="activity-title">{activity.title}</div>
                <div className="activity-details">
                  <span className="activity-user">{activity.user}</span>
                  <span className="activity-time">{formatDate(activity.timestamp)}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

function getActivityIcon(type) {
  switch (type) {
    case 'login':
      return 'fa-sign-in-alt';
    case 'logout':
      return 'fa-sign-out-alt';
    case 'create':
      return 'fa-plus';
    case 'update':
      return 'fa-edit';
    case 'delete':
      return 'fa-trash';
    case 'download':
      return 'fa-download';
    case 'upload':
      return 'fa-upload';
    default:
      return 'fa-info-circle';
  }
}

export default Dashboard; 