<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放核查系统 - 排放数据详情</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <style>
        .gradient-custom {
            background: linear-gradient(135deg, #43a047 0%, #1de9b6 100%);
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            margin-bottom: 20px;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }
        .card-header {
            border-radius: 15px 15px 0 0 !important;
            background: linear-gradient(135deg, #43a047 0%, #1de9b6 100%) !important;
            color: white;
            font-weight: 600;
            padding: 1rem 1.5rem;
            border: none;
        }
        .btn-success {
            background: linear-gradient(135deg, #43a047 0%, #1de9b6 100%);
            border: none;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #388e3c 0%, #00bfa5 100%);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: 10px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #43a047;
        }
        .timeline-item {
            position: relative;
            margin-bottom: 25px;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -30px;
            top: 0;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #43a047;
            border: 3px solid white;
            box-shadow: 0 0 0 3px rgba(67, 160, 71, 0.2);
        }
        .timeline-date {
            font-size: 0.85rem;
            color: #666;
            margin-bottom: 5px;
        }
        .timeline-content {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }
        .badge {
            padding: 0.5em 0.8em;
            border-radius: 10px;
            font-weight: 500;
        }
        .detail-label {
            font-weight: 600;
            color: #555;
        }
        .detail-value {
            color: #333;
        }
        .blockchain-hash {
            font-family: monospace;
            word-break: break-all;
            background-color: #f5f5f5;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container">
            <a class="navbar-brand" href="/"><i class="fas fa-leaf me-2"></i>碳排放核查系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise"><i class="fas fa-tachometer-alt me-1"></i>仪表板</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/enterprise/emissions"><i class="fas fa-cloud me-1"></i>排放管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise/transactions"><i class="fas fa-exchange-alt me-1"></i>交易管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise/reports"><i class="fas fa-file-alt me-1"></i>报告管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/enterprise/blockchain/config"><i class="fas fa-link me-1"></i>区块链配置</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>{{ current_user.get('username', '企业用户') }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="/enterprise/profile"><i class="fas fa-id-card me-1"></i>企业资料</a></li>
                            <li><a class="dropdown-item" href="/enterprise/settings"><i class="fas fa-cog me-1"></i>账户设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-1"></i>退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mt-4">
        <!-- 消息提示 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-file-alt me-2"></i>排放数据详情</h1>
            <div>
                <a href="/enterprise/emissions" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-arrow-left me-1"></i>返回列表
                </a>
                {% if emission.status == '待提交' or emission.status == 'draft' or emission.status == '已拒绝' or emission.status == 'rejected' %}
                    <a href="/enterprise/emissions/{{ emission.id }}/edit" class="btn btn-primary me-2">
                        <i class="fas fa-edit me-1"></i>编辑
                    </a>
                    <a href="/enterprise/emissions/{{ emission.id }}/submit" class="btn btn-success">
                        <i class="fas fa-paper-plane me-1"></i>提交
                    </a>
                {% endif %}
            </div>
        </div>

        <div class="row">
            <!-- 排放数据详情 -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header gradient-custom">
                        <h5 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i>基本信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <p class="detail-label">排放ID</p>
                                <p class="detail-value">{{ emission.id }}</p>
                            </div>
                            <div class="col-md-6">
                                <p class="detail-label">状态</p>
                                <p class="detail-value">
                                    {% if emission.status == '待提交' or emission.status == 'draft' %}
                                        <span class="badge" style="background: linear-gradient(135deg, #607d8b 0%, #90a4ae 100%);">待提交</span>
                                    {% elif emission.status == '已提交' or emission.status == 'submitted' %}
                                        <span class="badge" style="background: linear-gradient(135deg, #2196f3 0%, #64b5f6 100%);">已提交</span>
                                    {% elif emission.status == '待核查' or emission.status == 'pending' %}
                                        <span class="badge" style="background: linear-gradient(135deg, #ff9800 0%, #ffb74d 100%);">待核查</span>
                                    {% elif emission.status == '已核查' or emission.status == 'verified' %}
                                        <span class="badge" style="background: linear-gradient(135deg, #43a047 0%, #76d275 100%);">已核查</span>
                                    {% elif emission.status == '已拒绝' or emission.status == 'rejected' %}
                                        <span class="badge" style="background: linear-gradient(135deg, #f44336 0%, #ef5350 100%);">已拒绝</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ emission.status }}</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <p class="detail-label">排放源</p>
                                <p class="detail-value">{{ emission.emission_source }}</p>
                            </div>
                            <div class="col-md-6">
                                <p class="detail-label">计算方法</p>
                                <p class="detail-value">{{ emission.calculation_method }}</p>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <p class="detail-label">排放量</p>
                                <p class="detail-value">{{ emission.emission_amount }} {{ emission.emission_unit }}</p>
                            </div>
                            <div class="col-md-6">
                                <p class="detail-label">排放周期</p>
                                <p class="detail-value">{{ emission.emission_period_start }} 至 {{ emission.emission_period_end }}</p>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <p class="detail-label">提交时间</p>
                                <p class="detail-value">{{ emission.submission_time }}</p>
                            </div>
                            <div class="col-md-6">
                                <p class="detail-label">证明文件</p>
                                <p class="detail-value">
                                    {% if emission.proof_file_path %}
                                        <a href="{{ url_for('static', filename='uploads/' + emission.proof_file_path) }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-download me-1"></i>下载文件
                                        </a>
                                    {% else %}
                                        <span class="text-muted">无</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                        {% if emission.description %}
                            <div class="row mb-3">
                                <div class="col-12">
                                    <p class="detail-label">描述</p>
                                    <p class="detail-value">{{ emission.description }}</p>
                                </div>
                            </div>
                        {% endif %}
                        {% if emission.blockchain_hash %}
                            <div class="row">
                                <div class="col-12">
                                    <p class="detail-label">区块链哈希</p>
                                    <p class="blockchain-hash">{{ emission.blockchain_hash }}</p>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- 核查信息 -->
                {% if verification %}
                    <div class="card mt-4">
                        <div class="card-header gradient-custom">
                            <h5 class="card-title mb-0"><i class="fas fa-check-double me-2"></i>核查信息</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <p class="detail-label">核查机构</p>
                                    <p class="detail-value">{{ verification.verifier_name }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p class="detail-label">核查结论</p>
                                    <p class="detail-value">
                                        {% if verification.conclusion == 'approved' %}
                                            <span class="badge" style="background: linear-gradient(135deg, #43a047 0%, #76d275 100%);">通过</span>
                                        {% else %}
                                            <span class="badge" style="background: linear-gradient(135deg, #f44336 0%, #ef5350 100%);">不通过</span>
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <p class="detail-label">核查时间</p>
                                    <p class="detail-value">{{ verification.verification_time }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p class="detail-label">区块链哈希</p>
                                    <p class="detail-value">
                                        {% if verification.blockchain_hash %}
                                            <span class="blockchain-hash">{{ verification.blockchain_hash }}</span>
                                        {% else %}
                                            <span class="text-muted">无</span>
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                            {% if verification.comments %}
                                <div class="row">
                                    <div class="col-12">
                                        <p class="detail-label">核查意见</p>
                                        <p class="detail-value">{{ verification.comments }}</p>
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                {% endif %}
            </div>

            <!-- 状态时间线 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header gradient-custom">
                        <h5 class="card-title mb-0"><i class="fas fa-history me-2"></i>状态时间线</h5>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            {% if timeline %}
                                {% for item in timeline %}
                                    <div class="timeline-item">
                                        <div class="timeline-date">{{ item.time }}</div>
                                        <div class="timeline-content">
                                            <h6>{{ item.title }}</h6>
                                            <p class="mb-0">{{ item.description }}</p>
                                        </div>
                                    </div>
                                {% endfor %}
                            {% else %}
                                <div class="timeline-item">
                                    <div class="timeline-date">{{ emission.submission_time }}</div>
                                    <div class="timeline-content">
                                        <h6>创建排放数据</h6>
                                        <p class="mb-0">排放数据已创建，状态为"{{ emission.status }}"</p>
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">© 2025 碳排放核查系统 | 基于区块链技术</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
