"""
碳配额模型
"""

from datetime import datetime
from backend import db
from backend.models.user import User

class CarbonQuota(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    enterprise_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>('user.id'), nullable=False)
    year = db.Column(db.Integer, nullable=False)
    initial_amount = db.Column(db.Float, nullable=False)
    current_amount = db.Column(db.Float, nullable=False)
    last_updated = db.Column(db.DateTime, default=datetime.utcnow)
    blockchain_hash = db.Column(db.String(66))

    enterprise = db.relationship('User', backref=db.backref('carbon_quotas', lazy=True))

    def to_dict(self):
        return {
            'id': self.id,
            'enterprise_id': self.enterprise_id,
            'enterprise_name': self.enterprise.company_name if self.enterprise else None,
            'year': self.year,
            'initial_amount': self.initial_amount,
            'current_amount': self.current_amount,
            'last_updated': self.last_updated.isoformat() if self.last_updated else None,
            'blockchain_hash': self.blockchain_hash
        }
