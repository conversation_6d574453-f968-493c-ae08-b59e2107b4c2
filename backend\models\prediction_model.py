"""
预测模型记录
"""

import json
from datetime import datetime
from backend import db

class PredictionModel(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    model_path = db.Column(db.String(255), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_trained = db.Column(db.DateTime)
    metrics = db.Column(db.Text)  # 存储为JSON字符串

    def set_metrics(self, data):
        self.metrics = json.dumps(data)

    def get_metrics(self):
        return json.loads(self.metrics) if self.metrics else {}

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'model_path': self.model_path,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_trained': self.last_trained.isoformat() if self.last_trained else None,
            'metrics': self.get_metrics()
        }
