<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放管理系统 - 交易详情</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background-color: #2c3e50;
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-name {
            margin-right: 15px;
        }
        .logout-btn {
            background-color: #e74c3c;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
        }
        nav {
            background-color: #34495e;
            padding: 10px 0;
        }
        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
        }
        .nav-item {
            color: white;
            text-decoration: none;
            padding: 10px 15px;
            margin-right: 5px;
            border-radius: 4px;
        }
        .nav-item:hover, .nav-item.active {
            background-color: #2c3e50;
        }
        .card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .card-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .btn {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
        }
        .btn-primary {
            background-color: #3498db;
            color: white;
        }
        .btn-success {
            background-color: #2ecc71;
            color: white;
        }
        .btn-danger {
            background-color: #e74c3c;
            color: white;
        }
        .detail-section {
            margin-bottom: 30px;
        }
        .detail-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        .detail-row {
            display: flex;
            margin-bottom: 10px;
        }
        .detail-label {
            width: 150px;
            font-weight: bold;
            color: #7f8c8d;
        }
        .detail-value {
            flex: 1;
        }
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }
        .status-pending {
            background-color: #f39c12;
        }
        .status-verified {
            background-color: #2ecc71;
        }
        .status-rejected {
            background-color: #e74c3c;
        }
        .blockchain-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .blockchain-hash {
            font-family: monospace;
            background-color: #eee;
            padding: 2px 5px;
            border-radius: 3px;
        }
        .transaction-type {
            font-weight: bold;
        }
        .transaction-type-buy {
            color: #2ecc71;
        }
        .transaction-type-sell {
            color: #e74c3c;
        }
        .actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo">碳排放管理系统</div>
            <div class="user-info">
                <span class="user-name">北京碳排放科技有限公司</span>
                <a href="/login" class="logout-btn">退出登录</a>
            </div>
        </div>
    </header>

    <nav>
        <div class="nav-content">
            <a href="/dashboard" class="nav-item">仪表板</a>
            <a href="/emissions" class="nav-item">排放数据</a>
            <a href="/verifications" class="nav-item">核查记录</a>
            <a href="/transactions" class="nav-item active">碳交易</a>
            <a href="/calculator" class="nav-item">碳计算器</a>
            <a href="/predictions" class="nav-item">预测分析</a>
            <a href="/reports" class="nav-item">报告生成</a>
        </div>
    </nav>

    <div class="container">
        <div class="card-title">
            <h1>交易详情</h1>
            <div>
                <a href="/transactions" class="btn btn-primary">返回列表</a>
            </div>
        </div>

        <div class="card">
            <div class="detail-section">
                <div class="detail-title">基本信息</div>

                <div class="detail-row">
                    <div class="detail-label">交易ID</div>
                    <div class="detail-value">2001</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">交易类型</div>
                    <div class="detail-value">
                        <span class="transaction-type transaction-type-sell">出售</span>
                    </div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">交易方</div>
                    <div class="detail-value">上海绿色能源有限公司</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">交易数量</div>
                    <div class="detail-value">200 吨</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">交易价格</div>
                    <div class="detail-value">45 元/吨</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">交易总额</div>
                    <div class="detail-value">9,000 元</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">交易时间</div>
                    <div class="detail-value">2023-04-15 14:30:25</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">交易状态</div>
                    <div class="detail-value">
                        <span class="status status-verified">已完成</span>
                    </div>
                </div>
            </div>

            <div class="detail-section">
                <div class="detail-title">配额变化</div>

                <div class="detail-row">
                    <div class="detail-label">交易前配额</div>
                    <div class="detail-value">3,400 吨</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">交易后配额</div>
                    <div class="detail-value">3,200 吨</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">配额变化</div>
                    <div class="detail-value">-200 吨</div>
                </div>
            </div>

            <div class="detail-section">
                <div class="detail-title">区块链记录</div>

                <div class="blockchain-info">
                    <div class="detail-row">
                        <div class="detail-label">交易哈希</div>
                        <div class="detail-value">
                            <span class="blockchain-hash">0xd5e8f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6a7b8c9d0e1f2g3h4</span>
                        </div>
                    </div>

                    <div class="detail-row">
                        <div class="detail-label">区块号</div>
                        <div class="detail-value">15836421</div>
                    </div>

                    <div class="detail-row">
                        <div class="detail-label">时间戳</div>
                        <div class="detail-value">2023-04-15 14:35:12</div>
                    </div>

                    <div class="detail-row">
                        <div class="detail-label">数据哈希</div>
                        <div class="detail-value">
                            <span class="blockchain-hash">0x1a2b3c4d5e6f7g8h9i0j1k2l3m4n5o6p7q8r9s0t1u2v3w4x5y6z7a8b9c0d1e2</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="actions">
                <a href="/transactions" class="btn btn-primary">返回列表</a>
                <a href="/reports/transaction_report.html" target="_blank" class="btn btn-success">生成报告</a>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 导航切换 - 不需要额外的事件监听器，因为我们使用的是链接
            // 根据当前URL设置活动导航项
            const currentPath = window.location.pathname;
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                if (item.getAttribute('href') === currentPath) {
                    navItems.forEach(i => i.classList.remove('active'));
                    item.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>
