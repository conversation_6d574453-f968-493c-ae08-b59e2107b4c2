"""
演示数据生成脚本
用于生成系统演示所需的数据
"""

import os
import random
import hashlib
from datetime import datetime, timedelta
from dotenv import load_dotenv
from db_utils import get_db_connection
from werkzeug.security import generate_password_hash

# 加载环境变量
load_dotenv()

def generate_users():
    """生成用户数据"""
    print("生成用户数据...")

    # 生成密码哈希
    password = 'password123'
    password_hash = generate_password_hash(password)

    # 企业用户数据
    enterprises = [
        {
            'username': 'enterprise1',
            'email': '<EMAIL>',
            'password_hash': password_hash,
            'role': 'enterprise',
            'company_name': '北京碳排放科技有限公司',
            'credit_code': '91110000123456789A',
            'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        },
        {
            'username': 'enterprise2',
            'email': '<EMAIL>',
            'password_hash': password_hash,
            'role': 'enterprise',
            'company_name': '上海绿色能源有限公司',
            'credit_code': '91310000123456789B',
            'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        },
        {
            'username': 'enterprise3',
            'email': '<EMAIL>',
            'password_hash': password_hash,
            'role': 'enterprise',
            'company_name': '广州环保科技有限公司',
            'credit_code': '91440000123456789C',
            'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    ]

    # 核查机构用户数据
    verifiers = [
        {
            'username': 'verifier1',
            'email': '<EMAIL>',
            'password_hash': password_hash,
            'role': 'verifier',
            'company_name': '国家碳排放核查中心',
            'credit_code': '91110000123456789D',
            'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        },
        {
            'username': 'verifier2',
            'email': '<EMAIL>',
            'password_hash': password_hash,
            'role': 'verifier',
            'company_name': '碳核查认证机构',
            'credit_code': '91110000123456789E',
            'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    ]

    conn = get_db_connection()
    cursor = conn.cursor()

    # 插入企业用户
    for user in enterprises:
        try:
            cursor.execute("""
                INSERT INTO `user` (`username`, `email`, `password_hash`, `role`, `company_name`, `credit_code`, `created_at`)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE `username` = `username`
            """, (
                user['username'],
                user['email'],
                user['password_hash'],
                user['role'],
                user['company_name'],
                user['credit_code'],
                user['created_at']
            ))
            print(f"已创建企业用户: {user['username']} ({user['company_name']})")
        except Exception as e:
            print(f"创建企业用户 {user['username']} 失败: {str(e)}")

    # 插入核查机构用户
    for user in verifiers:
        try:
            cursor.execute("""
                INSERT INTO `user` (`username`, `email`, `password_hash`, `role`, `company_name`, `credit_code`, `created_at`)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE `username` = `username`
            """, (
                user['username'],
                user['email'],
                user['password_hash'],
                user['role'],
                user['company_name'],
                user['credit_code'],
                user['created_at']
            ))
            print(f"已创建核查机构用户: {user['username']} ({user['company_name']})")
        except Exception as e:
            print(f"创建核查机构用户 {user['username']} 失败: {str(e)}")

    conn.commit()

    # 获取用户ID
    enterprise_ids = []
    cursor.execute("SELECT id FROM `user` WHERE `role` = 'enterprise'")
    for row in cursor.fetchall():
        enterprise_ids.append(row[0])

    verifier_ids = []
    cursor.execute("SELECT id FROM `user` WHERE `role` = 'verifier'")
    for row in cursor.fetchall():
        verifier_ids.append(row[0])

    conn.close()

    return enterprise_ids, verifier_ids

def generate_carbon_quotas(enterprise_ids):
    """生成碳配额数据"""
    print("生成碳配额数据...")

    conn = get_db_connection()
    cursor = conn.cursor()

    # 当前年份
    current_year = datetime.now().year

    # 为每个企业创建配额
    for enterprise_id in enterprise_ids:
        initial_amount = random.randint(10000, 50000)  # 随机配额
        try:
            cursor.execute("""
                INSERT INTO `carbon_quota` (`enterprise_id`, `year`, `initial_amount`, `current_amount`, `last_updated`)
                VALUES (%s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE `initial_amount` = VALUES(`initial_amount`), `current_amount` = VALUES(`current_amount`)
            """, (
                enterprise_id,
                current_year,
                initial_amount,
                initial_amount,
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ))
            print(f"已为企业 ID:{enterprise_id} 创建 {current_year} 年配额: {initial_amount} 吨")
        except Exception as e:
            print(f"为企业 ID:{enterprise_id} 创建配额失败: {str(e)}")

    conn.commit()
    conn.close()

def generate_emission_data(enterprise_ids):
    """生成排放数据"""
    print("生成排放数据...")

    conn = get_db_connection()
    cursor = conn.cursor()

    # 排放源类型
    emission_sources = [
        '燃煤锅炉',
        '天然气锅炉',
        '工业生产过程',
        '交通运输',
        '电力消耗'
    ]

    # 计算方法
    calculation_methods = [
        '排放因子法',
        '物料平衡法',
        '连续监测法'
    ]

    # 当前日期
    now = datetime.now()

    # 为每个企业创建排放数据
    emission_ids = []
    for enterprise_id in enterprise_ids:
        for i in range(random.randint(3, 5)):
            emission_source = random.choice(emission_sources)
            emission_amount = random.uniform(100, 1000)
            calculation_method = random.choice(calculation_methods)
            days_ago = random.randint(30, 180)
            period_start = now - timedelta(days=days_ago)
            period_end = period_start + timedelta(days=random.randint(10, 30))

            try:
                cursor.execute("""
                    INSERT INTO `emission_data` (
                        `enterprise_id`, `emission_source`, `emission_amount`, `emission_unit`,
                        `calculation_method`, `emission_period_start`, `emission_period_end`,
                        `status`, `submission_time`, `proof_file_path`
                    )
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    enterprise_id,
                    emission_source,
                    emission_amount,
                    '吨CO2e',
                    calculation_method,
                    period_start.strftime('%Y-%m-%d'),
                    period_end.strftime('%Y-%m-%d'),
                    'pending',
                    (now - timedelta(days=random.randint(1, 10))).strftime('%Y-%m-%d %H:%M:%S'),
                    f'proofs/emission_{enterprise_id}_{i}.pdf'
                ))
                emission_id = cursor.lastrowid
                emission_ids.append(emission_id)
                print(f"已为企业 ID:{enterprise_id} 创建排放数据: {emission_source}, {emission_amount} 吨CO2e")
            except Exception as e:
                print(f"为企业 ID:{enterprise_id} 创建排放数据失败: {str(e)}")

    conn.commit()
    conn.close()

    return emission_ids

def generate_verification_records(emission_ids, verifier_ids):
    """生成核查记录"""
    print("生成核查记录...")

    if not emission_ids or not verifier_ids:
        print("没有排放数据或核查机构，跳过生成核查记录")
        return

    conn = get_db_connection()
    cursor = conn.cursor()

    # 核查一半的排放数据
    for emission_id in emission_ids[:len(emission_ids)//2]:
        verifier_id = random.choice(verifier_ids)
        conclusion = random.choice(['approved', 'rejected'])

        try:
            cursor.execute("""
                INSERT INTO `verification` (
                    `emission_data_id`, `verifier_id`, `conclusion`, `comments`, `verification_time`
                )
                VALUES (%s, %s, %s, %s, %s)
            """, (
                emission_id,
                verifier_id,
                conclusion,
                f'这是对排放数据 {emission_id} 的核查意见。' +
                ('数据准确无误。' if conclusion == 'approved' else '数据存在问题，需要修正。'),
                (datetime.now() - timedelta(days=random.randint(1, 5))).strftime('%Y-%m-%d %H:%M:%S')
            ))

            # 更新排放数据状态
            cursor.execute("""
                UPDATE `emission_data`
                SET `status` = %s
                WHERE `id` = %s
            """, (
                'verified' if conclusion == 'approved' else 'rejected',
                emission_id
            ))

            print(f"已为排放数据 ID:{emission_id} 创建核查记录: {conclusion}")
        except Exception as e:
            print(f"为排放数据 ID:{emission_id} 创建核查记录失败: {str(e)}")

    conn.commit()
    conn.close()

def generate_transactions(enterprise_ids):
    """生成交易记录"""
    print("生成交易记录...")

    if len(enterprise_ids) < 2:
        print("企业数量不足，跳过生成交易记录")
        return

    conn = get_db_connection()
    cursor = conn.cursor()

    # 当前年份
    current_year = datetime.now().year

    # 创建交易记录
    for i in range(random.randint(5, 10)):
        seller_id = random.choice(enterprise_ids)
        buyer_id = random.choice([e for e in enterprise_ids if e != seller_id])
        amount = random.randint(100, 1000)
        price = random.uniform(30, 70)
        total_price = amount * price
        transaction_time = datetime.now() - timedelta(days=random.randint(1, 60))
        status = random.choice(['pending', 'completed', 'cancelled'])

        try:
            cursor.execute("""
                INSERT INTO `transaction` (
                    `seller_id`, `buyer_id`, `amount`, `price`, `total_price`,
                    `transaction_time`, `status`
                )
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """, (
                seller_id,
                buyer_id,
                amount,
                price,
                total_price,
                transaction_time.strftime('%Y-%m-%d %H:%M:%S'),
                status
            ))

            # 如果交易已完成，更新配额
            if status == 'completed':
                # 更新卖家配额
                cursor.execute("""
                    UPDATE `carbon_quota`
                    SET `current_amount` = `current_amount` - %s,
                        `last_updated` = %s
                    WHERE `enterprise_id` = %s AND `year` = %s
                """, (
                    amount,
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    seller_id,
                    current_year
                ))

                # 更新买家配额
                cursor.execute("""
                    UPDATE `carbon_quota`
                    SET `current_amount` = `current_amount` + %s,
                        `last_updated` = %s
                    WHERE `enterprise_id` = %s AND `year` = %s
                """, (
                    amount,
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    buyer_id,
                    current_year
                ))

            print(f"已创建交易记录: 卖家 ID:{seller_id}, 买家 ID:{buyer_id}, 数量 {amount} 吨, 状态 {status}")
        except Exception as e:
            print(f"创建交易记录失败: {str(e)}")

    conn.commit()
    conn.close()

def main():
    """主函数"""
    print("=== 演示数据生成脚本 ===")

    # 生成用户数据
    enterprise_ids, verifier_ids = generate_users()

    # 生成碳配额数据
    generate_carbon_quotas(enterprise_ids)

    # 生成排放数据
    emission_ids = generate_emission_data(enterprise_ids)

    # 生成核查记录
    generate_verification_records(emission_ids, verifier_ids)

    # 生成交易记录
    generate_transactions(enterprise_ids)

    print("演示数据生成完成")

if __name__ == "__main__":
    main()
