<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放管理系统 - 碳交易</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background: linear-gradient(to right, #1B5E20, #388E3C);
            color: white;
            padding: 20px 0;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-name {
            margin-right: 15px;
        }
        .logout-btn {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 30px;
            cursor: pointer;
            text-decoration: none;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .logout-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        nav {
            background: linear-gradient(to right, #2E7D32, #43A047);
            padding: 10px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
        }
        .nav-item {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            margin-right: 5px;
            border-radius: 30px;
            transition: all 0.3s ease;
        }
        .nav-item:hover, .nav-item.active {
            background: linear-gradient(to right, #1B5E20, #2E7D32);
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        .card {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            transform: translateY(-5px);
        }
        .card-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        .btn-primary {
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            color: white;
        }
        .btn-success {
            background: linear-gradient(to right, #2ecc71, #27ae60);
            color: white;
        }
        .btn-danger {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            display: inline-block;
        }
        .status-pending {
            background: linear-gradient(to right, #f39c12, #e67e22);
            color: white;
        }
        .status-completed {
            background: linear-gradient(to right, #2ecc71, #27ae60);
            color: white;
        }
        .status-cancelled {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
        }
        .blockchain-info {
            font-size: 12px;
            color: #7f8c8d;
            margin-top: 5px;
        }
        .blockchain-hash {
            font-family: monospace;
            background: linear-gradient(to right, #f1f8e9, #dcedc8);
            padding: 3px 8px;
            border-radius: 15px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            display: inline-block;
        }
        .transaction-type {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 15px;
            display: inline-block;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .transaction-type-buy {
            background: linear-gradient(to right, #c8e6c9, #81c784);
            color: #1B5E20;
        }
        .transaction-type-sell {
            background: linear-gradient(to right, #ffccbc, #ff8a65);
            color: #bf360c;
        }
        .summary-cards {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        .summary-card {
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 25px;
            text-align: center;
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .summary-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        .summary-value {
            font-size: 36px;
            font-weight: bold;
            margin: 15px 0;
            background: linear-gradient(to right, #4CAF50, #2E7D32);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .summary-label {
            color: #7f8c8d;
            font-size: 14px;
        }
        .filters {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }
        .filter-group {
            display: flex;
            align-items: center;
        }
        .filter-group label {
            margin-right: 5px;
            font-weight: bold;
        }
        .filter-group select {
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 30px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .filter-group select:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 2px 10px rgba(76, 175, 80, 0.2);
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo">碳排放管理系统</div>
            <div class="user-info">
                <span class="user-name">北京碳排放科技有限公司</span>
                <a href="/login" class="logout-btn">退出登录</a>
            </div>
        </div>
    </header>

    <nav>
        <div class="nav-content">
            <a href="/dashboard" class="nav-item">仪表板</a>
            <a href="/emissions" class="nav-item">排放数据</a>
            <a href="/verifications" class="nav-item">核查记录</a>
            <a href="/transactions" class="nav-item active">碳交易</a>
            <a href="/calculator" class="nav-item">碳计算器</a>
            <a href="/predictions" class="nav-item">预测分析</a>
            <a href="/reports" class="nav-item">报告生成</a>
        </div>
    </nav>

    <div class="container">
        <h1>碳交易管理</h1>

        <div class="summary-cards">
            <div class="summary-card">
                <div class="summary-label">当前配额</div>
                <div class="summary-value">3,200</div>
                <div class="summary-label">吨CO2e</div>
            </div>
            <div class="summary-card">
                <div class="summary-label">交易总量</div>
                <div class="summary-value">1,250</div>
                <div class="summary-label">吨CO2e</div>
            </div>
            <div class="summary-card">
                <div class="summary-label">交易总额</div>
                <div class="summary-value">62,500</div>
                <div class="summary-label">元</div>
            </div>
        </div>

        <div class="filters">
            <div class="filter-group">
                <label for="year-filter">年份:</label>
                <select id="year-filter">
                    <option value="all">全部</option>
                    <option value="2023" selected>2023</option>
                    <option value="2022">2022</option>
                    <option value="2021">2021</option>
                </select>
            </div>
            <div class="filter-group">
                <label for="status-filter">状态:</label>
                <select id="status-filter">
                    <option value="all">全部</option>
                    <option value="pending">待确认</option>
                    <option value="completed">已完成</option>
                    <option value="cancelled">已取消</option>
                </select>
            </div>
            <div class="filter-group">
                <label for="type-filter">类型:</label>
                <select id="type-filter">
                    <option value="all">全部</option>
                    <option value="buy">购买</option>
                    <option value="sell">出售</option>
                </select>
            </div>
        </div>

        <div class="card">
            <div class="card-title">
                <span>交易记录</span>
                <a href="/transaction_create" class="btn btn-primary">创建交易</a>
            </div>
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>类型</th>
                        <th>交易方</th>
                        <th>数量</th>
                        <th>价格</th>
                        <th>总价</th>
                        <th>交易时间</th>
                        <th>状态</th>
                        <th>区块链记录</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>2001</td>
                        <td><span class="transaction-type transaction-type-sell">出售</span></td>
                        <td>上海绿色能源有限公司</td>
                        <td>200 吨</td>
                        <td>45 元/吨</td>
                        <td>9,000 元</td>
                        <td>2023-04-15</td>
                        <td><span class="status status-completed">已完成</span></td>
                        <td>
                            <div class="blockchain-info">
                                哈希: <span class="blockchain-hash">0xd5e8...</span>
                                <br>
                                区块: #15836421
                            </div>
                        </td>
                        <td><a href="/transaction_detail?id=2001" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>2002</td>
                        <td><span class="transaction-type transaction-type-buy">购买</span></td>
                        <td>广州环保科技有限公司</td>
                        <td>150 吨</td>
                        <td>50 元/吨</td>
                        <td>7,500 元</td>
                        <td>2023-05-20</td>
                        <td><span class="status status-pending">待确认</span></td>
                        <td>
                            <div class="blockchain-info">
                                哈希: <span class="blockchain-hash">0xe6f9...</span>
                                <br>
                                区块: #15872345
                            </div>
                        </td>
                        <td><a href="/transaction_detail?id=2002" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>2003</td>
                        <td><span class="transaction-type transaction-type-sell">出售</span></td>
                        <td>深圳低碳技术有限公司</td>
                        <td>300 吨</td>
                        <td>48 元/吨</td>
                        <td>14,400 元</td>
                        <td>2023-05-25</td>
                        <td><span class="status status-completed">已完成</span></td>
                        <td>
                            <div class="blockchain-info">
                                哈希: <span class="blockchain-hash">0xf7g0...</span>
                                <br>
                                区块: #15884567
                            </div>
                        </td>
                        <td><a href="/transaction_detail?id=2003" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>2004</td>
                        <td><span class="transaction-type transaction-type-buy">购买</span></td>
                        <td>杭州绿色发展有限公司</td>
                        <td>250 吨</td>
                        <td>52 元/吨</td>
                        <td>13,000 元</td>
                        <td>2023-06-05</td>
                        <td><span class="status status-cancelled">已取消</span></td>
                        <td>
                            <div class="blockchain-info">
                                哈希: <span class="blockchain-hash">0xh8i1...</span>
                                <br>
                                区块: #15896789
                            </div>
                        </td>
                        <td><a href="/transaction_detail?id=2004" class="btn btn-primary">查看</a></td>
                    </tr>
                    <tr>
                        <td>2005</td>
                        <td><span class="transaction-type transaction-type-sell">出售</span></td>
                        <td>南京环保能源有限公司</td>
                        <td>180 吨</td>
                        <td>55 元/吨</td>
                        <td>9,900 元</td>
                        <td>2023-06-10</td>
                        <td><span class="status status-pending">待确认</span></td>
                        <td>
                            <div class="blockchain-info">
                                哈希: <span class="blockchain-hash">0xi9j2...</span>
                                <br>
                                区块: #15908901
                            </div>
                        </td>
                        <td><a href="/transaction_detail?id=2005" class="btn btn-primary">查看</a></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 筛选功能
            const yearFilter = document.getElementById('year-filter');
            const statusFilter = document.getElementById('status-filter');
            const typeFilter = document.getElementById('type-filter');

            // 这里只是演示，实际应用中应该根据筛选条件过滤数据
            yearFilter.addEventListener('change', function() {
                console.log('年份筛选:', this.value);
            });

            statusFilter.addEventListener('change', function() {
                console.log('状态筛选:', this.value);
            });

            typeFilter.addEventListener('change', function() {
                console.log('类型筛选:', this.value);
            });

            // 添加交易趋势图表
            const chartContainer = document.createElement('div');
            chartContainer.style.height = '300px';
            chartContainer.style.marginBottom = '30px';
            chartContainer.innerHTML = '<canvas id="transactionChart"></canvas>';

            // 将图表插入到摘要卡片下方
            const summaryCards = document.querySelector('.summary-cards');
            summaryCards.parentNode.insertBefore(chartContainer, summaryCards.nextSibling);

            // 创建图表
            const ctx = document.getElementById('transactionChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [
                        {
                            label: '购买量 (吨)',
                            data: [150, 200, 180, 250, 300, 280],
                            borderColor: 'rgba(46, 204, 113, 1)',
                            backgroundColor: 'rgba(46, 204, 113, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        },
                        {
                            label: '出售量 (吨)',
                            data: [100, 150, 200, 180, 220, 300],
                            borderColor: 'rgba(231, 76, 60, 1)',
                            backgroundColor: 'rgba(231, 76, 60, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        },
                        {
                            label: '碳价 (元/吨)',
                            data: [45, 47, 48, 50, 52, 55],
                            borderColor: 'rgba(52, 152, 219, 1)',
                            backgroundColor: 'rgba(0, 0, 0, 0)',
                            borderWidth: 2,
                            borderDash: [5, 5],
                            pointRadius: 4,
                            tension: 0.4,
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '交易量 (吨)'
                            }
                        },
                        y1: {
                            position: 'right',
                            beginAtZero: false,
                            title: {
                                display: true,
                                text: '碳价 (元/吨)'
                            },
                            grid: {
                                drawOnChartArea: false
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: '2023年碳交易趋势',
                            font: {
                                size: 16
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.dataset.label || '';
                                    const value = context.raw || 0;

                                    if (context.datasetIndex === 2) {
                                        return `${label}: ${value} 元/吨`;
                                    } else {
                                        return `${label}: ${value} 吨`;
                                    }
                                }
                            }
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>
