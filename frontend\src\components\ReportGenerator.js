import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useAuth } from '../contexts/AuthContext';
import '../styles/ReportGenerator.css';

function ReportGenerator() {
  const [enterprises, setEnterprises] = useState([]);
  const [reports, setReports] = useState([]);
  const [formData, setFormData] = useState({
    enterprise_id: '',
    title: '',
    report_type: 'emission',
    period_start: '',
    period_end: '',
    include_prediction: true
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const { user } = useAuth();

  useEffect(() => {
    fetchEnterprises();
    fetchReports();
    
    // 设置默认日期范围为当年
    const today = new Date();
    const startOfYear = new Date(today.getFullYear(), 0, 1);
    const endOfYear = new Date(today.getFullYear(), 11, 31);
    
    setFormData(prev => ({
      ...prev,
      period_start: startOfYear.toISOString().split('T')[0],
      period_end: endOfYear.toISOString().split('T')[0],
      title: `${today.getFullYear()}年碳排放报告`
    }));
    
    // 如果用户是企业，自动选择自己
    if (user?.role === 'enterprise') {
      setFormData(prev => ({
        ...prev,
        enterprise_id: user.id
      }));
    }
  }, [user]);

  const fetchEnterprises = async () => {
    try {
      const response = await axios.get('/api/auth/enterprises');
      setEnterprises(response.data);
    } catch (err) {
      setError('获取企业列表失败: ' + (err.response?.data?.error || err.message));
    }
  };

  const fetchReports = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/reports');
      setReports(response.data);
      setLoading(false);
    } catch (err) {
      setError('获取报告列表失败: ' + (err.response?.data?.error || err.message));
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const response = await axios.post('/api/reports/generate/emission', formData);
      setSuccess('报告生成成功！');
      fetchReports();
    } catch (err) {
      setError('生成报告失败: ' + (err.response?.data?.error || err.message));
    } finally {
      setLoading(false);
    }
  };

  const downloadReport = async (reportId) => {
    try {
      window.open(`/api/reports/${reportId}/download`, '_blank');
    } catch (err) {
      setError('下载报告失败: ' + (err.response?.data?.error || err.message));
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const getReportTypeName = (type) => {
    switch (type) {
      case 'emission':
        return '排放报告';
      case 'trading':
        return '交易报告';
      case 'compliance':
        return '合规报告';
      default:
        return type;
    }
  };

  return (
    <div className="report-generator">
      <h2>自动化报告生成</h2>
      
      {error && <div className="error-message">{error}</div>}
      {success && <div className="success-message">{success}</div>}
      
      <div className="report-container">
        <div className="report-form">
          <h3>生成新报告</h3>
          <form onSubmit={handleSubmit}>
            <div className="form-group">
              <label>企业</label>
              <select 
                name="enterprise_id" 
                value={formData.enterprise_id}
                onChange={handleChange}
                required
                disabled={user?.role === 'enterprise'}
              >
                <option value="">选择企业</option>
                {enterprises.map(enterprise => (
                  <option key={enterprise.id} value={enterprise.id}>
                    {enterprise.company_name}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="form-group">
              <label>报告标题</label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleChange}
                required
              />
            </div>
            
            <div className="form-group">
              <label>报告类型</label>
              <select 
                name="report_type" 
                value={formData.report_type}
                onChange={handleChange}
                required
              >
                <option value="emission">排放报告</option>
                <option value="trading">交易报告</option>
                <option value="compliance">合规报告</option>
              </select>
            </div>
            
            <div className="form-row">
              <div className="form-group">
                <label>开始日期</label>
                <input
                  type="date"
                  name="period_start"
                  value={formData.period_start}
                  onChange={handleChange}
                  required
                />
              </div>
              
              <div className="form-group">
                <label>结束日期</label>
                <input
                  type="date"
                  name="period_end"
                  value={formData.period_end}
                  onChange={handleChange}
                  required
                />
              </div>
            </div>
            
            <div className="form-group checkbox">
              <input
                type="checkbox"
                name="include_prediction"
                checked={formData.include_prediction}
                onChange={handleChange}
                id="include_prediction"
              />
              <label htmlFor="include_prediction">包含预测数据</label>
            </div>
            
            <button type="submit" className="generate-btn" disabled={loading}>
              {loading ? '生成中...' : '生成报告'}
            </button>
          </form>
        </div>
        
        <div className="report-preview">
          <div className="preview-placeholder">
            <img src="/images/report-preview.png" alt="报告预览" />
            <p>报告将包含图表、数据分析和预测信息</p>
          </div>
        </div>
      </div>
      
      <div className="reports-list">
        <h3>报告列表</h3>
        
        {loading ? (
          <div className="loading">加载中...</div>
        ) : reports.length > 0 ? (
          <table>
            <thead>
              <tr>
                <th>ID</th>
                <th>标题</th>
                <th>企业</th>
                <th>类型</th>
                <th>报告期间</th>
                <th>创建时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              {reports.map(report => (
                <tr key={report.id}>
                  <td>{report.id}</td>
                  <td>{report.title}</td>
                  <td>{report.enterprise_name}</td>
                  <td>{getReportTypeName(report.report_type)}</td>
                  <td>{formatDate(report.period_start)} - {formatDate(report.period_end)}</td>
                  <td>{new Date(report.created_at).toLocaleString()}</td>
                  <td>
                    <button 
                      className="download-btn"
                      onClick={() => downloadReport(report.id)}
                    >
                      下载
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <div className="no-data">暂无报告</div>
        )}
      </div>
      
      <div className="report-features">
        <h3>报告功能</h3>
        <div className="features-grid">
          <div className="feature-card">
            <div className="feature-icon">📊</div>
            <h4>数据可视化</h4>
            <p>自动生成排放趋势图、排放源占比图等多种图表</p>
          </div>
          
          <div className="feature-card">
            <div className="feature-icon">🔮</div>
            <h4>预测分析</h4>
            <p>基于机器学习模型预测未来排放趋势</p>
          </div>
          
          <div className="feature-card">
            <div className="feature-icon">📝</div>
            <h4>多种报告类型</h4>
            <p>支持排放报告、交易报告和合规报告</p>
          </div>
          
          <div className="feature-card">
            <div className="feature-icon">📱</div>
            <h4>多种格式</h4>
            <p>支持PDF、HTML等多种格式导出</p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ReportGenerator;
