<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳排放核查系统 - 区块链配置（演示）</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <style>
        .gradient-custom {
            background: linear-gradient(135deg, #43a047 0%, #1de9b6 100%);
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            margin-bottom: 20px;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }
        .card-header {
            border-radius: 15px 15px 0 0 !important;
            background: linear-gradient(135deg, #43a047 0%, #1de9b6 100%) !important;
            color: white;
            font-weight: 600;
            padding: 1rem 1.5rem;
            border: none;
        }
        .btn-success {
            background: linear-gradient(135deg, #43a047 0%, #1de9b6 100%);
            border: none;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #388e3c 0%, #00bfa5 100%);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }
        .btn-primary {
            background: linear-gradient(135deg, #2196f3 0%, #64b5f6 100%);
            border: none;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }
        .form-control:focus {
            border-color: #43a047;
            box-shadow: 0 0 0 0.25rem rgba(67, 160, 71, 0.25);
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container">
            <a class="navbar-brand" href="/demo"><i class="fas fa-leaf me-2"></i>碳排放核查系统（演示）</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin"><i class="fas fa-tachometer-alt me-1"></i>仪表板</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin_users"><i class="fas fa-users me-1"></i>用户管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin_quotas"><i class="fas fa-chart-pie me-1"></i>配额管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin_penalties"><i class="fas fa-exclamation-triangle me-1"></i>惩罚管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin_reports"><i class="fas fa-file-alt me-1"></i>报告管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin_settings"><i class="fas fa-cog me-1"></i>系统设置</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin_logs"><i class="fas fa-list-alt me-1"></i>日志查看</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/admin_blockchain_config"><i class="fas fa-link me-1"></i>区块链配置</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>{{ current_user.get('username', '管理员') }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-id-card me-1"></i>个人资料</a></li>
                            <li><a class="dropdown-item" href="/admin_settings"><i class="fas fa-cog me-1"></i>系统设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/demo"><i class="fas fa-sign-out-alt me-1"></i>返回演示首页</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 演示提示 -->
    <div class="container mt-3">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>这是<strong>演示页面</strong>，展示了区块链配置的界面和功能。在实际系统中，您可以配置以太坊节点、部署智能合约，并管理用户的区块链地址。
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="container mt-4">
        <!-- 消息提示 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- 成功消息 -->
        {% if success_message %}
            <div class="alert alert-success alert-dismissible fade show">
                {{ success_message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endif %}

        <!-- 错误消息 -->
        {% if error_message %}
            <div class="alert alert-danger alert-dismissible fade show">
                {{ error_message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endif %}

        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-link me-2"></i>区块链配置</h1>
            <div>
                <span class="badge bg-primary">当前时间: <span id="current-time"></span></span>
            </div>
        </div>

        <!-- 区块链配置表单 -->
        <div class="card mb-4">
            <div class="card-header gradient-custom">
                <h5 class="card-title mb-0"><i class="fas fa-cog me-2"></i>以太坊节点配置</h5>
            </div>
            <div class="card-body">
                <form method="post" action="/admin_blockchain_config">
                    <div class="mb-3">
                        <label for="ethereum_node_url" class="form-label">以太坊节点URL</label>
                        <input type="text" class="form-control" id="ethereum_node_url" name="ethereum_node_url" value="{{ config.ETHEREUM_NODE_URL }}" required>
                        <div class="form-text">例如: http://127.0.0.1:8545 (本地Ganache)</div>
                    </div>
                    <div class="mb-3">
                        <label for="contract_address" class="form-label">智能合约地址</label>
                        <input type="text" class="form-control" id="contract_address" name="contract_address" value="{{ config.CONTRACT_ADDRESS }}">
                        <div class="form-text">如果留空，系统将尝试部署新的合约</div>
                    </div>
                    <div class="mb-3">
                        <label for="admin_address" class="form-label">管理员地址</label>
                        <input type="text" class="form-control" id="admin_address" name="admin_address" value="{{ config.ADMIN_ADDRESS }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="admin_private_key" class="form-label">管理员私钥</label>
                        <input type="password" class="form-control" id="admin_private_key" name="admin_private_key" value="{{ config.ADMIN_PRIVATE_KEY }}" required>
                        <div class="form-text">注意: 私钥仅用于签名交易，不会被发送到区块链网络</div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-primary" id="save-config"><i class="fas fa-save me-1"></i>保存配置</button>
                        <button type="button" class="btn btn-success" id="deploy-contract"><i class="fas fa-upload me-1"></i>部署合约</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 企业和核查机构配置 -->
        <div class="card mb-4">
            <div class="card-header gradient-custom">
                <h5 class="card-title mb-0"><i class="fas fa-users-cog me-2"></i>用户区块链配置</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>企业配置</h5>
                        <div class="mb-3">
                            <label class="form-label">企业1地址</label>
                            <input type="text" class="form-control" value="{{ config.ENTERPRISE_1_ADDRESS }}" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">企业2地址</label>
                            <input type="text" class="form-control" value="{{ config.ENTERPRISE_2_ADDRESS }}" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">企业3地址</label>
                            <input type="text" class="form-control" value="{{ config.ENTERPRISE_3_ADDRESS }}" readonly>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5>核查机构配置</h5>
                        <div class="mb-3">
                            <label class="form-label">核查机构1地址</label>
                            <input type="text" class="form-control" value="{{ config.VERIFIER_1_ADDRESS }}" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">核查机构2地址</label>
                            <input type="text" class="form-control" value="{{ config.VERIFIER_2_ADDRESS }}" readonly>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Ganache账户列表 -->
        <div class="card mb-4">
            <div class="card-header gradient-custom">
                <h5 class="card-title mb-0"><i class="fas fa-wallet me-2"></i>可用账户列表</h5>
            </div>
            <div class="card-body">
                {% if ganache_accounts %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>地址</th>
                                    <th>余额 (ETH)</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for account in ganache_accounts %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td><code>{{ account.address }}</code></td>
                                        <td>{{ account.balance }}</td>
                                        <td>
                                            <button class="btn btn-sm btn-primary copy-address" data-address="{{ account.address }}">
                                                <i class="fas fa-copy"></i> 复制地址
                                            </button>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>无法获取账户列表，请确保以太坊节点正在运行
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- 区块链状态 -->
        <div class="card mb-4">
            <div class="card-header gradient-custom">
                <h5 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i>区块链状态</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">连接状态</label>
                            <div>
                                {% if ganache_accounts %}
                                    <span class="badge bg-success"><i class="fas fa-check-circle me-1"></i>已连接</span>
                                {% else %}
                                    <span class="badge bg-danger"><i class="fas fa-times-circle me-1"></i>未连接</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">合约状态</label>
                            <div>
                                {% if config.CONTRACT_ADDRESS %}
                                    <span class="badge bg-success"><i class="fas fa-check-circle me-1"></i>已部署</span>
                                {% else %}
                                    <span class="badge bg-warning"><i class="fas fa-exclamation-circle me-1"></i>未部署</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">网络类型</label>
                            <div>
                                <span class="badge bg-info"><i class="fas fa-network-wired me-1"></i>本地开发网络</span>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">模拟模式</label>
                            <div>
                                <span class="badge bg-secondary"><i class="fas fa-toggle-on me-1"></i>已启用</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">© 2025 碳排放核查系统 | 基于区块链技术</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 显示当前时间
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleString('zh-CN');
        }
        updateTime();
        setInterval(updateTime, 1000);

        // 复制地址到剪贴板
        document.querySelectorAll('.copy-address').forEach(button => {
            button.addEventListener('click', function() {
                const address = this.getAttribute('data-address');
                navigator.clipboard.writeText(address).then(() => {
                    this.innerHTML = '<i class="fas fa-check"></i> 已复制';
                    setTimeout(() => {
                        this.innerHTML = '<i class="fas fa-copy"></i> 复制地址';
                    }, 2000);
                });
            });
        });

        // 保存配置按钮
        document.getElementById('save-config').addEventListener('click', function() {
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>保存中...';

            setTimeout(() => {
                alert('配置已成功保存（演示）');
                this.disabled = false;
                this.innerHTML = '<i class="fas fa-save me-1"></i>保存配置';
            }, 1500);
        });

        // 部署合约按钮
        document.getElementById('deploy-contract').addEventListener('click', function() {
            if (confirm('确定要部署新的智能合约吗？这将覆盖现有的合约地址。')) {
                this.disabled = true;
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>部署中...';

                setTimeout(() => {
                    alert('合约已成功部署（演示）\n合约地址: 0x5FbDB2315678afecb367f032d93F642f64180aa3');
                    document.getElementById('contract_address').value = '0x5FbDB2315678afecb367f032d93F642f64180aa3';
                    this.disabled = false;
                    this.innerHTML = '<i class="fas fa-upload me-1"></i>部署合约';
                }, 2000);
            }
        });
    </script>
</body>
</html>
