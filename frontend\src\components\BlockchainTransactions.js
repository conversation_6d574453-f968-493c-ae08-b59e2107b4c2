import React, { useState, useEffect } from 'react';
import axios from 'axios';
import BlockchainInfo from './BlockchainInfo';
import '../styles/BlockchainTransactions.css';

/**
 * 区块链交易列表组件
 * 用于展示与特定实体相关的区块链交易
 */
const BlockchainTransactions = ({ entityId, entityType, transactionType }) => {
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    fetchTransactions();
  }, [entityId, entityType, transactionType, page]);

  const fetchTransactions = async () => {
    if (!entityId) return;

    setLoading(true);
    try {
      // 构建API请求参数
      const params = {
        page,
        limit: 10,
        entity_id: entityId
      };

      if (entityType) params.entity_type = entityType;
      if (transactionType) params.transaction_type = transactionType;

      const response = await axios.get('/api/blockchain/transactions', {
        params,
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });

      if (page === 1) {
        setTransactions(response.data.transactions);
      } else {
        setTransactions(prev => [...prev, ...response.data.transactions]);
      }

      setHasMore(response.data.transactions.length === 10);
      setError('');
    } catch (err) {
      setError('获取区块链交易记录失败');
      console.error('获取区块链交易记录失败:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadMore = () => {
    if (!loading && hasMore) {
      setPage(prev => prev + 1);
    }
  };

  // 根据交易类型获取图标和标签
  const getTransactionTypeInfo = (type) => {
    switch (type) {
      case 'emission_data':
        return { type: 'emission', label: '排放数据' };
      case 'verification':
        return { type: 'verification', label: '核查结果' };
      case 'carbon_transaction':
        return { type: 'transaction', label: '碳交易' };
      case 'penalty':
        return { type: 'penalty', label: '惩罚记录' };
      default:
        return { type: 'unknown', label: '未知类型' };
    }
  };

  return (
    <div className="blockchain-transactions">
      <h3 className="blockchain-transactions-title">区块链交易记录</h3>
      
      {error && <div className="blockchain-transactions-error">{error}</div>}
      
      {transactions.length === 0 && !loading ? (
        <div className="blockchain-transactions-empty">
          <i className="fas fa-file-contract"></i>
          <p>暂无区块链交易记录</p>
        </div>
      ) : (
        <div className="blockchain-transactions-list">
          {transactions.map(transaction => {
            const typeInfo = getTransactionTypeInfo(transaction.transaction_type);
            return (
              <BlockchainInfo
                key={transaction.tx_hash}
                transactionHash={transaction.tx_hash}
                blockNumber={transaction.block_number}
                timestamp={transaction.timestamp}
                status={transaction.status}
                type={typeInfo.type}
              />
            );
          })}
          
          {loading && (
            <div className="blockchain-transactions-loading">
              <i className="fas fa-spinner fa-spin"></i>
              <span>加载中...</span>
            </div>
          )}
          
          {hasMore && !loading && (
            <div className="blockchain-transactions-load-more">
              <button onClick={loadMore}>加载更多</button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default BlockchainTransactions;
