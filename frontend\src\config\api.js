import axios from 'axios';

// 创建 axios 实例
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response) {
      // 处理 401 未授权错误
      if (error.response.status === 401) {
        localStorage.removeItem('token');
        window.location.href = '/login';
      }
      // 显示错误消息
      const errorMsg = error.response.data.error || '请求失败';
      // 使用统一的消息提示组件
      if (window.showNotification) {
        window.showNotification(errorMsg, 'error');
      }
    }
    return Promise.reject(error);
  }
);

// API 端点
export const endpoints = {
  auth: {
    login: '/auth/login',
    register: '/auth/register',
    forgotPassword: '/auth/forgot-password',
    resetPassword: '/auth/reset-password',
    status: '/auth/status'
  },
  emissions: {
    list: '/emission-data',
    create: '/emission-data',
    update: (id) => `/emission-data/${id}`,
    delete: (id) => `/emission-data/${id}`,
    verify: (id) => `/verification/${id}`
  },
  dashboard: {
    stats: '/dashboard/stats',
    recentActivities: '/dashboard/recent-activities'
  },
  system: {
    config: '/system/config',
    updateConfig: '/system/config'
  }
};

// 导出API方法
export default {
  // 认证相关
  login: (credentials) => api.post(endpoints.auth.login, credentials),
  register: (userData) => api.post(endpoints.auth.register, userData),
  
  // 排放数据相关
  getEmissions: (params) => api.get(endpoints.emissions.list, { params }),
  createEmission: (data, files) => {
    const formData = new FormData();
    Object.keys(data).forEach(key => formData.append(key, data[key]));
    if (files && files.proof_file) {
      formData.append('proof_file', files.proof_file);
    }
    return api.post(endpoints.emissions.create, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  },
  updateEmission: (id, data) => api.put(endpoints.emissions.update(id), data),
  deleteEmission: (id) => api.delete(endpoints.emissions.delete(id)),
  
  // 验证相关
  verifyEmission: (id, verificationData) => api.post(endpoints.emissions.verify(id), verificationData),
  
  // 仪表板相关
  getDashboardStats: () => api.get(endpoints.dashboard.stats),
  getRecentActivities: () => api.get(endpoints.dashboard.recentActivities),
  
  // 系统配置相关
  getSystemConfig: () => api.get(endpoints.system.config),
  updateSystemConfig: (configData) => api.put(endpoints.system.updateConfig, configData)
};
