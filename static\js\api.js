/**
 * API接口封装
 */

const API = {
    /**
     * 发送请求
     * @param {string} url - 请求URL
     * @param {string} method - 请求方法
     * @param {object} data - 请求数据
     * @returns {Promise} - 返回Promise对象
     */
    request: async function(url, method = 'GET', data = null) {
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'same-origin'
        };

        if (data && (method === 'POST' || method === 'PUT')) {
            options.body = JSON.stringify(data);
        }

        try {
            const response = await fetch(url, options);
            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.message || '请求失败');
            }

            return result;
        } catch (error) {
            console.error('API请求错误:', error);
            throw error;
        }
    },

    /**
     * 用户登录
     * @param {string} username - 用户名
     * @param {string} password - 密码
     * @returns {Promise} - 返回Promise对象
     */
    login: function(username, password) {
        return this.request('/api/auth/login', 'POST', { username, password });
    },

    /**
     * 用户注册
     * @param {object} userData - 用户数据
     * @returns {Promise} - 返回Promise对象
     */
    register: function(userData) {
        return this.request('/api/auth/register', 'POST', userData);
    },

    /**
     * 用户登出
     * @returns {Promise} - 返回Promise对象
     */
    logout: function() {
        return this.request('/api/auth/logout', 'POST');
    },

    /**
     * 获取当前用户信息
     * @returns {Promise} - 返回Promise对象
     */
    getCurrentUser: function() {
        return this.request('/api/auth/current_user');
    },

    /**
     * 获取用户列表
     * @param {object} params - 查询参数
     * @returns {Promise} - 返回Promise对象
     */
    getUsers: function(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        return this.request(`/api/user/list?${queryString}`);
    },

    /**
     * 获取用户详情
     * @param {number} userId - 用户ID
     * @returns {Promise} - 返回Promise对象
     */
    getUserDetail: function(userId) {
        return this.request(`/api/user/detail/${userId}`);
    },

    /**
     * 添加用户
     * @param {object} userData - 用户数据
     * @returns {Promise} - 返回Promise对象
     */
    addUser: function(userData) {
        return this.request('/api/user/add', 'POST', userData);
    },

    /**
     * 更新用户
     * @param {number} userId - 用户ID
     * @param {object} userData - 用户数据
     * @returns {Promise} - 返回Promise对象
     */
    updateUser: function(userId, userData) {
        return this.request(`/api/user/update/${userId}`, 'PUT', userData);
    },

    /**
     * 删除用户
     * @param {number} userId - 用户ID
     * @returns {Promise} - 返回Promise对象
     */
    deleteUser: function(userId) {
        return this.request(`/api/user/delete/${userId}`, 'DELETE');
    },

    /**
     * 获取排放数据列表
     * @param {object} params - 查询参数
     * @returns {Promise} - 返回Promise对象
     */
    getEmissions: function(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        return this.request(`/api/emission/list?${queryString}`);
    },

    /**
     * 获取排放数据详情
     * @param {number} emissionId - 排放数据ID
     * @returns {Promise} - 返回Promise对象
     */
    getEmissionDetail: function(emissionId) {
        return this.request(`/api/emission/detail/${emissionId}`);
    },

    /**
     * 添加排放数据
     * @param {object} emissionData - 排放数据
     * @returns {Promise} - 返回Promise对象
     */
    addEmission: function(emissionData) {
        return this.request('/api/emission/add', 'POST', emissionData);
    },

    /**
     * 更新排放数据
     * @param {number} emissionId - 排放数据ID
     * @param {object} emissionData - 排放数据
     * @returns {Promise} - 返回Promise对象
     */
    updateEmission: function(emissionId, emissionData) {
        return this.request(`/api/emission/update/${emissionId}`, 'PUT', emissionData);
    },

    /**
     * 提交排放数据到区块链
     * @param {number} emissionId - 排放数据ID
     * @returns {Promise} - 返回Promise对象
     */
    submitEmission: function(emissionId) {
        return this.request(`/api/emission/submit/${emissionId}`, 'POST');
    },

    /**
     * 删除排放数据
     * @param {number} emissionId - 排放数据ID
     * @returns {Promise} - 返回Promise对象
     */
    deleteEmission: function(emissionId) {
        return this.request(`/api/emission/delete/${emissionId}`, 'DELETE');
    },

    /**
     * 获取排放数据统计信息
     * @returns {Promise} - 返回Promise对象
     */
    getEmissionStatistics: function() {
        return this.request('/api/emission/statistics');
    },

    /**
     * 获取核查记录列表
     * @param {object} params - 查询参数
     * @returns {Promise} - 返回Promise对象
     */
    getVerifications: function(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        return this.request(`/api/verification/list?${queryString}`);
    },

    /**
     * 获取待核查的排放数据列表
     * @returns {Promise} - 返回Promise对象
     */
    getPendingVerifications: function() {
        return this.request('/api/verification/pending');
    },

    /**
     * 获取核查记录详情
     * @param {number} verificationId - 核查记录ID
     * @returns {Promise} - 返回Promise对象
     */
    getVerificationDetail: function(verificationId) {
        return this.request(`/api/verification/detail/${verificationId}`);
    },

    /**
     * 核查排放数据
     * @param {number} emissionId - 排放数据ID
     * @param {object} verificationData - 核查数据
     * @returns {Promise} - 返回Promise对象
     */
    verifyEmission: function(emissionId, verificationData) {
        return this.request(`/api/verification/verify/${emissionId}`, 'POST', verificationData);
    },

    /**
     * 获取核查统计信息
     * @returns {Promise} - 返回Promise对象
     */
    getVerificationStatistics: function() {
        return this.request('/api/verification/statistics');
    },

    /**
     * 获取交易列表
     * @param {object} params - 查询参数
     * @returns {Promise} - 返回Promise对象
     */
    getTransactions: function(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        return this.request(`/api/transaction/list?${queryString}`);
    },

    /**
     * 获取交易详情
     * @param {number} transactionId - 交易ID
     * @returns {Promise} - 返回Promise对象
     */
    getTransactionDetail: function(transactionId) {
        return this.request(`/api/transaction/detail/${transactionId}`);
    },

    /**
     * 创建交易
     * @param {object} transactionData - 交易数据
     * @returns {Promise} - 返回Promise对象
     */
    createTransaction: function(transactionData) {
        return this.request('/api/transaction/create', 'POST', transactionData);
    },

    /**
     * 确认交易
     * @param {number} transactionId - 交易ID
     * @returns {Promise} - 返回Promise对象
     */
    confirmTransaction: function(transactionId) {
        return this.request(`/api/transaction/confirm/${transactionId}`, 'POST');
    },

    /**
     * 取消交易
     * @param {number} transactionId - 交易ID
     * @returns {Promise} - 返回Promise对象
     */
    cancelTransaction: function(transactionId) {
        return this.request(`/api/transaction/cancel/${transactionId}`, 'POST');
    },

    /**
     * 获取交易统计信息
     * @returns {Promise} - 返回Promise对象
     */
    getTransactionStatistics: function() {
        return this.request('/api/transaction/statistics');
    },

    /**
     * 获取配额列表
     * @param {object} params - 查询参数
     * @returns {Promise} - 返回Promise对象
     */
    getQuotas: function(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        return this.request(`/api/quota/list?${queryString}`);
    },

    /**
     * 获取配额详情
     * @param {number} quotaId - 配额ID
     * @returns {Promise} - 返回Promise对象
     */
    getQuotaDetail: function(quotaId) {
        return this.request(`/api/quota/detail/${quotaId}`);
    },

    /**
     * 分配配额
     * @param {object} quotaData - 配额数据
     * @returns {Promise} - 返回Promise对象
     */
    allocateQuota: function(quotaData) {
        return this.request('/api/quota/allocate', 'POST', quotaData);
    },

    /**
     * 获取配额统计信息
     * @returns {Promise} - 返回Promise对象
     */
    getQuotaStatistics: function() {
        return this.request('/api/quota/statistics');
    },

    /**
     * 获取惩罚列表
     * @param {object} params - 查询参数
     * @returns {Promise} - 返回Promise对象
     */
    getPenalties: function(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        return this.request(`/api/penalty/list?${queryString}`);
    },

    /**
     * 获取惩罚详情
     * @param {number} penaltyId - 惩罚ID
     * @returns {Promise} - 返回Promise对象
     */
    getPenaltyDetail: function(penaltyId) {
        return this.request(`/api/penalty/detail/${penaltyId}`);
    },

    /**
     * 添加惩罚
     * @param {object} penaltyData - 惩罚数据
     * @returns {Promise} - 返回Promise对象
     */
    addPenalty: function(penaltyData) {
        return this.request('/api/penalty/add', 'POST', penaltyData);
    },

    /**
     * 更新惩罚
     * @param {number} penaltyId - 惩罚ID
     * @param {object} penaltyData - 惩罚数据
     * @returns {Promise} - 返回Promise对象
     */
    updatePenalty: function(penaltyId, penaltyData) {
        return this.request(`/api/penalty/update/${penaltyId}`, 'PUT', penaltyData);
    },

    /**
     * 获取惩罚统计信息
     * @returns {Promise} - 返回Promise对象
     */
    getPenaltyStatistics: function() {
        return this.request('/api/penalty/statistics');
    },

    /**
     * 获取报告列表
     * @param {object} params - 查询参数
     * @returns {Promise} - 返回Promise对象
     */
    getReports: function(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        return this.request(`/api/report/list?${queryString}`);
    },

    /**
     * 获取报告详情
     * @param {number} reportId - 报告ID
     * @returns {Promise} - 返回Promise对象
     */
    getReportDetail: function(reportId) {
        return this.request(`/api/report/detail/${reportId}`);
    },

    /**
     * 生成报告
     * @param {object} reportData - 报告数据
     * @returns {Promise} - 返回Promise对象
     */
    generateReport: function(reportData) {
        return this.request('/api/report/generate', 'POST', reportData);
    },

    /**
     * 下载报告
     * @param {number} reportId - 报告ID
     * @returns {Promise} - 返回Promise对象
     */
    downloadReport: function(reportId) {
        window.location.href = `/api/report/download/${reportId}`;
        return Promise.resolve();
    },

    /**
     * 获取区块链配置
     * @returns {Promise} - 返回Promise对象
     */
    getBlockchainConfig: function() {
        return this.request('/api/blockchain/config');
    },

    /**
     * 更新区块链配置
     * @param {object} configData - 配置数据
     * @returns {Promise} - 返回Promise对象
     */
    updateBlockchainConfig: function(configData) {
        return this.request('/api/blockchain/config', 'POST', configData);
    },

    /**
     * 部署智能合约
     * @returns {Promise} - 返回Promise对象
     */
    deployContract: function() {
        return this.request('/api/blockchain/deploy', 'POST');
    },

    /**
     * 获取区块链账户列表
     * @returns {Promise} - 返回Promise对象
     */
    getBlockchainAccounts: function() {
        return this.request('/api/blockchain/accounts');
    },

    /**
     * 获取区块链状态
     * @returns {Promise} - 返回Promise对象
     */
    getBlockchainStatus: function() {
        return this.request('/api/blockchain/status');
    },

    /**
     * 授权用户
     * @param {number} userId - 用户ID
     * @returns {Promise} - 返回Promise对象
     */
    authorizeUser: function(userId) {
        return this.request(`/api/blockchain/authorize/${userId}`, 'POST');
    },

    /**
     * 撤销用户授权
     * @param {number} userId - 用户ID
     * @returns {Promise} - 返回Promise对象
     */
    revokeUser: function(userId) {
        return this.request(`/api/blockchain/revoke/${userId}`, 'POST');
    },

    /**
     * 获取区块链用户列表
     * @returns {Promise} - 返回Promise对象
     */
    getBlockchainUsers: function() {
        return this.request('/api/blockchain/users');
    },

    /**
     * 测试区块链连接
     * @returns {Promise} - 返回Promise对象
     */
    testBlockchainConnection: function() {
        return this.request('/api/blockchain/test-connection', 'GET');
    },

    /**
     * 部署智能合约
     * @returns {Promise} - 返回Promise对象
     */
    deployContract: function() {
        return this.request('/api/blockchain/deploy-contract', 'POST');
    },

    /**
     * 注册区块链地址
     * @param {object} addressData - 地址数据
     * @returns {Promise} - 返回Promise对象
     */
    registerBlockchainAddress: function(addressData) {
        return this.request('/api/blockchain/register-address', 'POST', addressData);
    },

    /**
     * 获取区块链账户余额
     * @param {string} address - 区块链地址
     * @returns {Promise} - 返回Promise对象
     */
    getBlockchainBalance: function(address) {
        return this.request(`/api/blockchain/balance?address=${address}`);
    },

    /**
     * 检查授权状态
     * @returns {Promise} - 返回Promise对象
     */
    checkAuthorization: function() {
        return this.request('/api/blockchain/check-authorization');
    }
};

// 导出API对象
window.API = API;
