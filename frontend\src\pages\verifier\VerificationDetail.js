import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';
import BlockchainInfo from '../../components/BlockchainInfo';
import BlockchainVerification from '../../components/BlockchainVerification';
import '../../styles/VerificationDetail.css';

/**
 * 核查详情页面
 * 展示核查记录的详细信息，包括区块链信息
 */
const VerificationDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [verification, setVerification] = useState(null);
  const [emission, setEmission] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchVerificationData();
  }, [id]);

  const fetchVerificationData = async () => {
    setLoading(true);
    try {
      // 获取核查记录
      const verificationResponse = await axios.get(`/api/verifications/${id}`, {
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      
      setVerification(verificationResponse.data.verification);
      
      // 获取对应的排放数据
      const emissionId = verificationResponse.data.verification.emission_data_id;
      const emissionResponse = await axios.get(`/api/emissions/${emissionId}`, {
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      
      setEmission(emissionResponse.data.emission);
      
      setError('');
    } catch (err) {
      console.error('获取核查记录失败:', err);
      setError('获取核查记录失败');
    } finally {
      setLoading(false);
    }
  };

  // 格式化日期
  const formatDate = (dateString) => {
    if (!dateString) return '';
    
    try {
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch (e) {
      return dateString;
    }
  };

  // 获取结论标签和颜色
  const getConclusionLabel = (conclusion) => {
    switch (conclusion) {
      case 'approved':
        return { label: '通过', color: 'green' };
      case 'rejected':
        return { label: '拒绝', color: 'red' };
      default:
        return { label: conclusion, color: 'gray' };
    }
  };

  if (loading) {
    return (
      <div className="verification-detail-loading">
        <i className="fas fa-spinner fa-spin"></i>
        <span>加载中...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="verification-detail-error">
        <i className="fas fa-exclamation-triangle"></i>
        <span>{error}</span>
        <button onClick={() => navigate('/verifications')} className="btn-back">
          返回列表
        </button>
      </div>
    );
  }

  if (!verification) {
    return (
      <div className="verification-detail-not-found">
        <i className="fas fa-search"></i>
        <span>未找到核查记录</span>
        <button onClick={() => navigate('/verifications')} className="btn-back">
          返回列表
        </button>
      </div>
    );
  }

  const conclusionInfo = getConclusionLabel(verification.conclusion);

  return (
    <div className="verification-detail">
      <div className="detail-header">
        <h2>核查记录详情</h2>
        <button onClick={() => navigate('/verifications')} className="btn-back">
          <i className="fas fa-arrow-left"></i> 返回列表
        </button>
      </div>

      {/* 基本信息 */}
      <div className="detail-section">
        <h3 className="section-title">基本信息</h3>
        <div className="detail-grid">
          <div className="detail-item">
            <span className="detail-label">核查ID</span>
            <span className="detail-value">{verification.id}</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">核查机构</span>
            <span className="detail-value">{verification.verifier_name}</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">核查时间</span>
            <span className="detail-value">{formatDate(verification.verification_time)}</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">核查结论</span>
            <span className="detail-value">
              <span className="conclusion-badge" style={{ backgroundColor: conclusionInfo.color }}>
                {conclusionInfo.label}
              </span>
            </span>
          </div>
          <div className="detail-item detail-item-full">
            <span className="detail-label">核查意见</span>
            <span className="detail-value detail-comments">{verification.comments}</span>
          </div>
        </div>
      </div>

      {/* 区块链信息 */}
      {verification.blockchain_hash && (
        <div className="detail-section">
          <h3 className="section-title">区块链信息</h3>
          <BlockchainInfo
            transactionHash={verification.blockchain_hash}
            blockNumber={verification.blockchain_block}
            timestamp={verification.verification_time}
            status="confirmed"
            type="verification"
          />
          <BlockchainVerification
            dataId={verification.id}
            dataType="verification"
          />
        </div>
      )}

      {/* 排放数据信息 */}
      {emission && (
        <div className="detail-section">
          <h3 className="section-title">排放数据信息</h3>
          <div className="detail-grid">
            <div className="detail-item">
              <span className="detail-label">排放数据ID</span>
              <span className="detail-value">{emission.id}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">企业</span>
              <span className="detail-value">{emission.enterprise_name}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">排放源</span>
              <span className="detail-value">{emission.emission_source}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">排放量</span>
              <span className="detail-value">{emission.emission_amount} {emission.emission_unit || '吨CO2e'}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">计算方法</span>
              <span className="detail-value">{emission.calculation_method}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">提交时间</span>
              <span className="detail-value">{formatDate(emission.submission_time)}</span>
            </div>
          </div>
          
          <div className="emission-actions">
            <button 
              onClick={() => navigate(`/emissions/${emission.id}`)} 
              className="btn-view-emission"
            >
              查看排放数据详情
            </button>
          </div>
        </div>
      )}
      
      {/* 操作按钮 */}
      <div className="detail-actions">
        <button 
          onClick={() => navigate('/verifications')} 
          className="btn-secondary"
        >
          返回列表
        </button>
        <button 
          onClick={() => window.open(`/reports/verification/${verification.id}`, '_blank')} 
          className="btn-primary"
        >
          生成核查报告
        </button>
      </div>
    </div>
  );
};

export default VerificationDetail;
