"""
简单的登录系统
"""

from flask import Flask, render_template, request, redirect, url_for, session, flash
import os

app = Flask(__name__)
app.secret_key = 'carbon-emission-system-secret-key-2025'

# 测试用户
USERS = {
    'admin': {
        'password': 'admin123',
        'role': 'admin',
        'company_name': '系统管理员'
    },
    'enterprise1': {
        'password': 'enterprise123',
        'role': 'enterprise',
        'company_name': '北京碳排放科技有限公司'
    },
    'verifier1': {
        'password': 'verifier123',
        'role': 'verifier',
        'company_name': '北京碳核查认证中心'
    }
}

@app.route('/')
def index():
    """首页"""
    return redirect('/login')

@app.route('/login', methods=['GET', 'POST'])
def login():
    """登录页面"""
    error_message = None
    
    # 如果用户已登录，根据角色重定向到相应页面
    if 'user_id' in session:
        role = session.get('role')
        if role == 'admin':
            return redirect('/admin')
        elif role == 'enterprise':
            return redirect('/enterprise')
        elif role == 'verifier':
            return redirect('/verifier')
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        print(f"尝试登录: 用户名={username}, 密码={'*' * len(password)}")
        
        # 验证用户名和密码
        if username in USERS and USERS[username]['password'] == password:
            # 登录成功
            session.clear()
            session['user_id'] = username
            session['username'] = username
            session['role'] = USERS[username]['role']
            session['company_name'] = USERS[username]['company_name']
            session['logged_in'] = True
            session.modified = True
            
            print(f"登录成功: 用户名={username}, 角色={USERS[username]['role']}")
            
            # 根据用户角色重定向到相应页面
            if USERS[username]['role'] == 'admin':
                return redirect('/admin')
            elif USERS[username]['role'] == 'enterprise':
                return redirect('/enterprise')
            elif USERS[username]['role'] == 'verifier':
                return redirect('/verifier')
        else:
            # 登录失败
            error_message = "用户名或密码不正确"
            print(f"登录失败: 用户名={username}")
    
    return render_template('simple_login.html', error_message=error_message)

@app.route('/admin')
def admin():
    """管理员页面"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return redirect('/login')
    
    return f"""
    <h1>管理员页面</h1>
    <p>欢迎, {session.get('username')}!</p>
    <p>角色: {session.get('role')}</p>
    <p>公司: {session.get('company_name')}</p>
    <a href="/logout">退出登录</a>
    """

@app.route('/enterprise')
def enterprise():
    """企业页面"""
    if 'user_id' not in session or session.get('role') != 'enterprise':
        return redirect('/login')
    
    return f"""
    <h1>企业页面</h1>
    <p>欢迎, {session.get('username')}!</p>
    <p>角色: {session.get('role')}</p>
    <p>公司: {session.get('company_name')}</p>
    <a href="/logout">退出登录</a>
    """

@app.route('/verifier')
def verifier():
    """核查机构页面"""
    if 'user_id' not in session or session.get('role') != 'verifier':
        return redirect('/login')
    
    return f"""
    <h1>核查机构页面</h1>
    <p>欢迎, {session.get('username')}!</p>
    <p>角色: {session.get('role')}</p>
    <p>公司: {session.get('company_name')}</p>
    <a href="/logout">退出登录</a>
    """

@app.route('/logout')
def logout():
    """退出登录"""
    session.clear()
    return redirect('/login')

if __name__ == '__main__':
    app.run(debug=True, port=5001)
